// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

enum TypeSession {
  PLAY
  TIPS
}

model User {
  id         Int          @id @default(autoincrement())
  isActive   Boolean      @default(true)
  name       String?
  email      String       @unique
  password   String
  registration String?
  role_id    Int?
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @default(now()) @updatedAt
  Role       Role?        @relation(fields: [role_id], references: [id])
  user_token User_token[]
  Disputes   Disputes[]
  tribute    Tribute[]
  chargeback_dispute chargeback_dispute[]

  @@index([role_id])
  @@map("Users")
}

model User_token {
  id           Int      @id @default(autoincrement())
  token        String
  expires_date DateTime
  createdAt    DateTime @default(now())
  userId       Int
  User         User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([token])
}

model Role {
  id          Int          @id @default(autoincrement())
  name        String       @unique
  description String
  user        User[]
  Permission  Permission[]
}

model Permission {
  id          Int    @id @default(autoincrement())
  name        String @unique
  description String
  route       String?
  icon        String?
  role        Role[]
}

model Player {
  id                    Int                     @id @default(autoincrement())
  name                  String?
  cpf                   String?                 @unique
  nickname              String
  email                 String
  password              String?
  phone                 String?
  bio                   String?                 @db.VarChar(500)
  birthdate             DateTime?
  country               String?
  cep                   String?
  state                 String?
  city                  String?
  district              String?
  address               String?
  complement            String?
  number                String?
  cover_picture         String?
  avatar                String?
  stream_link           String?
  push_token            String?
  isPro                 Boolean                 @default(value: false)
  isEmailVerified       Boolean                 @default(value: false)
  ProMigrationDate      DateTime?
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  deletedAt             DateTime?
  nickname_twitch       String?
  subscribe_twitch      BigInt?                 @default(0)
  total_views_twitch    BigInt?                 @default(0)
  favoritePlayers       Favorite_Player[]
  badges                Badge[]
  wallet                Wallet?
  sessions              Session[]
  games                 Game[]
  player_stats          Player_stats[]
  player_token          Player_token[]
  Rating                Rating[]
  Seat                  Seat[]
  Linked_account        Linked_account[]
  Payment_info          Payment_info[]
  Account_statement     Account_statement[]
  Federated_credentials Federated_credentials[]
  Mentoring_games       Mentoring_games[]
  Player_notification   Player_notification[]
  Withdraw_request      Withdraw_request[]
  Session_metrics       Session_metrics[]
  Disputes              Disputes[]
  Cancelation_motive    Cancelation_motive[]
  Message_chat          Message_chat[]
  promoted_player       Promoted_player[]
  Teams                 Teams[]
  Members_team          Members_team[]
  Device                Device[]
  
  @@index([total_views_twitch])
  @@index([cpf])
  @@index([nickname])
  @@index([isPro])
}

model Player_token {
  id           Int      @id @default(autoincrement())
  token        String
  code         String?
  expires_date DateTime
  createdAt    DateTime @default(now())
  player_id    Int
  Player       Player   @relation(fields: [player_id], references: [id])

  @@index([token])
  @@index([code])
  @@index([player_id])
}

model Device {
  id          Int      @id @default(autoincrement())
  device_id   Int
  deviceToken String
  type        String
  active      Boolean  @default(true)
  player_id   Int
  language_id Int
  timezone    String   @default("America/Sao_Paulo")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  Player   Player   @relation(fields: [player_id], references: [id])
  Language Language @relation(fields: [language_id], references: [id])

  @@index([device_id])
  @@index([player_id])
  @@index([language_id])
  @@map("Devices")
}

model Language {
  id        Int      @id @default(autoincrement())
  code      String   @unique // Ex: "pt-BR", "en-US"
  name      String   // Ex: "Português (Brasil)", "English (US)"
  Devices   Device[] // Relacionamento com Device

  @@map("Languages")
}

model Federated_credentials {
  id          Int      @id @default(autoincrement())
  provider    String
  provider_id String
  createdAt   DateTime @default(now())
  player_id   Int
  Player      Player   @relation(fields: [player_id], references: [id])

  @@index([provider_id])
  @@index([player_id])
}

model Mentoring_games {
  id        Int      @id @default(autoincrement())
  active    Boolean  @default(value: true)
  createdAt DateTime @default(now())
  player_id Int
  game_id   Int
  Player    Player   @relation(fields: [player_id], references: [id])
  Game      Game     @relation(fields: [game_id], references: [id])

  @@index([game_id])
  @@index([player_id])
  @@index([active])
}

model Type {
  id   Int    @id @default(autoincrement())
  name String @unique
  Game Game[]
}

model Game {
  id              Int               @id @default(autoincrement())
  name            String            @unique
  isActive        Boolean           @default(value: true)
  banner          String
  logo            String
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  players         Player[]
  game_stats      Game_stats[]
  Session         Session[]
  Mentoring_games Mentoring_games[]
  Type            Type[]
  Teams           Teams[]
}

model Game_stats {
  id           Int            @id @default(autoincrement())
  name         String
  slug         String?
  game_id      Int
  Game         Game           @relation(fields: [game_id], references: [id])
  Player_stats Player_stats[]

  @@index([game_id])
}

model Player_stats {
  id            Int        @id @default(autoincrement())
  value         Float
  player_id     Int
  game_stats_id Int
  Player        Player     @relation(fields: [player_id], references: [id])
  Game_stats    Game_stats @relation(fields: [game_stats_id], references: [id])

  @@index([game_stats_id])
  @@index([player_id])
}

model Favorite_Player {
  id                 Int    @id @default(autoincrement())
  favorite_player_id Int
  player_id          Int
  Player             Player @relation(fields: [player_id], references: [id])

  @@index([player_id])
  @@index([favorite_player_id])
}

model Badge {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  description String?
  icon        String
  players     Player[]
}

model Wallet {
  id                Int                 @id @default(autoincrement())
  total             Float               @default(value: 0)
  player_id         Int                 @unique
  Player            Player              @relation(fields: [player_id], references: [id])
  Account_statement Account_statement[]

  @@index([player_id])
}

model Session {
  id                 Int                  @id @default(autoincrement())
  start_time         DateTime
  end_time           DateTime
  price              Float
  seats              Int
  communication_link String?
  isActive           Boolean              @default(value: true)
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  deletedAt          DateTime?
  canceledAt         DateTime?
  player_id          Int
  game_id            Int
  session_base_id    Int?
  platform_id        Int?
  input_id           Int?
  type_session       TypeSession          @default(PLAY)
  Game               Game                 @relation(fields: [game_id], references: [id])
  session_base       Session?             @relation("SessionToSession", fields: [session_base_id], references: [id])
  Platform           Platform?            @relation(fields: [platform_id], references: [id])
  Input              Input?               @relation(fields: [input_id], references: [id])
  Player             Player               @relation(fields: [player_id], references: [id])
  rating             Rating[]
  players_seats      Seat[]
  Account_statement  Account_statement[]
  Recurring_sessions Recurring_sessions[]
  Session            Session[]            @relation("SessionToSession")
  promoted_session   Promoted_session[]
  Session_metrics    Session_metrics[]
  Disputes           Disputes[]
  Cancelation_motive Cancelation_motive[]
  Message_chat       Message_chat[]
  Invite_Discord     Invite_Discord[]

  @@index([player_id])
  @@index([game_id])
  @@index([session_base_id])
  @@index([platform_id])
  @@index([input_id])
  @@index([isActive])
}

model Seat {
  id                      Int            @id @default(autoincrement())
  isConfirmed             Boolean        @default(value: false)
  isConfirmedAfterSession Boolean        @default(value: false)
  isFinished              Boolean        @default(value: false)
  hasDispute              Boolean        @default(value: false)
  hasChargeback           Boolean        @default(value: false)
  player_id               Int
  session_id              Int
  Session                 Session        @relation(fields: [session_id], references: [id])
  Player                  Player         @relation(fields: [player_id], references: [id])
  Jobs                    Jobs[]
  Payment_info            Payment_info[]

  @@index([session_id])
  @@index([player_id])
}

model Tag {
  id     Int      @id @default(autoincrement())
  name   String   @unique
  Rating Rating[]
}

model Rating {
  id          Int      @id @default(autoincrement())
  points      Int
  description String?  @db.VarChar(300)
  createdAt   DateTime @default(now())
  isAnonymous Boolean  @default(value: false)
  session_id  Int
  player_id   Int
  Session     Session  @relation(fields: [session_id], references: [id])
  Player      Player   @relation(fields: [player_id], references: [id])
  Tag         Tag[]

  @@index([player_id])
  @@index([session_id])
}

model Knowbie_points {
  id          Int      @id @default(autoincrement())
  quantity    Int
  price_cents Int
  name        String   @unique
  description String
  createdAt   DateTime @default(now())
}

model Game_provider {
  id             Int              @id @default(autoincrement())
  name           String
  Linked_account Linked_account[]
}

model Linked_account {
  id              Int           @id @default(autoincrement())
  account_id      String
  game_account_id Int
  player_id       Int
  Player          Player        @relation(fields: [player_id], references: [id])
  game_account    Game_provider @relation(fields: [game_account_id], references: [id])

  @@index([player_id])
  @@index([game_account_id])
  @@index([account_id])
}

model Highlights {
  id    Int    @id @default(autoincrement())
  image String
}

model Payment_info {
  id                    Int                 @id @default(autoincrement())
  status                String              @default(value: "created")
  payment_from_wallet   Float?
  payment_from_external Float?
  external_payment_id   String?
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt
  player_id             Int
  seat_id               Int?
  payment_url           String?
  service_type          String?
  Seat                  Seat?               @relation(fields: [seat_id], references: [id])
  Player                Player              @relation(fields: [player_id], references: [id])
  Account_statement     Account_statement[]
  Refund                Refund?


  @@index([status])
  @@index([external_payment_id])
  @@index([player_id])
  @@index([seat_id])
}

model Account_statement {
  id               Int                @id @default(autoincrement())
  title            String
  description      String?
  quantity         Float
  type             String
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  wallet_id        Int
  session_id       Int?
  buyer_id         Int?
  payment_info_id  Int?
  Wallet           Wallet             @relation(fields: [wallet_id], references: [id])
  Session          Session?           @relation(fields: [session_id], references: [id])
  Player           Player?            @relation(fields: [buyer_id], references: [id])
  Payment_info     Payment_info?      @relation(fields: [payment_info_id], references: [id])
  Withdraw_request Withdraw_request[]

  @@index([type])
  @@index([wallet_id])
  @@index([session_id])
  @@index([buyer_id])
  @@index([payment_info_id])
}

model Jobs {
  id               Int       @id @default(autoincrement())
  active           Boolean   @default(value: true)
  expiresDate      DateTime
  deactivationDate DateTime?
  createdAt        DateTime  @default(now())
  seat_id          Int
  Seat             Seat      @relation(fields: [seat_id], references: [id])

  @@index([seat_id])
  @@index([active])
}

model Recurring_sessions {
  id          Int       @id @default(autoincrement())
  startDate   DateTime
  nextGenDate DateTime?
  untilDate   DateTime?
  pattern     String
  session_id  Int
  session     Session   @relation(fields: [session_id], references: [id])

  @@index([session_id])
}

model Notification {
  id                  Int                   @id @default(autoincrement())
  title               String
  message             String
  image               String?
  primaryButton       String?
  secondaryButton     String?
  primaryAction       String?
  secondaryAction     String?
  createdAt           DateTime              @default(now())
  Player_notification Player_notification[]
}

model Player_notification {
  id              Int          @id @default(autoincrement())
  isRead          Boolean      @default(value: false)
  isClicked       Boolean      @default(value: false)
  createdAt       DateTime     @default(now())
  player_id       Int
  notification_id Int
  Player          Player       @relation(fields: [player_id], references: [id])
  Notification    Notification @relation(fields: [notification_id], references: [id])

  @@index([player_id])
  @@index([notification_id])
}

model Withdraw_request {
  id                    Int                @id @default(autoincrement())
  account_statement_id  Int?
  player_id             Int
  retry_id              Int?               @unique
  status                String
  payment_external_code String
  batch_number          String?            @default("")
  launch_number         String?            @default("")
  payment_type          String
  amount                String
  bank_account_type     String?
  bank_account_number   String?
  bank_account_agency   String?
  pix_key               String?
  paymentDay            DateTime           @default(now())
  createdAt             DateTime           @default(now())
  payer                 String?            @default("")
  receiver_name         String             @default("")
  receiver              String?            @default("")
  json_request          String?            @default("")
  json_response         String?            @default("")
  Account_statement     Account_statement? @relation(fields: [account_statement_id], references: [id])
  Player                Player             @relation(fields: [player_id], references: [id])
  Next_Retry            Withdraw_request?  @relation("RetryRelation", fields: [retry_id], references: [id])
  Previous_Retry        Withdraw_request?  @relation("RetryRelation")
  Tribute               Tribute[]

  @@index([status])
  @@index([retry_id])
  @@index([player_id])
  @@index([account_statement_id])
}

model Promoted_session {
  id                 Int      @id @default(autoincrement())
  until              DateTime
  days_to_expire     Int
  price              Float
  expected_reach_min Int
  expected_reach_max Int
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  active             Boolean  @default(value: true)
  session_id         Int
  price_per_day      Float
  payment_type       String
  payment_info_id    Int?
  Session            Session  @relation(fields: [session_id], references: [id])

  @@index([session_id])
  @@index([active])
}

model Promoted_player {
  id                 Int      @id @default(autoincrement())
  until              DateTime
  days_to_expire     Int
  price              Float
  expected_reach_min Int
  expected_reach_max Int
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  active             Boolean  @default(value: true)
  player_id          Int
  price_per_day      Float
  payment_type       String
  payment_info_id    Int?
  Player             Player  @relation(fields: [player_id], references: [id])

  @@index([player_id])
  @@index([active])
}

model Promoted_session_values {
  id             Int      @id @default(autoincrement())
  knowbie_points Int
  reach_min      Int
  reach_max      Int
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

model Session_metrics {
  id         Int      @id @default(autoincrement())
  reach      Int?
  views      Int?
  clicks     Int?
  checkouts  Int?
  date       DateTime @default(now())
  player_id  Int
  session_id Int
  Player     Player   @relation(fields: [player_id], references: [id])
  Session    Session  @relation(fields: [session_id], references: [id], onDelete: Cascade)

  @@index([session_id])
  @@index([player_id])
}

model Disputes {
  id                    Int       @id @default(autoincrement())
  status                String
  decision              String?
  resolution_date       DateTime?
  player_description    String?
  player_attachment     String?
  mentor_description    String?
  mentor_attachment     String?
  mentor_response_date  DateTime?
  moderator_description String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  winner_id             Int?
  player_id             Int
  session_id            Int
  user_id               Int?
  Player                Player    @relation(fields: [player_id], references: [id])
  Session               Session   @relation(fields: [session_id], references: [id])
  Moderator             User?     @relation(fields: [user_id], references: [id])

  @@index([session_id])
  @@index([user_id])
  @@index([player_id])
  @@index([winner_id])
  @@index([status])
}

model Invite_Discord {
   id                         Int    @id @default(autoincrement())
   link_discord               String
   session_id                 Int
   channel_id                 String
   createdAt                  DateTime  @default(now())
   Session                    Session   @relation(fields: [session_id], references: [id])

  @@index([session_id])
}

model Chargeback {
  id                          Int    @id @default(autoincrement())
  external_id                 String
  external_payment_id         BigInt
  amount                      Int
  coverage_applied            Boolean
  coverage_elegible           Boolean
  documentation_required      Boolean
  documentation_status        String
  date_documentation_deadline DateTime
  date_created                DateTime
  date_last_updated           DateTime
  live_mode                   Boolean

  chargeback_history          chargeback_history[]
  chargeback_documents        chargeback_documents[]
  chargeback_dispute          chargeback_dispute[]
}

model chargeback_documents{
  id                          Int    @id @default(autoincrement())
  type                        String
  url                         String
  description                 String
  hash                        String
  chargeback_id               Int
  Chargeback                  Chargeback   @relation(fields: [chargeback_id], references: [id])

  @@index([chargeback_id])
}
model chargeback_dispute{
  id                          Int    @id @default(autoincrement())
  mentor_response             String?
  mentor_attachment           String?
  mentor_date_response        DateTime?
  chargeback_decision_user    String?
  chargeback_decision_mp      String?
  user_response               String?
  user_date_response          DateTime?
  createdAt                   DateTime  @default(now())
  updatedAt                   DateTime  @updatedAt
  user_id                     Int?
  chargeback_id               Int
  mentor_requested_response   Boolean?
  Chargeback                  Chargeback   @relation(fields: [chargeback_id], references: [id])
  Moderator                   User?        @relation(fields: [user_id], references: [id])

  @@index([chargeback_id])
  @@index([user_id])
}

model chargeback_history {
  id                          Int    @id @default(autoincrement())
  external_id                 String
  external_payment_id         BigInt
  amount                      Int
  coverage_applied            Boolean
  coverage_elegible           Boolean
  documentation_required      Boolean
  documentation_status        String
  date_documentation_deadline DateTime
  date_created                DateTime
  date_last_updated           DateTime
  live_mode                   Boolean
  chargeback_id               Int
  Chargeback                  Chargeback   @relation(fields: [chargeback_id], references: [id])

  @@index([chargeback_id])
}

model Platform {
  id          Int       @id @default(autoincrement())
  description String    @unique
  Session     Session[]
}

model Input {
  id          Int       @id @default(autoincrement())
  description String    @unique
  Session     Session[]
}

model Cancelation_option {
  id                 Int                  @id @default(autoincrement())
  description        String               @unique
  Cancelation_motive Cancelation_motive[]
}

model Cancelation_motive {
  id                    Int                @id @default(autoincrement())
  player_id             Int
  session_id            Int
  cancelation_option_id Int
  motive_field          String?
  Player                Player             @relation(fields: [player_id], references: [id])
  Session               Session            @relation(fields: [session_id], references: [id])
  Cancelation_option    Cancelation_option @relation(fields: [cancelation_option_id], references: [id])

  @@index([player_id])
  @@index([session_id])
}

model Teams {
  id            Int                @id @default(autoincrement())
  isActive      Boolean            @default(true)
  name          String
  player_id     Int
  description   String?
  seats         Int
  game_id       Int
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  Game          Game               @relation(fields: [game_id], references: [id])
  Player        Player             @relation(fields: [player_id], references: [id])
  Members_team  Members_team[]


  @@index([game_id])
  @@index([player_id])
}

model Members_team {
  id           Int                @id @default(autoincrement())
  isActive     Boolean            @default(true)
  player_id    Int
  team_id      Int
  isAdmin      Boolean?           @default(false)
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt
  Player       Player             @relation(fields: [player_id], references: [id])
  Teams        Teams              @relation(fields: [team_id], references: [id])


  @@index([player_id])
  @@index([team_id])
}

model Message_chat {
  id           Int      @id @default(autoincrement())
  message      String
  data_message DateTime @default(now())
  session_id   Int
  player_id    Int
  createdAt    DateTime @default(now())
  Player       Player   @relation(fields: [player_id], references: [id])
  Session      Session  @relation(fields: [session_id], references: [id])

  @@index([session_id])
  @@index([player_id])
}

model Type_tribute_value {
  id          Int       @id @default(autoincrement())
  description String    @unique
  Tribute     Tribute[]
}

model Tribute {
  id                    Int                @id @default(autoincrement())
  description           String
  value                 Float              @default(value: 0)
  status_tribute        Boolean
  type_tribute_value_id Int
  createdAt             DateTime           @default(now())
  updatedAt             DateTime?          @updatedAt
  User                  User?              @relation(fields: [user_id], references: [id])
  user_id               Int?
  Type_tribute_value    Type_tribute_value @relation(fields: [type_tribute_value_id], references: [id])
  Withdraw_request      Withdraw_request[]

  @@unique([description, status_tribute], name: "description_status")
  @@index([user_id])
  @@index([type_tribute_value_id])
}

model Holiday {
  id          Int       @id @default(autoincrement())
  date        DateTime
  description String    @unique
  createdAt   DateTime  @default(now())
  updatedAt   DateTime? @updatedAt
}

model Refund {
  id                     Int          @id
  payment_id             Float
  amount                 Float
  status                 String
  unique_sequence_number String?
  refund_mode            String?
  createdAt              DateTime
  payment_info_id        Int          @unique
  Payment_info           Payment_info @relation(fields: [payment_info_id], references: [id])

  @@index([payment_id])
  @@index([payment_info_id])
}
