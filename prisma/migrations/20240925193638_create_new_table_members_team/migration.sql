-- CreateTable
CREATE TABLE "Members_team" (
    "id" SERIAL NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "player_id" INTEGER NOT NULL,
    "team_id" INTEGER NOT NULL,
    "isAdmin" BOOLEAN DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Members_team_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Members_team_player_id_idx" ON "Members_team"("player_id");

-- CreateIndex
CREATE INDEX "Members_team_team_id_idx" ON "Members_team"("team_id");

-- AddForeignKey
ALTER TABLE "Members_team" ADD CONSTRAINT "Members_team_player_id_fkey" FOREIGN KEY ("player_id") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Members_team" ADD CONSTRAINT "Members_team_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "Teams"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
