-- CreateTable
CREATE TABLE "Teams" (
    "id" SERIAL NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "name" TEXT NOT NULL,
    "player_id" INTEGER NOT NULL,
    "description" TEXT,
    "seats" INTEGER NOT NULL,
    "game_id" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Teams_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Teams_game_id_idx" ON "Teams"("game_id");

-- CreateIndex
CREATE INDEX "Teams_player_id_idx" ON "Teams"("player_id");

-- AddForeignKey
ALTER TABLE "Teams" ADD CONSTRAINT "Teams_game_id_fkey" FOREIGN KEY ("game_id") REFERENCES "Game"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Teams" ADD CONSTRAINT "Teams_player_id_fkey" FOREIGN KEY ("player_id") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
