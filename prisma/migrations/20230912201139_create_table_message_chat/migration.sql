-- CreateTable
CREATE TABLE "Message_chat" (
    "id" SERIAL NOT NULL,
    "message" TEXT NOT NULL,
    "data_message" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "session_id" INTEGER NOT NULL,
    "player_id" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Message_chat_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Message_chat" ADD CONSTRAINT "Message_chat_player_id_fkey" FOREIGN KEY ("player_id") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignK<PERSON>
ALTER TABLE "Message_chat" ADD CONSTRAINT "Message_chat_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "Session"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
