-- CreateTable
CREATE TABLE "Refund" (
    "id" INTEGER NOT NULL,
    "payment_id" DOUBLE PRECISION NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "status" TEXT NOT NULL,
    "unique_sequence_number" TEXT NOT NULL,
    "refund_mode" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL,
    "payment_info_id" INTEGER NOT NULL,

    CONSTRAINT "Refund_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Refund_payment_info_id_key" ON "Refund"("payment_info_id");

-- AddForeignKey
ALTER TABLE "Refund" ADD CONSTRAINT "Refund_payment_info_id_fkey" FOREIGN KEY ("payment_info_id") REFERENCES "Payment_info"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
