-- CreateTable
CREATE TABLE "_TributeToWithdraw_request" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_TributeToWithdraw_request_AB_unique" ON "_TributeToWithdraw_request"("A", "B");

-- CreateIndex
CREATE INDEX "_TributeToWithdraw_request_B_index" ON "_TributeToWithdraw_request"("B");

-- AddForeignKey
ALTER TABLE "_TributeToWithdraw_request" ADD CONSTRAINT "_TributeToWithdraw_request_A_fkey" FOREIGN KEY ("A") REFERENCES "Tribute"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_TributeToWithdraw_request" ADD CONSTRAINT "_TributeToWithdraw_request_B_fkey" FOREIGN KEY ("B") REFERENCES "Withdraw_request"("id") ON DELETE CASCADE ON UPDATE CASCADE;
