/*
  Warnings:

  - A unique constraint covering the columns `[retry_id]` on the table `Withdraw_request` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Withdraw_request" ADD COLUMN     "retry_id" INTEGER;

-- CreateIndex
CREATE UNIQUE INDEX "Withdraw_request_retry_id_key" ON "Withdraw_request"("retry_id");

-- CreateIndex
CREATE INDEX "Withdraw_request_retry_id_idx" ON "Withdraw_request"("retry_id");

-- CreateIndex
CREATE INDEX "Withdraw_request_player_id_idx" ON "Withdraw_request"("player_id");

-- CreateIndex
CREATE INDEX "Withdraw_request_account_statement_id_idx" ON "Withdraw_request"("account_statement_id");

-- AddForeignKey
ALTER TABLE "Withdraw_request" ADD CONSTRAINT "Withdraw_request_retry_id_fkey" FOREIGN KEY ("retry_id") REFERENCES "Withdraw_request"("id") ON DELETE SET NULL ON UPDATE CASCADE;
