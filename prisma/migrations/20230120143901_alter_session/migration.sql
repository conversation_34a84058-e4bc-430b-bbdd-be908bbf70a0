/*
  Warnings:

  - You are about to drop the column `communication_link` on the `Player` table. All the data in the column will be lost.
  - You are about to drop the `InputToPlatform` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `input_id` to the `Session` table without a default value. This is not possible if the table is not empty.
  - Added the required column `platform_id` to the `Session` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "InputToPlatform" DROP CONSTRAINT "InputToPlatform_game_id_fkey";

-- DropForeignKey
ALTER TABLE "InputToPlatform" DROP CONSTRAINT "InputToPlatform_input_id_fkey";

-- DropForeignKey
ALTER TABLE "InputToPlatform" DROP CONSTRAINT "InputToPlatform_platform_id_fkey";

-- DropForeignKey
ALTER TABLE "InputToPlatform" DROP CONSTRAINT "InputToPlatform_player_id_fkey";

-- AlterTable
ALTER TABLE "Player" DROP COLUMN "communication_link";

-- AlterTable
ALTER TABLE "Session" ADD COLUMN     "communication_link" TEXT,
ADD COLUMN     "input_id" INTEGER NOT NULL,
ADD COLUMN     "platform_id" INTEGER NOT NULL;

-- DropTable
DROP TABLE "InputToPlatform";

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_platform_id_fkey" FOREIGN KEY ("platform_id") REFERENCES "Platform"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_input_id_fkey" FOREIGN KEY ("input_id") REFERENCES "Input"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
