-- CreateTable
CREATE TABLE "Cancelation_option" (
    "id" SERIAL NOT NULL,
    "description" TEXT NOT NULL,

    CONSTRAINT "Cancelation_option_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Cancelation_motive" (
    "id" SERIAL NOT NULL,
    "player_id" INTEGER NOT NULL,
    "session_id" INTEGER NOT NULL,
    "cancelation_option_id" INTEGER NOT NULL,
    "motive_field" TEXT,

    CONSTRAINT "Cancelation_motive_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Cancelation_motive" ADD CONSTRAINT "Cancelation_motive_player_id_fkey" FOREIGN KEY ("player_id") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Cancelation_motive" ADD CONSTRAINT "Cancelation_motive_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "Session"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddFore<PERSON>Key
ALTER TABLE "Cancelation_motive" ADD CONSTRAINT "Cancelation_motive_cancelation_option_id_fkey" FOREIGN KEY ("cancelation_option_id") REFERENCES "Cancelation_option"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
