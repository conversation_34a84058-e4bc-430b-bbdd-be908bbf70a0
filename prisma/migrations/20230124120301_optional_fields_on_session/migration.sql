-- DropForeign<PERSON>ey
ALTER TABLE "Session" DROP CONSTRAINT "Session_input_id_fkey";

-- DropFore<PERSON>Key
ALTER TABLE "Session" DROP CONSTRAINT "Session_platform_id_fkey";

-- AlterTable
ALTER TABLE "Session" ALTER COLUMN "input_id" DROP NOT NULL,
ALTER COLUMN "platform_id" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_platform_id_fkey" FOREIGN KEY ("platform_id") REFERENCES "Platform"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_input_id_fkey" FOREIGN KEY ("input_id") REFERENCES "Input"("id") ON DELETE SET NULL ON UPDATE CASCADE;
