-- CreateTable
CREATE TABLE "Devices" (
    "id" SERIAL NOT NULL,
    "device_id" INTEGER NOT NULL,
    "deviceToken" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "player_id" INTEGER NOT NULL,
    "language_id" INTEGER NOT NULL,
    "timezone" TEXT NOT NULL DEFAULT 'America/Sao_Paulo',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Devices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Languages" (
    "id" SERIAL NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "Languages_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Devices_device_id_idx" ON "Devices"("device_id");

-- CreateIndex
CREATE INDEX "Devices_player_id_idx" ON "Devices"("player_id");

-- CreateIndex
CREATE INDEX "Devices_language_id_idx" ON "Devices"("language_id");

-- CreateIndex
CREATE UNIQUE INDEX "Languages_code_key" ON "Languages"("code");

-- AddForeignKey
ALTER TABLE "Devices" ADD CONSTRAINT "Devices_player_id_fkey" FOREIGN KEY ("player_id") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Devices" ADD CONSTRAINT "Devices_language_id_fkey" FOREIGN KEY ("language_id") REFERENCES "Languages"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
