/*
  Warnings:

  - Changed the type of `documentation_required` on the `Chargeback` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `date_documentation_deadline` on the `Chargeback` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `documentation_required` on the `chargeback_history` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `date_documentation_deadline` on the `chargeback_history` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- AlterTable
ALTER TABLE "Chargeback" ALTER COLUMN "external_id" SET DATA TYPE TEXT,
DROP COLUMN "documentation_required",
ADD COLUMN     "documentation_required" BOOLEAN NOT NULL,
DROP COLUMN "date_documentation_deadline",
ADD COLUMN     "date_documentation_deadline" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "chargeback_history" ALTER COLUMN "external_id" SET DATA TYPE TEXT,
DROP COLUMN "documentation_required",
ADD COLUMN     "documentation_required" BOOLEAN NOT NULL,
DROP COLUMN "date_documentation_deadline",
ADD COLUMN     "date_documentation_deadline" TIMESTAMP(3) NOT NULL;
