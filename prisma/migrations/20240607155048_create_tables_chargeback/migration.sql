-- CreateTable
CREATE TABLE "Chargeback" (
    "id" SERIAL NOT NULL,
    "external_id" INTEGER NOT NULL,
    "external_payment_id" INTEGER NOT NULL,
    "amount" INTEGER NOT NULL,
    "coverage_applied" BOOLEAN NOT NULL,
    "coverage_elegible" BOOLEAN NOT NULL,
    "documentation_required" TEXT NOT NULL,
    "documentation_status" TEXT NOT NULL,
    "date_documentation_deadline" TEXT NOT NULL,
    "date_created" TIMESTAMP(3) NOT NULL,
    "date_last_updated" TIMESTAMP(3) NOT NULL,
    "live_mode" BOOLEAN NOT NULL,

    CONSTRAINT "Chargeback_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "chargeback_documents" (
    "id" SERIAL NOT NULL,
    "type" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "chargeback_id" INTEGER NOT NULL,

    CONSTRAINT "chargeback_documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "chargeback_history" (
    "id" SERIAL NOT NULL,
    "external_id" INTEGER NOT NULL,
    "external_payment_id" INTEGER NOT NULL,
    "amount" INTEGER NOT NULL,
    "coverage_applied" BOOLEAN NOT NULL,
    "coverage_elegible" BOOLEAN NOT NULL,
    "documentation_required" TEXT NOT NULL,
    "documentation_status" TEXT NOT NULL,
    "date_documentation_deadline" TEXT NOT NULL,
    "date_created" TIMESTAMP(3) NOT NULL,
    "date_last_updated" TIMESTAMP(3) NOT NULL,
    "live_mode" BOOLEAN NOT NULL,
    "chargeback_id" INTEGER NOT NULL,

    CONSTRAINT "chargeback_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "chargeback_documents_chargeback_id_idx" ON "chargeback_documents"("chargeback_id");

-- CreateIndex
CREATE INDEX "chargeback_history_chargeback_id_idx" ON "chargeback_history"("chargeback_id");

-- AddForeignKey
ALTER TABLE "chargeback_documents" ADD CONSTRAINT "chargeback_documents_chargeback_id_fkey" FOREIGN KEY ("chargeback_id") REFERENCES "Chargeback"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "chargeback_history" ADD CONSTRAINT "chargeback_history_chargeback_id_fkey" FOREIGN KEY ("chargeback_id") REFERENCES "Chargeback"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
