-- CreateTable
CREATE TABLE "chargeback_dispute" (
    "id" SERIAL NOT NULL,
    "mentor_response" TEXT,
    "mentor_date_response" TIMESTAMP(3),
    "chargeback_decision_user" TEXT,
    "chargeback_decision_mp" TEXT,
    "user_response" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "user_id" INTEGER,
    "chargeback_id" INTEGER NOT NULL,

    CONSTRAINT "chargeback_dispute_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "chargeback_dispute_chargeback_id_idx" ON "chargeback_dispute"("chargeback_id");

-- CreateIndex
CREATE INDEX "chargeback_dispute_user_id_idx" ON "chargeback_dispute"("user_id");

-- AddForeignKey
ALTER TABLE "chargeback_dispute" ADD CONSTRAINT "chargeback_dispute_chargeback_id_fkey" FOREIGN KEY ("chargeback_id") REFERENCES "Chargeback"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignK<PERSON>
ALTER TABLE "chargeback_dispute" ADD CONSTRAINT "chargeback_dispute_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "Users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
