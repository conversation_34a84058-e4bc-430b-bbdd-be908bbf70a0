-- CreateTable
CREATE TABLE "Promoted_player" (
    "id" SERIAL NOT NULL,
    "until" TIMESTAMP(3) NOT NULL,
    "days_to_expire" INTEGER NOT NULL,
    "price" INTEGER NOT NULL,
    "expected_reach_min" INTEGER NOT NULL,
    "expected_reach_max" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "player_id" INTEGER NOT NULL,
    "price_per_day" INTEGER NOT NULL,
    "payment_type" TEXT NOT NULL,

    CONSTRAINT "Promoted_player_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Promoted_player_player_id_idx" ON "Promoted_player"("player_id");

-- CreateIndex
CREATE INDEX "Promoted_player_active_idx" ON "Promoted_player"("active");

-- AddForeignKey
ALTER TABLE "Promoted_player" ADD CONSTRAINT "Promoted_player_player_id_fkey" FOREIGN KEY ("player_id") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
