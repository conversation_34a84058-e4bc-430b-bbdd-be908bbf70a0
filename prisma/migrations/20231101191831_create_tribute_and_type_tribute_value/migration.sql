-- CreateTable
CREATE TABLE "Type_tribute_value" (
    "id" SERIAL NOT NULL,
    "description" TEXT NOT NULL,

    CONSTRAINT "Type_tribute_value_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Tribute" (
    "id" SERIAL NOT NULL,
    "description" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "status_tribute" BOOLEAN NOT NULL,
    "type_tribute_value_id" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Tribute_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Tribute" ADD CONSTRAINT "Tribute_type_tribute_value_id_fkey" FOREIGN KEY ("type_tribute_value_id") REFERENCES "Type_tribute_value"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
