-- CreateTable
CREATE TABLE "Platform" (
    "id" SERIAL NOT NULL,
    "description" TEXT NOT NULL,

    CONSTRAINT "Platform_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Input" (
    "id" SERIAL NOT NULL,
    "description" TEXT NOT NULL,

    CONSTRAINT "Input_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InputToPlatform" (
    "id" SERIAL NOT NULL,
    "player_id" INTEGER NOT NULL,
    "platform_id" INTEGER NOT NULL,
    "input_id" INTEGER NOT NULL,
    "game_id" INTEGER NOT NULL,

    CONSTRAINT "InputToPlatform_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "InputToPlatform" ADD CONSTRAINT "InputToPlatform_player_id_fkey" FOREIGN KEY ("player_id") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InputToPlatform" ADD CONSTRAINT "InputToPlatform_game_id_fkey" FOREIGN KEY ("game_id") REFERENCES "Game"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InputToPlatform" ADD CONSTRAINT "InputToPlatform_platform_id_fkey" FOREIGN KEY ("platform_id") REFERENCES "Platform"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InputToPlatform" ADD CONSTRAINT "InputToPlatform_input_id_fkey" FOREIGN KEY ("input_id") REFERENCES "Input"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
