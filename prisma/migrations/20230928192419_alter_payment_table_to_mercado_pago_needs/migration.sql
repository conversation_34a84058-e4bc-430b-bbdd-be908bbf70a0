/*
  Warnings:

  - You are about to drop the column `customer_id` on the `Payment_info` table. All the data in the column will be lost.
  - You are about to drop the column `subaccount_id` on the `Payment_info` table. All the data in the column will be lost.
  - You are about to drop the `Payment_method` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `payment_options_id` to the `Payment_info` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Payment_method" DROP CONSTRAINT "Payment_method_payment_info_id_fkey";

-- AlterTable
ALTER TABLE "Payment_info" DROP COLUMN "customer_id",
DROP COLUMN "subaccount_id",
ADD COLUMN     "external_payment_id" INTEGER,
ADD COLUMN     "payment_from_external" INTEGER,
ADD COLUMN     "payment_from_wallet" INTEGER,
ADD COLUMN     "payment_options_id" INTEGER NOT NULL,
ADD COLUMN     "status" TEXT NOT NULL DEFAULT E'created';

-- DropTable
DROP TABLE "Payment_method";

-- CreateTable
CREATE TABLE "Payment_options" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Payment_options_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Payment_info" ADD CONSTRAINT "Payment_info_payment_options_id_fkey" FOREIGN KEY ("payment_options_id") REFERENCES "Payment_options"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
