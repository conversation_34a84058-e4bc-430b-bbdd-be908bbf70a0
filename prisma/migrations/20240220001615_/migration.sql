/*
  Warnings:

  - A unique constraint covering the columns `[cpf]` on the table `Player` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE INDEX "Account_statement_type_idx" ON "Account_statement"("type");

-- CreateIndex
CREATE INDEX "Account_statement_wallet_id_idx" ON "Account_statement"("wallet_id");

-- CreateIndex
CREATE INDEX "Account_statement_session_id_idx" ON "Account_statement"("session_id");

-- CreateIndex
CREATE INDEX "Account_statement_buyer_id_idx" ON "Account_statement"("buyer_id");

-- CreateIndex
CREATE INDEX "Account_statement_payment_info_id_idx" ON "Account_statement"("payment_info_id");

-- CreateIndex
CREATE INDEX "Cancelation_motive_player_id_idx" ON "Cancelation_motive"("player_id");

-- CreateIndex
CREATE INDEX "Cancelation_motive_session_id_idx" ON "Cancelation_motive"("session_id");

-- CreateIndex
CREATE INDEX "Disputes_session_id_idx" ON "Disputes"("session_id");

-- CreateIndex
CREATE INDEX "Disputes_user_id_idx" ON "Disputes"("user_id");

-- CreateIndex
CREATE INDEX "Disputes_player_id_idx" ON "Disputes"("player_id");

-- CreateIndex
CREATE INDEX "Disputes_winner_id_idx" ON "Disputes"("winner_id");

-- CreateIndex
CREATE INDEX "Disputes_status_idx" ON "Disputes"("status");

-- CreateIndex
CREATE INDEX "Favorite_Player_player_id_idx" ON "Favorite_Player"("player_id");

-- CreateIndex
CREATE INDEX "Favorite_Player_favorite_player_id_idx" ON "Favorite_Player"("favorite_player_id");

-- CreateIndex
CREATE INDEX "Federated_credentials_provider_id_idx" ON "Federated_credentials"("provider_id");

-- CreateIndex
CREATE INDEX "Federated_credentials_player_id_idx" ON "Federated_credentials"("player_id");

-- CreateIndex
CREATE INDEX "Game_stats_game_id_idx" ON "Game_stats"("game_id");

-- CreateIndex
CREATE INDEX "Jobs_seat_id_idx" ON "Jobs"("seat_id");

-- CreateIndex
CREATE INDEX "Jobs_active_idx" ON "Jobs"("active");

-- CreateIndex
CREATE INDEX "Linked_account_player_id_idx" ON "Linked_account"("player_id");

-- CreateIndex
CREATE INDEX "Linked_account_game_account_id_idx" ON "Linked_account"("game_account_id");

-- CreateIndex
CREATE INDEX "Linked_account_account_id_idx" ON "Linked_account"("account_id");

-- CreateIndex
CREATE INDEX "Mentoring_games_game_id_idx" ON "Mentoring_games"("game_id");

-- CreateIndex
CREATE INDEX "Mentoring_games_player_id_idx" ON "Mentoring_games"("player_id");

-- CreateIndex
CREATE INDEX "Mentoring_games_active_idx" ON "Mentoring_games"("active");

-- CreateIndex
CREATE INDEX "Message_chat_session_id_idx" ON "Message_chat"("session_id");

-- CreateIndex
CREATE INDEX "Message_chat_player_id_idx" ON "Message_chat"("player_id");

-- CreateIndex
CREATE INDEX "Payment_info_status_idx" ON "Payment_info"("status");

-- CreateIndex
CREATE INDEX "Payment_info_external_payment_id_idx" ON "Payment_info"("external_payment_id");

-- CreateIndex
CREATE INDEX "Payment_info_player_id_idx" ON "Payment_info"("player_id");

-- CreateIndex
CREATE INDEX "Payment_info_seat_id_idx" ON "Payment_info"("seat_id");

-- CreateIndex
CREATE UNIQUE INDEX "Player_cpf_key" ON "Player"("cpf");

-- CreateIndex
CREATE INDEX "Player_total_views_twitch_idx" ON "Player"("total_views_twitch");

-- CreateIndex
CREATE INDEX "Player_cpf_idx" ON "Player"("cpf");

-- CreateIndex
CREATE INDEX "Player_nickname_idx" ON "Player"("nickname");

-- CreateIndex
CREATE INDEX "Player_isPro_idx" ON "Player"("isPro");

-- CreateIndex
CREATE INDEX "Player_notification_player_id_idx" ON "Player_notification"("player_id");

-- CreateIndex
CREATE INDEX "Player_notification_notification_id_idx" ON "Player_notification"("notification_id");

-- CreateIndex
CREATE INDEX "Player_stats_game_stats_id_idx" ON "Player_stats"("game_stats_id");

-- CreateIndex
CREATE INDEX "Player_stats_player_id_idx" ON "Player_stats"("player_id");

-- CreateIndex
CREATE INDEX "Player_token_token_idx" ON "Player_token"("token");

-- CreateIndex
CREATE INDEX "Player_token_code_idx" ON "Player_token"("code");

-- CreateIndex
CREATE INDEX "Player_token_player_id_idx" ON "Player_token"("player_id");

-- CreateIndex
CREATE INDEX "Promoted_session_session_id_idx" ON "Promoted_session"("session_id");

-- CreateIndex
CREATE INDEX "Promoted_session_active_idx" ON "Promoted_session"("active");

-- CreateIndex
CREATE INDEX "Rating_player_id_idx" ON "Rating"("player_id");

-- CreateIndex
CREATE INDEX "Rating_session_id_idx" ON "Rating"("session_id");

-- CreateIndex
CREATE INDEX "Recurring_sessions_session_id_idx" ON "Recurring_sessions"("session_id");

-- CreateIndex
CREATE INDEX "Refund_payment_id_idx" ON "Refund"("payment_id");

-- CreateIndex
CREATE INDEX "Refund_payment_info_id_idx" ON "Refund"("payment_info_id");

-- CreateIndex
CREATE INDEX "Seat_session_id_idx" ON "Seat"("session_id");

-- CreateIndex
CREATE INDEX "Seat_player_id_idx" ON "Seat"("player_id");

-- CreateIndex
CREATE INDEX "Session_player_id_idx" ON "Session"("player_id");

-- CreateIndex
CREATE INDEX "Session_game_id_idx" ON "Session"("game_id");

-- CreateIndex
CREATE INDEX "Session_session_base_id_idx" ON "Session"("session_base_id");

-- CreateIndex
CREATE INDEX "Session_platform_id_idx" ON "Session"("platform_id");

-- CreateIndex
CREATE INDEX "Session_input_id_idx" ON "Session"("input_id");

-- CreateIndex
CREATE INDEX "Session_isActive_idx" ON "Session"("isActive");

-- CreateIndex
CREATE INDEX "Session_metrics_session_id_idx" ON "Session_metrics"("session_id");

-- CreateIndex
CREATE INDEX "Session_metrics_player_id_idx" ON "Session_metrics"("player_id");

-- CreateIndex
CREATE INDEX "Tribute_user_id_idx" ON "Tribute"("user_id");

-- CreateIndex
CREATE INDEX "Tribute_type_tribute_value_id_idx" ON "Tribute"("type_tribute_value_id");

-- CreateIndex
CREATE INDEX "User_token_userId_idx" ON "User_token"("userId");

-- CreateIndex
CREATE INDEX "User_token_token_idx" ON "User_token"("token");

-- CreateIndex
CREATE INDEX "Users_role_id_idx" ON "Users"("role_id");

-- CreateIndex
CREATE INDEX "Wallet_player_id_idx" ON "Wallet"("player_id");
