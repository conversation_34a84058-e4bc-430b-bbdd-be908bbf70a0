/*
  Warnings:

  - You are about to drop the column `approved` on the `Withdraw_request` table. All the data in the column will be lost.
  - Added the required column `payment_external_code` to the `Withdraw_request` table without a default value. This is not possible if the table is not empty.
  - Added the required column `payment_type` to the `Withdraw_request` table without a default value. This is not possible if the table is not empty.
  - Added the required column `status` to the `Withdraw_request` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Withdraw_request" DROP COLUMN "approved",
ADD COLUMN     "bank_account_agency" TEXT,
ADD COLUMN     "bank_account_number" TEXT,
ADD COLUMN     "bank_account_type" TEXT,
ADD COLUMN     "payment_external_code" TEXT NOT NULL,
ADD COLUMN     "payment_type" TEXT NOT NULL,
ADD COLUMN     "pix_key" TEXT,
ADD COLUMN     "status" TEXT NOT NULL,
ALTER COLUMN "amount" SET DATA TYPE TEXT;
