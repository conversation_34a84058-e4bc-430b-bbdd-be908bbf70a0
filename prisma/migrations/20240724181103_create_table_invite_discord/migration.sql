-- CreateTable
CREATE TABLE "Invite_Discord" (
    "id" SERIAL NOT NULL,
    "link_discord" TEXT NOT NULL,
    "session_id" INTEGER NOT NULL,

    CONSTRAINT "Invite_Discord_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Invite_Discord_session_id_idx" ON "Invite_Discord"("session_id");

-- AddForeignKey
ALTER TABLE "Invite_Discord" ADD CONSTRAINT "Invite_Discord_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "Session"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
