import { PrismaClient } from '@prisma/client';

import * as data from './seed_data';

const prisma = new PrismaClient();

async function main() {
	console.log('Criando tabela languages');
	for (const element of data.languages) {
		await prisma.language.upsert({
			where: { code: element.code },
			update: {},
			create: {
				code: element.code,
				name: element.name
			}
		});
	}

	console.log('Criando tabela holidays');
	for (const element of data.holidays) {
		await prisma.holiday.upsert({
			where: { description: element.description },
			update: {},
			create: {
				date: element.date,
				description: element.description,
				createdAt: element.createAt
			}
		});
	}

	console.log('Criando tabela tags');
	for (const element of data.tags) {
		await prisma.tag.upsert({
			where: { name: element.name },
			update: {},
			create: {
				name: element.name
			}
		});
	}

	console.log('Criando tabela knowbiePoints');
	for (const element of data.knowbiePoints) {
		await prisma.knowbie_points.upsert({
			where: { name: element.name },
			update: {},
			create: {
				quantity: element.quantity,
				price_cents: element.priceCents,
				name: element.name,
				description: element.description,
				createdAt: element.createdAt
			}
		});
	}

	console.log('Criando tabela types');
	for (const element of data.types) {
		await prisma.type.upsert({
			where: { name: element.name },
			update: {},
			create: {
				name: element.name
			}
		});
	}

	console.log('Criando tabela cancelationOptions');
	for (const element of data.cancelationOptions) {
		await prisma.cancelation_option.upsert({
			where: { description: element.description },
			update: {},
			create: {
				description: element.description
			}
		});
	}

	console.log('Criando tabela badges');
	for (const element of data.badges) {
		await prisma.badge.upsert({
			where: { name: element.name },
			update: {},
			create: {
				name: element.name,
				description: element.description,
				icon: element.icon
			}
		});
	}

	console.log('Criando tabela permissions');
	for (const element of data.permissions) {
		await prisma.permission.upsert({
			where: { name: element.name },
			update: {},
			create: {
				name: element.name,
				description: element.description
			}
		});
	}

	console.log('Criando tabela roles');
	for (const element of data.roles) {
		await prisma.role.upsert({
			where: { name: element.name },
			update: {},
			create: {
				name: element.name,
				description: element.description,
				Permission: {
					connect: element.permissions.map(name => ({ name }))
				}
			}
		});
	}

	console.log('Criando tabela typeTributeValue');
	for (const element of data.typeTributeValue) {
		await prisma.type_tribute_value.upsert({
			where: { description: element.description },
			update: {},
			create: {
				description: element.description
			}
		});
	}

	console.log('Criando tabela platforms');
	for (const element of data.platforms) {
		await prisma.platform.upsert({
			where: { description: element.description },
			update: {},
			create: {
				description: element.description
			}
		});
	}

	console.log('Criando tabela input');
	for (const element of data.input) {
		await prisma.input.upsert({
			where: { description: element.description },
			update: {},
			create: {
				description: element.description
			}
		});
	}

	console.log('Criando tabela games');
	for (const element of data.games) {
		await prisma.game.upsert({
			where: { name: element.name },
			update: {},
			create: {
				name: element.name,
				isActive: element.isActive,
				banner: element.banner,
				logo: element.logo,
				createdAt: element.createdAt,
				updatedAt: element.updatedAt,
				game_stats: {
					createMany: {
						data: element.GameStats.map(stat => ({
							name: stat.name
						}))
					}
				}
			}
		});
	}

	console.log('Criando tabela users');
	for (const element of data.users) {
		await prisma.user.upsert({
			where: { email: element.email },
			update: {},
			create: {
				name: element.name,
				email: element.email,
				password: element.password,
				createdAt: element.createdAt,
				updatedAt: element.updatedAt,
				Role: {
					connect: {
						name: element.role.name
					}
				}
			}
		});
	}

	console.log('Criando tabela tributes');
	for (const element of data.tributes) {
		await prisma.tribute.upsert({
			where: {
				description_status: {
					description: element.description,
					status_tribute: element.statusTribute
				}
			},
			update: {},
			create: {
				description: element.description,
				value: element.value,
				status_tribute: element.statusTribute,
				createdAt: element.createdAt,
				updatedAt: element.updatedAt,
				User: {
					connect: {
						email: element.userId.email
					}
				},
				Type_tribute_value: {
					connect: {
						description: element.typeTributeValueId.description
					}
				}
			}
		});
	}
}
main()
	.then(async () => {
		await prisma.$disconnect();
	})
	.catch(async e => {
		console.error(e);
		await prisma.$disconnect();
		process.exit(1);
	});
