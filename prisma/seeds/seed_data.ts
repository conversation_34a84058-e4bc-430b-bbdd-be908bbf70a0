export const games = [
	{
		name: 'CS <PERSON>',
		isActive: true,
		banner: 'CS GO.png',
		logo: '072b37ac8791123eb85be266597af551-cs go.svg',
		createdAt: new Date('2022-04-20 13:31:30.717'),
		updatedAt: new Date('2022-04-20 13:31:30.718'),
		GameStats: [{ name: 'Kill' }, { name: 'Death' }, { name: 'Assistance' }]
	},
	{
		name: '<PERSON><PERSON><PERSON>',
		isActive: true,
		banner: 'Fortnite.png',
		logo: 'e19111a8023f7506934ef9ab88108fa7-Fortnite.svg',
		createdAt: new Date('2022-04-20 13:34:44.890'),
		updatedAt: new Date('2022-04-20 13:34:44.891'),
		GameStats: [{ name: 'Kill' }, { name: 'Death' }, { name: 'Assistance' }]
	},
	{
		name: 'League of legends',
		isActive: true,
		banner: 'League of Legends.png',
		logo: '47080b823cf12088660728c2d0f3d532-league of legends.svg',
		createdAt: new Date('2022-04-20 13:37:13.651'),
		updatedAt: new Date('2022-04-20 13:37:13.652'),
		GameStats: [{ name: 'Wins' }, { name: 'Losses' }]
	},
	{
		name: 'Call of Duty',
		isActive: true,
		banner: 'Call of Duty.png',
		logo: '3ef06a50d9269ba4b3cbf3bd97f7d1ca-Call of Duty.svg',
		createdAt: new Date('2022-04-20 13:54:19.574'),
		updatedAt: new Date('2022-04-20 13:54:19.575'),
		GameStats: [{ name: 'Kill' }, { name: 'Death' }, { name: 'Assistance' }]
	},
	{
		name: 'PUBG',
		isActive: true,
		banner: 'PUBG.png',
		logo: '0b9522e47b8d211bdbb3424c3bb47c04-pubg.svg',
		createdAt: new Date('2022-04-20 13:56:25.732'),
		updatedAt: new Date('2022-04-20 13:56:25.732'),
		GameStats: [{ name: 'Kill' }, { name: 'Death' }, { name: 'Assistance' }]
	},
	{
		name: 'Fifa',
		isActive: true,
		banner: 'FIFA.png',
		logo: 'b22d34f8148ec3dd57501dc8598121d3-FIFA.svg',
		createdAt: new Date('2022-04-20 13:57:29.892'),
		updatedAt: new Date('2022-04-20 13:57:29.892'),
		GameStats: [{ name: 'Wins' }, { name: 'Losses' }]
	},
	{
		name: 'Minecraft',
		isActive: true,
		banner: '559ca9628d05aa92949cd35092a67575-bannermine.sgv.png',
		logo: 'b436f21509db1e6e609ff5f67a616fb1-MineLogo.svg',
		createdAt: new Date('2024-01-15 13:35:55.165'),
		updatedAt: new Date('2024-02-14 18:49:40.092'),
		GameStats: []
	},
	{
		name: 'Free Fire',
		isActive: true,
		banner: 'fcdfaae8b3fe98d6fec645d264a09f9a-bannerff.png',
		logo: '28d0789b14dc2a063e8079028cc73b14-logoff.svg',
		createdAt: new Date('2024-01-18 20:31:36.855'),
		updatedAt: new Date('2024-02-14 18:53:47.215'),
		GameStats: [{ name: 'Kill' }, { name: 'Death' }, { name: 'Assistance' }]
	}
];

export const input = [
	{
		description: 'Controle'
	},
	{
		description: 'Teclado e mouse'
	},
	{
		description: 'Mobile'
	}
];

export const platforms = [
	{
		description: 'Xbox'
	},
	{
		description: 'PC'
	},
	{
		description: 'Mobile'
	},
	{
		description: 'Playstation'
	},
	{
		description: 'Switch'
	}
];

export const roles = [
	{
		name: 'Admin',
		description: 'Administrator',
		permissions: ['User', 'Tribute', 'Players']
	},
	{
		name: 'Suport',
		description: 'Suporte',
		permissions: []
	}
];

export const permissions = [
	{
		name: 'User',
		description: 'Usuários'
	},
	{
		name: 'Tribute',
		description: 'Taxas'
	},
	{
		name: 'Players',
		description: 'player'
	}
];

export const badges = [
	{
		name: 'Ranking Bronze',
		description: 'joystick-bronze.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Ranking Ouro',
		description: 'joystick-gold.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Ranking Platina',
		description: 'joystick-platinum.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Ranking Prata',
		description: 'joystick-silver.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Aprendiz Bronze',
		description: 'medal-bronze.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Aprendiz Ouro',
		description: 'medal-gold.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Aprendiz Platina',
		description: 'medal-platinum.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Aprendiz Prata',
		description: 'medal-silver.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{ name: 'Jogador Pro', description: 'Pro.png', icon: 'Jogador Pro' },
	{
		name: 'Campeão Bronze',
		description: 'trophy-bronze.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Campeão Ouro',
		description: 'trophy-gold.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Campeão Platina',
		description: 'trophy-platinum.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Campeão Prata',
		description: 'trophy-silver.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Jogador Bronze',
		description: 'gamepad-bronze.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Jogador Ouro',
		description: 'gamepad-gold.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Jogador Platina',
		description: 'gamepad-platinum.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	},
	{
		name: 'Jogador Prata',
		description: 'gamepad-silver.png',
		icon: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
	}
];

export const cancelationOptions = [
	{ description: 'Chocou com outro compromisso' },
	{ description: 'Dificuldades técnicas' },
	{ description: 'Problemas de saúde' },
	{ description: 'Muitas desistências' },
	{ description: 'Remuneração abaixo do esperado' },
	{ description: 'Poucos agendamentos' },
	{ description: 'Outro motivo' }
];

export const types = [
	{ name: 'FPS' },
	{ name: 'MOBA' },
	{ name: 'Corrida' },
	{ name: 'Estratégia' },
	{ name: 'Ação' },
	{ name: 'Shotter' },
	{ name: 'Aventura' },
	{ name: 'RPG' },
	{ name: 'Simulação' },
	{ name: 'Sports' },
	{ name: 'Puzzle' },
	{ name: 'Horror' },
	{ name: 'MMO' },
	{ name: 'Battle Royale' },
	{ name: 'Cartas e Tabuleiro' },
	{ name: 'Música/Ritmo' },
	{ name: 'Educativos' },
	{ name: 'Indie' },
	{ name: 'Outros' }
];

export const knowbiePoints = [
	{
		quantity: 50,
		priceCents: 1490,
		name: 'Pacote Base',
		description: 'Base',
		createdAt: new Date('2022-04-08 17:53:47.260')
	},
	{
		quantity: 200,
		priceCents: 5490,
		name: 'Pacote Bronze',
		description: 'Bronze',
		createdAt: new Date('2022-04-08 17:53:47.264')
	},
	{
		quantity: 450,
		priceCents: 10990,
		name: 'Pacote Prata',
		description: 'Silver',
		createdAt: new Date('2022-04-08 17:53:47.268')
	},
	{
		quantity: 750,
		priceCents: 17990,
		name: 'Pacote Ouro',
		description: 'Gold',
		createdAt: new Date('2022-04-08 17:53:47.272')
	},
	{
		quantity: 1300,
		priceCents: 27490,
		name: 'Pacote Platina',
		description: 'Platinum',
		createdAt: new Date('2022-04-08 17:53:47.276')
	},
	{
		quantity: 2850,
		priceCents: 54990,
		name: 'Pacote Diamante',
		description: 'Diamond',
		createdAt: new Date('2022-04-08 17:53:47.280')
	},
	{
		quantity: 1,
		priceCents: 100,
		name: 'Avulso',
		description: 'KnowbiePoint',
		createdAt: new Date('2022-04-08 17:53:47.284')
	}
];

export const tags = [
	{ name: 'Engraçado' },
	{ name: 'Excelente' },
	{ name: 'Simpático' },
	{ name: 'Comunicativo' },
	{ name: 'Pontual' },
	{ name: 'Boa didática' },
	{ name: 'Paciente' },
	{ name: 'Profissional' },
	{ name: 'Joga muito!' },
	{ name: 'Atencioso' }
];

export const holidays = [
	{
		date: new Date('2024-09-07 00:00:00.000'),
		description: 'Independencia do Brasil',
		createAt: new Date('2024-01-26 14:23:15.833')
	},
	{
		date: new Date('2024-03-29 00:00:00.000'),
		description: 'Paixão de Cristo',
		createAt: new Date('2024-01-26 14:47:20.958')
	},
	{
		date: new Date('2024-04-21 00:00:00.000'),
		description: 'Tiradentes',
		createAt: new Date('2024-01-26 14:48:54.072')
	},
	{
		date: new Date('2024-05-01 00:00:00.000'),
		description: 'Dia do Trabalho',
		createAt: new Date('2024-01-26 14:48:54.193')
	},
	{
		date: new Date('2024-10-12 00:00:00.000'),
		description: 'Nossa senhora aparecida',
		createAt: new Date('2024-01-26 14:53:32.824')
	},
	{
		date: new Date('2024-11-02 00:00:00.000'),
		description: 'Dia de finados',
		createAt: new Date('2024-01-26 14:53:32.948')
	},
	{
		date: new Date('2024-11-15 00:00:00.000'),
		description: 'Proclamação da República',
		createAt: new Date('2024-01-26 14:53:33.072')
	},
	{
		date: new Date('2024-11-20 00:00:00.000'),
		description: 'Consciencia Negra',
		createAt: new Date('2024-01-26 14:53:33.192')
	},
	{
		date: new Date('2024-12-25 00:00:00.000'),
		description: 'Natal',
		createAt: new Date('2024-01-26 14:53:33.316')
	},
	{
		date: new Date('2024-07-02 00:00:00.000'),
		description: 'Independencia da Bahia',
		createAt: new Date('2024-01-26 14:57:23.971')
	},
	{
		date: new Date('2024-05-30 00:00:00.000'),
		description: 'Corpus Christi',
		createAt: new Date('2024-01-26 14:57:24.103')
	},
	{
		date: new Date('2024-06-24 00:00:00.000'),
		description: 'São João',
		createAt: new Date('2024-01-26 14:57:24.225')
	},
	{
		date: new Date('2024-12-08 00:00:00.000'),
		description: 'Nossa Senhora da Conceição da Praia',
		createAt: new Date('2024-01-26 14:57:24.357')
	}
];

export const users = [
	{
		name: 'Marcos',
		email: '<EMAIL>',
		password: '$2b$10$v87qX7GYX1Xp0MOQjS1uRelJ6Li4h1Df67zUbb/YVLAumw.5DAAnW',
		createdAt: new Date('2023-11-13 14:35:26.040'),
		updatedAt: new Date('2023-11-13 14:35:26.040'),
		role: { name: 'Admin' }
	},
	{
		name: 'WTF',
		email: '<EMAIL>',
		password: '$2b$10$dMgpIicCoAIpMeo3x75XIe9TDjJATTu5IyWYTkHQ0nySey1QkQjBS',
		createdAt: new Date('2023-11-13 14:35:26.040'),
		updatedAt: new Date('2023-11-13 14:35:26.040'),
		role: { name: 'Admin' }
	}
];

export const typeTributeValue = [
	{
		description: 'Porcentagem'
	},
	{
		description: 'Real'
	}
];

export const tributes = [
	{
		description: 'withdraw pix fee',
		value: 0.52,
		statusTribute: false,
		createdAt: new Date('2023-11-07 14:44:20.640'),
		updatedAt: new Date('2024-01-17 14:33:01.552'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'withdraw pix fee',
		value: 0.5,
		statusTribute: true,
		createdAt: new Date('2024-01-17 14:33:01.552'),
		updatedAt: new Date('2024-01-17 14:33:01.552'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'withdraw ted fee',
		value: 1.99,
		statusTribute: false,
		createdAt: new Date('2023-11-07 14:44:08.147'),
		updatedAt: new Date('2024-01-26 12:14:15.921'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'withdraw ted fee',
		value: 1.99,
		statusTribute: true,
		createdAt: new Date('2024-01-26 12:14:15.921'),
		updatedAt: new Date('2024-01-26 12:14:15.921'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'payment player fee',
		value: 1.52,
		statusTribute: false,
		createdAt: new Date('2024-01-04 20:02:31.090'),
		updatedAt: new Date('2024-02-02 17:38:52.883'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'payment player fee',
		value: 4.99,
		statusTribute: true,
		createdAt: new Date('2024-02-02 17:38:52.883'),
		updatedAt: new Date('2024-02-02 17:38:52.883'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'comission fee',
		value: 10.0,
		statusTribute: false,
		createdAt: new Date('2023-11-07 14:34:07.211'),
		updatedAt: new Date('2024-02-02 17:42:46.544'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'comission fee',
		value: 5.0,
		statusTribute: false,
		createdAt: new Date('2024-01-04 20:24:20.485'),
		updatedAt: new Date('2024-02-02 17:42:46.544'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'comission fee',
		value: 8.0,
		statusTribute: false,
		createdAt: new Date('2024-01-25 20:07:05.618'),
		updatedAt: new Date('2024-02-02 17:42:46.544'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'comission fee',
		value: 5.0,
		statusTribute: false,
		createdAt: new Date('2024-01-25 20:08:19.342'),
		updatedAt: new Date('2024-02-02 17:42:46.544'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'comission fee',
		value: 5.0,
		statusTribute: false,
		typeTributeValueId: { description: 'Porcentagem' },
		createdAt: new Date('2024-01-25 20:08:50.698'),
		updatedAt: new Date('2024-02-02 17:42:46.544'),
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'comission fee',
		value: 10.0,
		statusTribute: true,
		createdAt: new Date('2024-02-02 17:42:46.544'),
		updatedAt: new Date('2024-02-02 17:42:46.544'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'license fee',
		value: 5.0,
		statusTribute: false,
		createdAt: new Date('2023-11-07 14:35:30.123'),
		updatedAt: new Date('2024-02-02 17:43:49.747'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'license fee',
		value: 8.52,
		statusTribute: false,
		createdAt: new Date('2024-01-04 20:10:37.680'),
		updatedAt: new Date('2024-02-02 17:43:49.747'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	},
	{
		description: 'license fee',
		value: 2.0,
		statusTribute: true,
		createdAt: new Date('2024-02-02 17:43:49.747'),
		updatedAt: new Date('2024-02-02 17:43:49.747'),
		typeTributeValueId: { description: 'Porcentagem' },
		userId: { email: '<EMAIL>' }
	}
];

export const languages = [
	{
		code: 'pt-BR',
		name: 'Português (Brasil)'
	},
	{
		code: 'en-US',
		name: 'English (US)'
	}
];
