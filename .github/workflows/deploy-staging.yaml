---
name: "Build and Deploy WTF Knowbie API Staging to AWS ECR."
on:
  pull_request:
    branches:
      - staging
    types:
      - closed

env:
  ECR_REGISTRY: ${{ secrets.AWS_ECR_REGISTER_STAG }}
  ECR_REPOSITORY: ${{ secrets.AWS_ECR_REPOSITORY_STAG }}
  IMAGE_TAG: latest

permissions:
  contents: read
  id-token: write

jobs:
  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    strategy:
      matrix:
        os: [ubuntu-latest]
        node-version: [14.x]

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_STAG }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_STAG }}
        aws-region: ${{ secrets.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build, Tag, and Push image to Amazon ECR
      working-directory: ./
      id: build-image
      run: |
        touch .env
        echo "${{ secrets.ENV_STAG }}" > .env
        docker build -t ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ env.IMAGE_TAG }} .
        docker push ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ env.IMAGE_TAG }}
