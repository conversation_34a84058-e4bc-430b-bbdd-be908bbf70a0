import ptLogin from './auth/login.json';
import ptRecoveryPassword from './auth/recoveryPassword.json';
import ptRegister from './auth/register.json';
import ptCreateSession from './create_session.json';
import ptCommon from './common.json';
import ptHome from './home.json';
import ptProfile from './profile.json';
import ptSettings from './settings.json';
import ptEditSession from './edit_session.json';
import ptAvailability from './availability.json';
import ptEvaluation from './evaluation.json';
import ptHighlight from './highlight.json';
import ptInfoSession from './infoSession.json';
import ptInfoSessionProPlayer from './infoSessionProPlayer.json';
import ptMentoringCancellation from './mentoringCancellation.json';
import ptMessageScreen from './messageScreen.json';
import ptNotifications from './notifications.json';
import ptWallet from './wallet.json';

export default {
	common: ptCommon.common,
	home: ptHome.home,
	createSession: ptCreateSession.createSession,
	editSession: ptEditSession.editSession,
	profile: ptProfile.profile,
	settings: ptSettings.settings,
	availability: ptAvailability.availability,
	evaluation: ptEvaluation.evaluation,
	highlight: ptHighlight.highlight,
	infoSession: ptInfoSession.infoSession,
	infoSessionProPlayer: ptInfoSessionProPlayer.infoSessionProPlayer,
	mentoringCancellation: ptMentoringCancellation.mentoringCancellation,
	messageScreen: ptMessageScreen.messageScreen,
	notifications: ptNotifications.notifications,
	wallet: ptWallet.wallet,
	auth: {
		login: ptLogin.login,
		recoveryPassword: ptRecoveryPassword.recoveryPassword,
		register: ptRegister.register
	}
};
