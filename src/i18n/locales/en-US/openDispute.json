{"openDispute": {"title": "Contest payment", "disputePayment": {"title": "Do you want to contest the payment for this session?", "subtitle": "A dispute between you and the <PERSON><PERSON> will be initiated and analyzed by our team. If your dispute is approved you will receive the refund in your wallet.", "session": "Session", "value": "Value", "processInfo": "This process can take up to 30 days. You will be notified of every change.", "yesContinue": "Yes, continue"}, "justification": {"title": "Justification", "subtitle": "Write the reason for contesting the payment in as much detail as possible. You can add images and videos to complement your justification.", "placeholder": "Write your justification...", "addFile": "Add\nfile", "send": "Send", "errorTitle": "Request denied", "errorMessage": "Something went wrong with your request, please try again later!"}, "justificationSent": {"title": "Justification sent", "subtitle": "Your justification has been sent and you can view the status of the dispute at any time.", "viewStatus": "View status", "back": "Back"}}}