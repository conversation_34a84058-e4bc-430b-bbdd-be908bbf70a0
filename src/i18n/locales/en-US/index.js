import enLogin from './auth/login.json';
import enRecoveryPassword from './auth/recoveryPassword.json';
import enRegister from './auth/register.json';
import enCommon from './common.json';
import enHome from './home.json';
import enProfile from './profile.json';
import enSettings from './settings.json';
import enCreateSession from './create_session.json';
import enEditSession from './edit_session.json';
import enAvailability from './availability.json';
import enEvaluation from './evaluation.json';
import enHighlight from './highlight.json';
import enInfoSession from './infoSession.json';
import enInfoSessionProPlayer from './infoSessionProPlayer.json';
import enMentoringCancellation from './mentoringCancellation.json';
import enMessageScreen from './messageScreen.json';
import enNotifications from './notifications.json';
import enWallet from './wallet.json';
import enMetrics from './metrics.json';
import enMigrationPro from './migrationPro.json';
import enOpenDispute from './openDispute.json';

export default {
	common: enCommon.common,
	home: enHome.home,
	createSession: enCreateSession.createSession,
	editSession: enEditSession.editSession,
	profile: enProfile.profile,
	settings: enSettings.settings,
	availability: enAvailability.availability,
	evaluation: enEvaluation.evaluation,
	highlight: enHighlight.highlight,
	infoSession: enInfoSession.infoSession,
	infoSessionProPlayer: enInfoSessionProPlayer.infoSessionProPlayer,
	mentoringCancellation: enMentoringCancellation.mentoringCancellation,
	messageScreen: enMessageScreen.messageScreen,
	notifications: enNotifications.notifications,
	wallet: enWallet.wallet,
	metrics: enMetrics.metrics,
	migrationPro: enMigrationPro.migrationPro,
	openDispute: enOpenDispute.openDispute,
	auth: {
		login: enLogin.login,
		recoveryPassword: enRecoveryPassword.recoveryPassword,
		register: enRegister.register
	}
};
