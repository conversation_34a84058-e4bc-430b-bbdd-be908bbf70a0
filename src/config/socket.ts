import { Server, Socket } from "socket.io";
import http from "http";
const SendMessageController = require("@modules/socket/useCases/sendMessage/SendMessageController");
const CreateSessionChatController = require("@modules/socket/useCases/createSessionChat/CreateSessionChatController");
const JoinSessionChatController = require("@modules/socket/useCases/joinSessionChat/JoinSessionChatController");
const GetListOnlineChatController = require("@modules/socket/useCases/getListOnlineChat/GetListOnlineChatController");
const LeaveSessionChatController = require("@modules/socket/useCases/leaveSessionChat/LeaveSessionChatController");

export const configureSocket = (server: http.Server) => {
  const io = new Server(server);
  const sessions = new Map<string, Set<string>>();

  io.on("connection", (socket) => {
    console.log("Um cliente conectou-se ao servidor de socket");

    SendMessageController(socket, io);
    CreateSessionChatController(socket, sessions);
    JoinSessionChatController(socket, sessions);
    GetListOnlineChatController(socket, io, sessions);
    LeaveSessionChatController(socket, sessions);
    socket.on("disconnect", () => {
      sessions.forEach((users, sessionName) => {
        users.delete(socket.id);
        if (users.size === 0) {
          sessions.delete(sessionName);
        }
      });
      console.log("Um cliente desconectou-se do servidor de socket");
    });
  });

  return io;
};
