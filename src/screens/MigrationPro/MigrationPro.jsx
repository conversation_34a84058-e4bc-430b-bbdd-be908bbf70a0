//GLOBAL - COMPONENTS
import React, { useEffect, useState, useRef } from 'react';
import {
	View,
	KeyboardAvoidingView,
	FlatList,
	LogBox,
	Image,
	Text,
	TouchableOpacity,
	ImageBackground,
	Platform,
	Dimensions
} from 'react-native';
import validator from 'validator';
import { Masks } from 'react-native-mask-input';
import { cpf } from 'cpf-cnpj-validator';
import WebView from 'react-native-webview';
import { MotiView } from 'moti';
import Constants from 'expo-constants';
import { useTranslation } from 'react-i18next';

//COMPONENTS
import { PageHeader } from '../../components/Navigation';
import { Input, Radio } from '../../components/DataEntry';
import { StepSignUp, HeaderSteps, Card, BlurOverlay } from '../../components/DataDisplay';
import { StepAnimatedView, ScrollView } from '../../components/Layout';
import { SkeletonLoading, Modalize, Modal, TextError } from '../../components/Feedback';
import { Typography, Button, ButtonText } from '../../components/General';

//SERVICES
import * as API from '../../api';

//GLOBAL STATE - redux
import { useDispatch, useSelector } from 'react-redux';
import * as userActions from '../../redux/reducers/user';

//ASSETS
import {
	User,
	Close,
	Coins,
	Star,
	Book,
	Document,
	Telephone,
	Key,
	Trophy,
	Shield,
	Calendar,
	Angle,
	Sync,
	Info
} from '../../assets/icons';
import ProfileUser from '../../assets/images/placeholderImageAvatar.svg';

//STYLES
import styles from './styles';
import { theme } from '@theme';
import { SvgUri } from 'react-native-svg';

export function MigrationPro({ navigation }) {
	const { t } = useTranslation();
	const [currentStep, setCurrentStep] = useState(1);
	const [animated, setAnimated] = useState(true);
	const [gamesData, setGamesData] = useState();
	const [skeletonLoading, setSkeletonLoading] = useState(true);
	const [loadingButton, setLoadingButton] = useState(false);
	const [messageInput, setMessageInput] = useState({ message: '', type: '' });
	const [checkTermOfUse, setCheckTermOfUse] = useState(false);
	const [isModalSyncVisible, setIsModalSyncVisible] = useState(false);
	const [editableInput, setEditableInput] = useState(true);
	const [dataGameSync, setDataGameSync] = useState({
		index: null,
		logoGame: null,
		game_id: null
	});

	const modalizeTermOfUse = useRef(null);
	const webViewRef = useRef(null);
	const { height: initialHeight } = Dimensions.get('window');
	const [height, setHeight] = useState(initialHeight);

	const [dataUser, setDataUser] = useState({
		name: '',
		cpf: '',
		birth_date: '',
		phone: '',
		mentoringGame: []
	});

	const [newAddress, setNewAddress] = useState({
		cep: '',
		street: '',
		district: '',
		number: '',
		city: '',
		state: '',
		complement: ''
	});

	const disableButtonAddress =
		newAddress.cep != '' &&
		newAddress.cep.length === 9 &&
		newAddress.street != '' &&
		newAddress.district != '' &&
		newAddress.number != '';

	const dispatch = useDispatch();
	const user = useSelector(state => state.user);

	const goNextStep = step => {
		setAnimated(true);
		setMessageInput({ message: '', type: '' });
		setCurrentStep(step);
	};

	const goBackStep = () => {
		setAnimated(false);
		setMessageInput({ message: '', type: '' });
		setCurrentStep(currentStep - 1);
	};

	const goToContinue = (step, field) => {
		if (dataUser[field].trim() === '') {
			setDataUser({ ...dataUser, [field]: '' });
			return setMessageInput({
				message: t('migrationPro.errors.fillAllFields'),
				type: 'error'
			});
		}

		setMessageInput({ message: '', type: '' });

		goNextStep(step);
	};

	const goToContinueAddress = step => {
		if (
			newAddress.street.trim() === '' ||
			newAddress.cep.trim() === '' ||
			newAddress.district.trim() === '' ||
			newAddress.number.trim() === ''
		) {
			return setMessageInput({
				message: t('migrationPro.errors.fillAllFields'),
				type: 'error'
			});
		}

		goNextStep(step);
	};

	const clearCep = () => {
		setNewAddress({
			...newAddress,
			cep: '',
			street: '',
			district: '',
			number: '',
			city: '',
			state: '',
			complement: ''
		});
		setMessageInput({ message: '', type: '' });
	};

	const handleLayout = ({ layout }) => {
		setHeight(layout.height);
	};

	const goPage = page => {
		navigation.navigate(page);
	};

	const handleSubmit = async () => {
		setLoadingButton(true);

		let birth_date = dataUser.birth_date.split('/');
		let newFormatBirthDate = `${birth_date[1]}/${birth_date[0]}/${birth_date[2]}`;

		try {
			const { data } = await API.auth.migrationPro(dataUser, newFormatBirthDate, newAddress);

			dispatch(userActions.updateUser(data));
			goNextStep(9);
		} catch (error) {
			return setMessageInput({
				message: t('migrationPro.errors.registrationError'),
				type: 'error'
			});
		} finally {
			setLoadingButton(false);
		}
	};

	const handleSelectionGame = (id, logoGame, index) => {
		if (dataUser.mentoringGame.includes(id)) {
			return setDataUser({ ...dataUser, mentoringGame: [] });
		}

		setDataUser({ ...dataUser, mentoringGame: [id] });
		setDataGameSync({ logoGame: logoGame, game_id: id, index: index });

		openModalSyncGame();
	};

	const handleValidationPhone = step => {
		if (!validator.isMobilePhone(dataUser.phone, 'pt-BR')) {
			return setMessageInput({
				message: t('migrationPro.errors.invalidPhone'),
				type: 'error'
			});
		}

		goNextStep(step);
	};

	const getAddress = async () => {
		setEditableInput(false);
		try {
			const { data } = await API.brasilApi.getAddress(newAddress.cep);
			setNewAddress({
				...newAddress,
				street: data.street,
				district: data.neighborhood,
				city: data.city,
				state: data.state
			});
		} catch (error) {
			console.log(error);
		} finally {
			setEditableInput(true);
		}
	};

	const validatorCPF = () => {
		if (cpf.isValid(dataUser.cpf)) {
			return goNextStep(5);
		}

		return setMessageInput({
			message: t('migrationPro.errors.invalidCPF'),
			type: 'error'
		});
	};

	const clearInput = field => {
		setDataUser({ ...dataUser, [field]: '' });
		setMessageInput({ message: '', type: '' });
	};

	const openModalSyncGame = () => {
		setIsModalSyncVisible(!isModalSyncVisible);
	};

	const cancelModalSyncGame = () => {
		setIsModalSyncVisible(!isModalSyncVisible);
		setDataUser({ ...dataUser, mentoringGame: [] });
	};

	const syncGameAccount = () => {
		setLoadingButton(true);
		setTimeout(() => {
			setIsModalSyncVisible(!isModalSyncVisible);
			goNextStep(3);
			setLoadingButton(false);
		}, 1000);
	};

	//USEFFECT

	useEffect(() => {
		const getGames = async () => {
			try {
				const data = await API.games.all();
				setGamesData(data);
				setSkeletonLoading(false);
			} catch (error) {
				console.log(error);
			}
		};

		getGames();
	}, []);

	useEffect(() => {
		newAddress.cep.length === 9 && getAddress();
		newAddress.cep.length === 0 && clearCep();
	}, [newAddress.cep]);

	useEffect(() => {
		LogBox.ignoreLogs(['VirtualizedLists should never be nested']);
	}, []);

	return (
		<KeyboardAvoidingView
			style={styles.container}
			behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
			<ScrollView>
				{currentStep != 1 && currentStep != 9 && <PageHeader onPress={() => goBackStep()} />}

				{currentStep != 1 && currentStep != 8 && currentStep != 9 && (
					<View style={styles.containerActualStep}>
						<HeaderSteps onStep={currentStep} type="migration" style={{ width: '80%' }} />
					</View>
				)}

				{currentStep === 1 && (
					<MotiView
						from={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ type: 'timing', duration: 1500 }}>
						<TouchableOpacity
							activeOpacity={0.7}
							onPress={() => navigation.goBack()}
							style={styles.headerClose}>
							<Close width="28" height="28" fill={theme.colors.white_1} />
						</TouchableOpacity>
						<View style={styles.initialStep}>
							<Typography.H1>{t('migrationPro.title')}</Typography.H1>

							<View style={styles.containerCallAction}>
								<View style={styles.containerTextCallAction}>
									<Book fill={theme.colors.white_1} width="30" height="30" />
									<Typography.Text fontSize={16} style={styles.textCallAction}>
										{t('migrationPro.benefits.shareKnowledge')}
									</Typography.Text>
								</View>
								<View style={styles.containerTextCallAction}>
									<Star fill={theme.colors.white_1} width="30" height="30" />
									<Typography.Text fontSize={16} style={styles.textCallAction}>
										{t('migrationPro.benefits.recognition')}
									</Typography.Text>
								</View>
								<View style={styles.containerTextCallAction}>
									<Coins fill={theme.colors.white_1} width="30" height="30" />
									<Typography.Text fontSize={16} style={styles.textCallAction}>
										{t('migrationPro.benefits.earnMoney')}
									</Typography.Text>
								</View>
							</View>
							<Image
								style={styles.imageCoinsDown}
								source={require('../../assets/images/CoinsDown.png')}
							/>
						</View>

						<View style={styles.containerFooterInitialStep}>
							<View style={styles.containerButtonContinue}>
								<Button onPress={() => goNextStep(2)}>
									<Typography.ButtonText>
										{t('migrationPro.buttons.continue')}
									</Typography.ButtonText>
								</Button>
							</View>
						</View>
					</MotiView>
				)}

				{currentStep === 2 && (
					<StepAnimatedView styleAnimated={animated}>
						<View style={styles.containerInformation}>
							<Typography.H1>{t('migrationPro.steps.selectGame.title')}</Typography.H1>

							<View style={styles.containerDescription}>
								<Typography.Text style={styles.textTitleGame}>
									{t('migrationPro.steps.selectGame.description')}
								</Typography.Text>
							</View>
						</View>

						<View style={styles.containerGames}>
							{skeletonLoading ? (
								<SkeletonLoading.Card.CardGames />
							) : (
								<FlatList
									contentContainerStyle={styles.listCardGames}
									showsVerticalScrollIndicator={false}
									nestedScrollEnabled
									data={gamesData}
									keyExtractor={gamesData => gamesData.id.toString()}
									renderItem={({ item, index }) => (
										<View style={styles.containerGame}>
											<Card.CardGames
												select={dataUser.mentoringGame === item.id}
												onPress={() => {
													handleSelectionGame(item.id, item.logo, index);
												}}
												banner={item.banner}
												logo={item.logo}
											/>
										</View>
									)}
								/>
							)}
						</View>

						<View style={styles.containerMain}>
							{/* <View style={styles.containerFooter}>
                <Typography.Text>Não encontrou seu jogo?</Typography.Text>
                <ButtonText onPress={() => {}}>Deixe sua sugestão!</ButtonText>
              </View> */}
						</View>

						<Card.CardInfo
							icon={<Sync style={styles.iconInfoTerm} />}
							description={t('migrationPro.steps.selectGame.syncInfo')}
							style={[styles.containerInfoAfterMigration, styles.infoSyncGame]}
						/>
					</StepAnimatedView>
				)}

				{currentStep === 3 && (
					<StepAnimatedView styleAnimated={animated}>
						<StepSignUp
							title={t('migrationPro.steps.fullName.title')}
							subTitle={t('migrationPro.steps.fullName.subtitle')}
							textButton={t('migrationPro.buttons.continue')}
							disable={!(dataUser.name.length >= 3)}
							onPressButton={() => goToContinue(4, 'name')}
							loading={loadingButton}>
							<View style={styles.containerInputs}>
								<Input
									value={dataUser.name}
									placeholder={t('migrationPro.steps.fullName.placeholder')}
									maxLength={60}
									autoCapitalize="words"
									icon={<User />}
									clearInput={() => clearInput('name')}
									onChangeText={event =>
										setDataUser({
											...dataUser,
											name: event.replace(/[^a-zA-Z ]/g, '')
										})
									}
								/>
								<View style={styles.containerFooterInput}>
									<TextError>{messageInput?.message}</TextError>
								</View>
							</View>
						</StepSignUp>
					</StepAnimatedView>
				)}

				{currentStep === 4 && (
					<StepAnimatedView styleAnimated={animated}>
						<StepSignUp
							title={t('migrationPro.steps.cpf.title')}
							subTitle={t('migrationPro.steps.cpf.subtitle')}
							textButton={t('migrationPro.buttons.continue')}
							disable={!(dataUser.cpf.length === 11)}
							onPressButton={() => validatorCPF()}
							loading={loadingButton}>
							<View style={styles.containerInputs}>
								<Input
									value={dataUser.cpf}
									placeholder={'000.000.000-00'}
									mask={Masks.BRL_CPF}
									keyboardType="numeric"
									maxLength={14}
									icon={<Document />}
									clearInput={() => clearInput('cpf')}
									onChangeText={event =>
										setDataUser({
											...dataUser,
											cpf: event.replace(/[-\/.]/g, '')
										})
									}
								/>
								<View style={styles.containerFooterInput}>
									<TextError>{messageInput?.message}</TextError>
								</View>
							</View>
						</StepSignUp>
					</StepAnimatedView>
				)}

				{currentStep === 5 && (
					<StepAnimatedView styleAnimated={animated}>
						<StepSignUp
							title={t('migrationPro.steps.address.title')}
							loading={loadingButton}
							disable={!disableButtonAddress}
							onPressButton={() => goToContinueAddress(6)}
							textButton={t('migrationPro.buttons.continue')}>
							<View
								style={{
									marginHorizontal: 16,
									marginTop: 50,
									marginBottom: 70
								}}>
								<View style={styles.containerInput}>
									<Input
										value={newAddress.cep}
										placeholder={t('migrationPro.steps.address.cep')}
										mask={Masks.ZIP_CODE}
										keyboardType="numeric"
										clearInput={() => clearCep()}
										onChangeText={value => setNewAddress({ ...newAddress, cep: value })}
									/>
								</View>
								<View style={styles.containerInput}>
									<Input
										value={newAddress.street}
										placeholder={t('migrationPro.steps.address.street')}
										clearInput={() => setNewAddress({ ...newAddress, street: '' })}
										onChangeText={value =>
											setNewAddress({
												...newAddress,
												street: value
											})
										}
										editable={editableInput}
									/>
								</View>

								<View style={styles.containerInput}>
									<View style={styles.containerAddressNumber}>
										<View style={styles.containerDistrict}>
											<Input
												value={newAddress.district}
												placeholder={t('migrationPro.steps.address.district')}
												clearInput={() => setNewAddress({ ...newAddress, district: '' })}
												onChangeText={value => setNewAddress({ ...newAddress, district: value })}
												editable={editableInput}
											/>
										</View>

										<View style={styles.containerNumberAddress}>
											<Input
												value={newAddress.number}
												placeholder={t('migrationPro.steps.address.number')}
												keyboardType="numeric"
												clearInput={() => setNewAddress({ ...newAddress, number: '' })}
												onChangeText={value => setNewAddress({ ...newAddress, number: value })}
											/>
										</View>
									</View>

									<View style={[styles.containerFooterInput, styles.messageAddress]}>
										<TextError>{messageInput?.message}</TextError>
									</View>
								</View>

								<View style={styles.containerInput}>
									<Input
										value={newAddress.complement}
										placeholder={t('migrationPro.steps.address.complement')}
										clearInput={() => setNewAddress({ ...newAddress, complement: '' })}
										onChangeText={value => setNewAddress({ ...newAddress, complement: value })}
									/>
								</View>
							</View>
						</StepSignUp>
					</StepAnimatedView>
				)}

				{currentStep === 6 && (
					<StepAnimatedView styleAnimated={animated}>
						<StepSignUp
							title={t('migrationPro.steps.phone.title')}
							subTitle={t('migrationPro.steps.phone.subtitle')}
							textButton={t('migrationPro.buttons.continue')}
							disable={!(dataUser.phone.length === 11)}
							onPressButton={() => handleValidationPhone(7)}
							loading={loadingButton}>
							<View style={styles.containerInputs}>
								<Input
									value={dataUser.phone}
									placeholder={'(00) 00000-0000'}
									mask={Masks.BRL_PHONE}
									keyboardType="numeric"
									maxLength={15}
									icon={<Telephone />}
									clearInput={() => clearInput('phone')}
									onChangeText={event =>
										setDataUser({
											...dataUser,
											phone: event.replace(/[- \/\\()]/g, '')
										})
									}
								/>
								<View style={styles.containerFooterInput}>
									<TextError>{messageInput?.message}</TextError>
								</View>
							</View>
						</StepSignUp>
					</StepAnimatedView>
				)}

				{currentStep === 7 && (
					<StepAnimatedView styleAnimated={animated}>
						<StepSignUp
							// title="Dados bancários"
							// subTitle="Insira os dados da conta que vai receber a transferência."
							textButton={t('migrationPro.buttons.continue')}
							disable={false}
							onPressButton={() => goNextStep(8)}
							// loading={loadingButton}
						>
							<View style={styles.containerDataBank}>
								<TouchableOpacity
									style={styles.containerInputBankModalize}
									activeOpacity={0.8}
									onPress={() => {}}>
									<Typography.Text style={styles.placeholderDataBank}>
										Instituição Financeira
									</Typography.Text>

									<Angle style={styles.iconAngleBank} />
								</TouchableOpacity>

								<TouchableOpacity
									style={styles.containerInputBankModalize}
									activeOpacity={0.8}
									onPress={() => {}}>
									<Typography.Text style={styles.placeholderDataBank}>
										Tipo de conta
									</Typography.Text>

									<Angle style={styles.iconAngleBank} />
								</TouchableOpacity>

								<View style={styles.inputDataBank}>
									<View style={styles.inputAgency}>
										<Input value="" placeholder="Agência" />
									</View>
									<View style={styles.inputAccount}>
										<Input value="" placeholder="Conta com dígito" />
									</View>
								</View>
							</View>

							<Card.CardInfo
								style={styles.containerInfo}
								icon={<Info style={styles.iconInfo} />}
								description="Essa informação será solicitada assim que você puder cobrar pelas sessões."
							/>
							<BlurOverlay
								title="Dados bancários"
								description="Os dados bancários serão necessários caso você queira sacar o valor que ganhou realizando as sessões"
								descriptionSecond="Ainda não é possível cobrar pelas sessões na versão Beta"
								styleContent={styles.contentOverlay}
							/>
						</StepSignUp>
					</StepAnimatedView>
				)}

				{currentStep === 8 && (
					<StepAnimatedView styleAnimated={animated}>
						<View style={styles.termStep}>
							<Typography.H1>Quase lá</Typography.H1>
							<Typography.Text style={styles.textSubtitle}>
								Confirme a mudança da sua conta.
							</Typography.Text>

							<View style={styles.containerTermStepGameSync}>
								<ImageBackground
									resizeMode="cover"
									opacity={0.5}
									source={{
										uri: gamesData[dataGameSync?.index]?.banner
									}}
									style={styles.imageBackgroundTerm}>
									<Image style={styles.imageFade} source={require('../../mocks/fade.png')} />
									<SvgUri width="120" height="65" uri={gamesData[dataGameSync?.index]?.logo} />
								</ImageBackground>
							</View>

							<Card.CardInfo
								style={styles.containerInfoAfterMigration}
								icon={<Shield style={styles.iconInfoTerm} />}
								description="Suas informações pessoais são privadas e jamais serão exibidas no seu perfil público."
							/>
						</View>

						<View style={styles.containerFooterMigrationPro}>
							<View style={styles.containerTermOfUse}>
								<Radio
									seats={false}
									style={styles.containerCheckBox}
									onPress={() => setCheckTermOfUse(!checkTermOfUse)}
									checkboxState={checkTermOfUse}
								/>
								<View style={styles.containerTextAgreeTerm}>
									<Typography.Text>Concordo com os </Typography.Text>
									<TouchableOpacity onPress={() => modalizeTermOfUse.current?.open()}>
										<Typography.Text style={styles.textClickTermOfUse}>
											termos e condições de uso
										</Typography.Text>
									</TouchableOpacity>
								</View>
							</View>
							<View style={styles.containerButtonMigrationPro}>
								<Button
									onPress={() => handleSubmit()}
									disable={!checkTermOfUse}
									loading={loadingButton}>
									<Typography.ButtonText>Mudar para conta Pro</Typography.ButtonText>
								</Button>
							</View>
						</View>
					</StepAnimatedView>
				)}

				{currentStep === 9 && (
					<StepAnimatedView styleAnimated={animated}>
						<View style={styles.headerClose}>
							<Close
								width="28"
								height="28"
								fill={theme.colors.white_1}
								onPress={() => navigation.goBack()}
							/>
						</View>
						<View style={styles.termStep}>
							<Typography.H1>Agora você é Pro</Typography.H1>
							<Typography.Text style={styles.textSubtitle}>
								Que tal configurar sua disponibilidade e adicionar seus primeiros horários?
							</Typography.Text>

							<View style={styles.containeMigrationProSucessful}>
								<ImageBackground
									source={{
										uri: user.cover_picture
									}}
									resizeMode="cover"
									style={styles.imageBackgroundUserPro}>
									<Image
										style={styles.imageFadeCoverPlayer}
										source={require('../../mocks/fade.png')}
									/>
									<View style={styles.containerUserInfo}>
										{user.avatar ? (
											<Image source={{ uri: user.avatar }} style={styles.userAvatar} />
										) : (
											<ProfileUser width={96} height={96} style={styles.placeholderAvatar} />
										)}

										<View style={styles.containerAllInfo}>
											<View style={styles.containerUserInfoKnobie}>
												<Typography.H1 style={styles.nicknameUser}>{user.nickname}</Typography.H1>
											</View>

											<View style={styles.containerBadges}>
												{user.badges.map(badges => {
													return (
														<Image
															key={badges.id}
															source={{ uri: badges.icon }}
															style={styles.iconsBadges}
														/>
													);
												})}
											</View>
										</View>
									</View>
								</ImageBackground>
							</View>

							<View style={styles.containerFinalyStep}>
								<TouchableOpacity
									activeOpacity={0.7}
									style={styles.containerInfoFinalyMigration}
									onPress={() => goPage('/availability')}>
									<View style={styles.headerInfoFinaly}>
										<Calendar style={styles.iconInfoFinaly} />
										<Typography.Text style={styles.titleHeaderFinaly} fontSize={16}>
											Disponibilidade
										</Typography.Text>
									</View>
									<View style={styles.contentInfoFinaly}>
										<Typography.Text style={styles.textInfoFinaly}>
											Selecione os dias e horários disponíveis e configure o valor das suas sessões
											de forma personalizada.
										</Typography.Text>
										<Angle style={styles.iconAngle} />
									</View>
								</TouchableOpacity>

								<TouchableOpacity
									activeOpacity={0.7}
									style={styles.containerInfoFinalyMigration}
									onPress={() => goPage('/profile')}>
									<View style={styles.headerInfoFinaly}>
										<Star
											style={styles.iconInfoFinaly}
											fill={theme.colors.white}
											width={24}
											height={24}
										/>
										<Typography.Text style={styles.titleHeaderFinaly} fontSize={16}>
											Avaliações
										</Typography.Text>
									</View>
									<View style={styles.contentInfoFinaly}>
										<Typography.Text style={styles.textInfoFinaly}>
											Fique de olho na sua nota e garanta que não caia oferecendo boas mentorias.
										</Typography.Text>
										<Angle style={styles.iconAngle} />
									</View>
								</TouchableOpacity>

								{/* <TouchableOpacity
									activeOpacity={0.7}
									style={styles.containerInfoFinalyMigration}
									onPress={() => goPage('/promote')}>
									<View style={styles.headerInfoFinaly}>
										<Calendar style={styles.iconInfoFinaly} />
										<Typography.Text
											style={styles.titleHeaderFinaly}
											fontSize={16}>
											Promova suas sessões
										</Typography.Text>
									</View>
									<View style={styles.contentInfoFinaly}>
										<Typography.Text style={styles.textInfoFinaly}>
											Chegue com tudo e ganhe uma rápida visibilidade.
										</Typography.Text>
										<Angle style={styles.iconAngle} />
									</View>
								</TouchableOpacity> */}
							</View>
						</View>

						<View style={styles.containerFooterButtonHome}>
							<View style={styles.containerButtonHome}>
								<Button onPress={() => goPage('Home')}>
									<Typography.ButtonText>Home</Typography.ButtonText>
								</Button>
							</View>
						</View>
					</StepAnimatedView>
				)}
			</ScrollView>

			<Modalize modalizeRef={modalizeTermOfUse} portal={false} onLayout={handleLayout}>
				<WebView
					ref={webViewRef}
					source={{
						uri: Constants.expoConfig.extra.TERM_OF_USE_MIGRATION
					}}
					style={{ height }}
				/>
			</Modalize>

			<Modal
				isOpenModal={isModalSyncVisible}
				onBackdropPress={() => cancelModalSyncGame()}
				onBackButtonPress={() => cancelModalSyncGame()}
				style={styles.containerModal}>
				<View style={styles.headerModalSync}>
					<Typography.H1 style={styles.titleModal}>Confirmar Jogo</Typography.H1>
					<Typography.Text>
						Sua conta já está vinculada! Gostaria de mentorar com esse jogo?
					</Typography.Text>
				</View>

				<SvgUri width="118" height="28" uri={dataGameSync.logoGame} style={styles.logoGameSync} />

				<View style={styles.containerOptionsButtonModal}>
					<ButtonText onPress={cancelModalSyncGame}>Cancelar</ButtonText>

					<View style={styles.buttonConfirmModal}>
						<Button loading={loadingButton} onPress={() => syncGameAccount()}>
							<Typography.ButtonText>Confirmar</Typography.ButtonText>
						</Button>
					</View>
				</View>
			</Modal>
		</KeyboardAvoidingView>
	);
}
