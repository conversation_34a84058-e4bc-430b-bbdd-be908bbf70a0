//GLOBAL COMPONENTS
import React, { useState } from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

import { ScrollView } from '../../components/Layout';
import { PageHeader } from '../../components/Navigation';
import { DisputePayment, Justification, JustificationSent } from './components';

import styles from './styles';

export function OpenDispute({ navigation, route }) {
	const { t } = useTranslation();
	const [currentStep, setCurrentStep] = useState(1);
	const dataSession = route?.params?.dataSession;
	const playerSeat = route?.params?.playerSeat;

	return (
		<View style={styles.container}>
			<ScrollView style={styles.containerScroll}>
				<PageHeader
					title={t('openDispute.title')}
					disableActions={true}
					arrowBack={currentStep !== 3}
					onPress={() => {
						if (currentStep === 2) {
							setCurrentStep(1);
						}
						if (currentStep === 3) {
							navigation.navigate('/sessionhistory');
						}
						if (currentStep === 1) {
							navigation.goBack();
						}
					}}
				/>
				{currentStep === 1 && (
					<DisputePayment
						dataSession={dataSession}
						onPressButton={() => {
							setCurrentStep(2);
						}}
					/>
				)}
				{currentStep === 2 && (
					<Justification
						dataSession={dataSession}
						onPressButton={() => {
							setCurrentStep(3);
						}}
					/>
				)}
				{currentStep === 3 && (
					<JustificationSent
						dataSession={dataSession}
						playerSeat={playerSeat}
						navigation={navigation}
					/>
				)}
			</ScrollView>
		</View>
	);
}
