//GLOBAL COMPONENTS
import React from 'react';
import { View, Image } from 'react-native';
import { SvgUri } from 'react-native-svg';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

import { ButtonLarge, Typography } from '../../../../components/General';

import { Info } from '../../../../assets/icons';
import PlaceholderSmallAvatar from '../../../../assets/images/placeholderImageAvatar.svg';

import { Card } from '../../../../components/DataDisplay';

import { formatPrice } from '../../../../utils';
import { Header } from '../Header/Header';

import styles from './styles';
import { useImage } from '../../../../hooks/useImage';

export function DisputePayment({ dataSession, onPressButton }) {
	// Hooks
	const { t } = useTranslation();
	const { image_uri } = useImage();

	const dateHour = dayjs(dataSession?.star_time).format('ddd DD MMM YYYY HH:MM');

	return (
		<View style={styles.container}>
			<Header
				title={t('openDispute.disputePayment.title')}
				subTitle={t('openDispute.disputePayment.subtitle')}
			/>

			<View style={styles.cardSession}>
				<View style={styles.rowCard}>
					<View style={styles.cardSession.section}>
						{dataSession?.Player?.avatar ? (
							<Image
								source={{
									uri: dataSession?.Player?.avatar
								}}
								style={styles.cardSession.section.avatar}
							/>
						) : (
							<PlaceholderSmallAvatar
								width={50}
								height={50}
								style={styles.cardSession.section.avatar}
							/>
						)}
						<Typography.Text style={styles.textNickname} fontSize={16}>
							{'@' + dataSession?.Player?.nickname}
						</Typography.Text>
					</View>

					{dataSession?.Game?.logo && (
						<SvgUri
							width={63}
							height={40}
							uri={
								dataSession?.Game?.logo.startsWith(image_uri.gamesLogo)
									? dataSession?.Game?.logo
									: image_uri.gamesLogo + dataSession?.Game?.logo
							}
						/>
					)}
				</View>

				<View style={styles.rowCard}>
					<View style={styles.cardSession.section}>
						<Typography.Text style={styles.cardSession.section.title} fontSize={16}>
							{t('openDispute.disputePayment.session')}
						</Typography.Text>
					</View>
					<View style={{}}>
						<Typography.Text style={styles.textDescription} fontSize={16}>
							{dateHour}
						</Typography.Text>
					</View>
				</View>

				<View style={styles.rowCard}>
					<View style={styles.cardSession.section}>
						<Typography.Text style={styles.cardSession.section.title} fontSize={16}>
							{t('openDispute.disputePayment.value')}
						</Typography.Text>
					</View>
					<View style={{}}>
						<Typography.Text style={styles.textDescription} fontSize={16}>
							{`R$ ${formatPrice(dataSession?.price)}`}
						</Typography.Text>
					</View>
				</View>
			</View>

			<Card.CardInfo
				style={{ marginTop: '20%' }}
				icon={<Info style={{ marginLeft: -6 }} />}
				description={t('openDispute.disputePayment.processInfo')}
			/>

			<ButtonLarge style={{ marginTop: '20%' }} onPress={onPressButton}>
				<Typography.ButtonText style={{ fontSize: 16, fontFamily: 'RobotoBold' }} fontSize={16}>
					{t('openDispute.disputePayment.yesContinue')}
				</Typography.ButtonText>
			</ButtonLarge>
		</View>
	);
}
