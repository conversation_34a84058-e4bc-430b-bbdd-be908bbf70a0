//GLOBAL COMPONENTS
import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

import { ButtonLarge, ButtonOutline, Typography } from '../../../../components/General';

import { CheckGradient } from '../../../../assets/icons';
import { Header } from '../Header/Header';
import styles from './styles';

export function JustificationSent({ navigation, dataSession, playerSeat }) {
	const { t } = useTranslation();

	function goSessionDispute() {
		navigation.navigate('/sessiondispute', {
			dataSession: dataSession,
			playerSeat: playerSeat,
			isMentor: false
		});
	}

	function goInfoSession() {
		navigation.navigate('/infosession', {
			dataSession: dataSession
		});
	}

	return (
		<View style={styles.container}>
			<Header
				title={t('openDispute.justificationSent.title')}
				subTitle={t('openDispute.justificationSent.subtitle')}
			/>

			<View style={styles.contentIconCheckGradient}>
				<CheckGradient height={300} widht={300} />
			</View>

			<ButtonLarge style={styles.buttonLarge} onPress={goSessionDispute}>
				<Typography.ButtonText style={styles.titleButton} fontSize={16}>
					{t('openDispute.justificationSent.viewStatus')}
				</Typography.ButtonText>
			</ButtonLarge>
			<ButtonOutline style={styles.buttonOutline} onPress={goInfoSession}>
				<Typography.ButtonText outlineColor style={styles.titleButton} fontSize={16}>
					{t('openDispute.justificationSent.back')}
				</Typography.ButtonText>
			</ButtonOutline>
		</View>
	);
}
