//GLOBAL COMPONENTS
import React, { useState } from 'react';
import { View, Image, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';

import * as DocumentPicker from 'expo-document-picker';
import * as MediaLibrary from 'expo-media-library';

import { ButtonLarge, Typography } from '../../../../components/General';

import { AttachmentDocumentIcon, ClosePurple, Plus } from '../../../../assets/icons';
import TextArea from '../../../../components/DataEntry/TextArea/TextArea';

import { ModalMessageError } from '../../../../components/Feedback';
import { Header } from '../Header/Header';

import * as API from '../../../../api';

import styles from './styles';
import { formatMultiformData, formatMultiformDataFile } from '../../../../utils';

export function Justification({ dataSession, onPressButton }) {
	// Hooks
	const { t } = useTranslation();
	const [permissionResponse, requestPermission] = MediaLibrary.usePermissions();

	const [disableButton, setDisableButton] = useState(false);
	const [isPickingDocument, setIsPickingDocument] = useState(false);
	const [textJustification, setTextJustification] = useState('');
	const [fileUpload, setFileUpload] = useState(
		{
			type: '' || undefined,
			name: '',
			size: 0 || undefined,
			uri: '',
			mimeType: '' || undefined,
			lastModified: 0 || undefined,
			file: null,
			output: null
		} || undefined
	);
	const [isModalConfirmedVisibleError, setIsModalConfirmedVisibleError] = useState(false);
	const [messageErrorModal, setMessageErrorModal] = useState({
		title: '',
		message: '',
		visibleImage: true
	});

	function closeModalError() {
		setIsModalConfirmedVisibleError(false);
	}
	function openModalError() {
		setIsModalConfirmedVisibleError(true);
	}

	function handleToCleanFileUpload() {
		setFileUpload({
			type: '',
			name: '',
			size: undefined,
			uri: '',
			mimeType: '',
			lastModified: undefined,
			file: null,
			output: null
		});
	}

	const filePickerCall = async () => {
		if (isPickingDocument) {
			return;
		}

		setIsPickingDocument(true);

		if (permissionResponse.status !== 'granted') {
			await requestPermission();
		}

		try {
			const pickedFile = await DocumentPicker.getDocumentAsync({
				type: '*/*'
			});

			if (pickedFile.canceled !== false) {
				return;
			}

			setFileUpload({
				...fileUpload,
				uri: pickedFile.assets[0].uri,
				name: pickedFile.assets[0].name,
				mimeType: pickedFile.assets[0].mimeType,
				size: pickedFile.assets[0].size
			});
		} catch (error) {
			console.error('DocumentPicker: ', error);
			throw error;
		} finally {
			setIsPickingDocument(false);
		}
	};

	/// return FormData Files
	const getFormData = () => {
		let file;
		const formData = new FormData();

		if (fileUpload.mimeType.startsWith('image/')) {
			file = formatMultiformData(fileUpload.uri);
		} else {
			file = formatMultiformDataFile(fileUpload.uri);
		}

		formData.append('session_id', dataSession?.id);
		formData.append('description', textJustification);
		formData.append('attachment', file);

		return formData;
	};

	const handleSendOpenDispute = async () => {
		try {
			setDisableButton(true);

			if (fileUpload.uri === '' || fileUpload.uri === (undefined || null)) {
				await API.sessions.openDispute(
					{ description: textJustification, session_id: dataSession?.id },
					false
				);
			} else {
				const formData = getFormData();
				await API.sessions.openDispute(formData, true);
			}
			onPressButton();
		} catch (error) {
			setMessageErrorModal({
				title: t('openDispute.justification.errorTitle'),
				message: t('openDispute.justification.errorMessage'),

				visibleImage: false
			});
			openModalError();
		} finally {
			setDisableButton(false);
		}
	};

	const ButtonAddArchive = () => {
		return (
			<TouchableOpacity onPress={filePickerCall} style={styles.containerButtonAddArchive}>
				<Plus />
				<Typography.ButtonText style={styles.titleButtonAddArchive} outlineColor>
					{t('openDispute.justification.addFile')}
				</Typography.ButtonText>
			</TouchableOpacity>
		);
	};

	const ButtonEditArchive = () => {
		return (
			<TouchableOpacity onPress={handleToCleanFileUpload} style={styles.containerButtonEditArchive}>
				{fileUpload.mimeType.startsWith('image/') ? (
					<Image source={{ uri: fileUpload.uri }} style={styles.imageFileUpload} />
				) : (
					<>
						<View style={styles.containerDocument}>
							<View style={styles.contentIconDocument}>
								<AttachmentDocumentIcon height={70} width={90} opacity={0.3} />
							</View>

							<View style={styles.contentIconClosePurple}>
								<ClosePurple height={45} width={45} />
							</View>
						</View>
						<Typography.Text fontSize={10}>{fileUpload.name}</Typography.Text>
					</>
				)}
			</TouchableOpacity>
		);
	};

	return (
		<View style={styles.container}>
			<Header
				title={t('openDispute.justification.title')}
				subTitle={t('openDispute.justification.subtitle')}
			/>

			<View style={styles.cardSession}>
				<TextArea
					placeholder={t('openDispute.justification.placeholder')}
					onChangeText={input => {
						setTextJustification(input);
					}}
					height={240}
				/>
			</View>

			<View style={styles.containerAddEditFile}>
				{fileUpload.uri !== '' && fileUpload.uri !== (undefined || null) ? (
					<ButtonEditArchive />
				) : (
					<ButtonAddArchive />
				)}
			</View>

			<ButtonLarge
				style={styles.buttonLarge}
				disable={textJustification.length === 0 || disableButton}
				loading={disableButton}
				onPress={handleSendOpenDispute}>
				<Typography.ButtonText style={styles.titleButtonLarge} fontSize={16}>
					{t('openDispute.justification.send')}
				</Typography.ButtonText>
			</ButtonLarge>

			<ModalMessageError
				isModalConfirmedVisibleError={isModalConfirmedVisibleError}
				messageErrorModal={messageErrorModal}
				closeModalError={closeModalError}
			/>
		</View>
	);
}
