//GLOBAL COMPONENTS
import React, { useState, useEffect, useRef } from 'react';
import { View, TouchableOpacity, Dimensions } from 'react-native';
import dayjs from 'dayjs';
import { addDays, eachDayOfInterval } from 'date-fns';
import { LineChart } from 'react-native-chart-kit';
import { useTranslation } from 'react-i18next';

//COMPONENTS
import { Typography, ButtonText } from '../../components/General';
import { ScrollView } from '../../components/Layout';
import { PageHeader } from '../../components/Navigation';
import { Modalize } from '../../components/Feedback';

//ASSETS
import { Interrogation, Clock, Angle } from '../../assets/icons';

//SERVICES
import * as API from '../../api';

//MOCKS
import { daysFilterMetrics, infoMetrics } from '../../mocks';

//STYLES
import styles from './styles';
import { theme } from '@theme';

export function Metrics({ navigation, route }) {
	const { t } = useTranslation();
	const [daysMetrics, setDaysMetrics] = useState(7);
	const [typeInfoModalize, setTypeInfoModalize] = useState('');
	const [dataMetricsSession, setDataMetricsSession] = useState();
	const [chartViewsData, setChartViewsData] = useState([0]);
	const [labelChartDate, setLabelChartDate] = useState(['']);

	const screenWidth = Dimensions.get('window').width;

	const modalizeInfoMetrics = useRef(null);
	const modalizeSelectFilter = useRef(null);

	const rangeDateShowMetrics = eachDayOfInterval({
		start: new Date(),
		end: addDays(new Date(), daysMetrics)
	});

	const dataChart = {
		labels: labelChartDate,
		datasets: [
			{
				data: chartViewsData,
				color: (opacity = 1) => `rgba(172, 77, 248, ${opacity})`,
				strokeWidth: 3
			}
		]
	};

	const chartConfig = {
		backgroundGradientFrom: theme.colors.background,
		backgroundGradientTo: theme.colors.background,
		color: (opacity = 1) => `rgba(172, 77, 248, ${opacity})`,
		labelColor: (opacity = 1) => `rgba(197, 197, 197, ${opacity} )`,
		useShadowColorFromDataset: false,
		decimalPlaces: 0,
		propsForDots: {
			r: '4',
			strokeWidth: '1'
		}
	};

	//FUNCTIONS
	const showModalizeInfo = type => {
		setTypeInfoModalize(type);
		modalizeInfoMetrics.current?.open();
	};

	const selectFiltreModalize = days => {
		setDaysMetrics(days);
		modalizeSelectFilter.current?.close();
	};

	//USEFFECT
	useEffect(() => {
		const getDataMetrics = async () => {
			try {
				const { data } = await API.sessions.getAllMetrics(route.params?.sessionId, daysMetrics);

				setDataMetricsSession(data);
			} catch (error) {
				console.log(error);
			}
		};

		getDataMetrics();
	}, [daysMetrics]);

	useEffect(() => {
		const viewsDataList = [];
		const labelDateList = [];

		const createLabelsChart = () => {
			dataMetricsSession?.metricsByDate.map(value => viewsDataList.push(value.views));

			dataMetricsSession?.metricsByDate.map(value =>
				labelDateList.push(dayjs(value.date).format('D MMM'))
			);

			setLabelChartDate(labelDateList);
			setChartViewsData(viewsDataList);
		};

		dataMetricsSession && createLabelsChart();
	}, [dataMetricsSession]);

	return (
		<View style={styles.container}>
			<ScrollView style={styles.containerScroll}>
				<PageHeader title={t('metrics.title')} />

				<View style={styles.containerInfoMetrics}>
					<TouchableOpacity
						activeOpacity={0.7}
						onPress={() => modalizeSelectFilter.current?.open()}
						style={styles.containerFilterMetrics}>
						<Clock />
						<Typography.Text style={styles.filterActiveShowMetrics}>
							{t('metrics.lastDays', { count: daysMetrics })}
						</Typography.Text>
						<Angle style={styles.iconAngle} />
					</TouchableOpacity>
					<Typography.Text style={styles.rangeDateFilter}>
						{dayjs(rangeDateShowMetrics[0]).format('D MMM') +
							' - ' +
							dayjs(rangeDateShowMetrics[daysMetrics]).format('D MMM')}
					</Typography.Text>
				</View>

				<View style={styles.containerInfoCharts}>
					<View style={styles.containerTypeMetrics}>
						<Typography.Text fontSize={16} style={[styles.labelTypeMetrics, styles.labelViews]}>
							{t('metrics.views')}
						</Typography.Text>
						<TouchableOpacity activeOpacity={0.7} onPress={() => showModalizeInfo('views')}>
							<Interrogation />
						</TouchableOpacity>
					</View>

					<View style={styles.containerChart}>
						<ScrollView horizontal={true}>
							<LineChart
								data={dataChart}
								width={
									daysMetrics === 7
										? screenWidth + daysMetrics * 7
										: daysMetrics === 15
											? screenWidth + daysMetrics * 30
											: daysMetrics === 30
												? screenWidth + daysMetrics * 60
												: screenWidth
								}
								height={220}
								chartConfig={chartConfig}
								withVerticalLines={false}
								bezier
							/>
						</ScrollView>
					</View>
				</View>

				<View style={styles.containerInfoMetrics}>
					<View style={styles.containerTypeMetrics}>
						<Typography.Text fontSize={16} style={styles.labelTypeMetrics}>
							{t('metrics.reach')}
						</Typography.Text>
						<TouchableOpacity activeOpacity={0.7} onPress={() => showModalizeInfo('reach')}>
							<Interrogation />
						</TouchableOpacity>
					</View>
					<Typography.H2 style={styles.valueMetrics}>
						{dataMetricsSession?.reach || '--'}
					</Typography.H2>
				</View>

				<View style={styles.containerInfoMetrics}>
					<View style={styles.containerTypeMetrics}>
						<Typography.Text fontSize={16} style={styles.labelTypeMetrics}>
							{t('metrics.clicks')}
						</Typography.Text>
						<TouchableOpacity activeOpacity={0.7} onPress={() => showModalizeInfo('clicks')}>
							<Interrogation />
						</TouchableOpacity>
					</View>
					<Typography.H2 style={styles.valueMetrics}>
						{dataMetricsSession?.clicks || '--'}
					</Typography.H2>
				</View>

				<View style={styles.containerInfoMetrics}>
					<View style={styles.containerTypeMetrics}>
						<Typography.Text fontSize={16} style={styles.labelTypeMetrics}>
							{t('metrics.checkout')}
						</Typography.Text>
						<TouchableOpacity activeOpacity={0.7} onPress={() => showModalizeInfo('checkout')}>
							<Interrogation />
						</TouchableOpacity>
					</View>
					<Typography.H2 style={styles.valueMetrics}>
						{dataMetricsSession?.checkouts || '--'}
					</Typography.H2>
				</View>
			</ScrollView>

			<Modalize
				modalizeRef={modalizeInfoMetrics}
				height={200}
				maxHeight={200}
				portal={false}
				HeaderComponent={
					<View style={styles.headerModal}>
						<Typography.Text style={styles.headerTitleModal} fontSize={16}>
							{infoMetrics[typeInfoModalize]?.title}
						</Typography.Text>
					</View>
				}>
				<View style={styles.modalContainer}>
					<Typography.Text style={styles.textInfoModalize}>
						{infoMetrics[typeInfoModalize]?.description}
					</Typography.Text>

					<ButtonText onPress={() => modalizeInfoMetrics.current?.close()}>
						<Typography.ButtonText outlineColor>{t('metrics.okButton')}</Typography.ButtonText>
					</ButtonText>
				</View>
			</Modalize>

			<Modalize
				modalizeRef={modalizeSelectFilter}
				height={320}
				maxHeight={320}
				portal={false}
				HeaderComponent={
					<View style={styles.headerModal}>
						<Typography.Text style={styles.headerTitleModal} fontSize={16}>
							{t('metrics.lastDaysTitle')}
						</Typography.Text>
					</View>
				}>
				<View style={styles.modalContainer}>
					{daysFilterMetrics.map((value, index) => {
						return (
							<TouchableOpacity
								key={index}
								activeOpacity={0.7}
								onPress={() => selectFiltreModalize(value.days)}
								style={[
									styles.cardSelectFilterMetrics,
									daysMetrics === value.days && styles.selectCardSelectFilterMetrics
								]}>
								<Typography.Text style={styles.labelCardSelecFilter}>
									{t('metrics.lastDays', { count: value.days })}
								</Typography.Text>
							</TouchableOpacity>
						);
					})}
				</View>
			</Modalize>
		</View>
	);
}
