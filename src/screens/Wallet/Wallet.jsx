//GLOBAL COMPONENTS
import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
	View,
	FlatList,
	TouchableOpacity,
	Dimensions,
	Image,
	LogBox,
	RefreshControl
} from 'react-native';
import { useIsFocused } from '@react-navigation/native';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

//COMPONENTS
import { Typography, ButtonCircleOutline } from '../../components/General';
import { PageHeader } from '../../components/Navigation';
import { Modalize, LoadingSpinner, EmptyState, ModalMessageError } from '../../components/Feedback';
import { Card, SessionReceipt } from '../../components/DataDisplay';

//SERVICES
import * as API from '../../api';

//UTILS
import { formatDateAndHour, differenceHour, formatPrice } from '../../utils';

//REDUX
import { useDispatch, useSelector } from 'react-redux';
import * as userActions from '../../redux/reducers/user';

//ASSETS
import { AnglePurple, Plus, Angle, Interrogation } from '../../assets/icons';

//STYLES
import styles from './styles';
import { theme } from '@theme';
import { SvgUri } from 'react-native-svg';
import ModalizeHelpValueBalance from '../Withdraw/components/ModalizeHelpValueBalance/ModalizeHelpValueBalance';
import ModalizePaymentConfirmed from './components/ModalizePaymentConfirmed/ModalizePaymentConfirmed';
import { useModalize } from '../../hooks/useModalize';

export function Wallet({ navigation }) {
	const { t } = useTranslation();
	const [isModalConfirmedVisibleError, setIsModalConfirmedVisibleError] = useState(false);
	const [messageErrorModal, setMessageErrorModal] = useState({
		title: '',
		message: ''
	});
	const [refreshing, setRefreshing] = useState(false);
	const [withdraw, setWithdraw] = useState();
	const [dataWalletStatements, setDataWalletStatements] = useState([]);
	const [loading, setLoading] = useState(false);
	const [paginationWallet, setPaginationWallet] = useState({
		page: 1,
		limit: 10
	});

	const [dataSessionModalize, setDataSessionModalize] = useState({});

	const [pendingFundsProcess, setPendingFundsProcess] = useState({});

	const isFocused = useIsFocused();
	const dispatch = useDispatch();
	const user = useSelector(state => state.user);

	const { formartHour: hourSession } = formatDateAndHour(dataSessionModalize?.start_time);
	const hourDifference = differenceHour(
		dataSessionModalize?.start_time,
		dataSessionModalize?.end_time
	);

	const daySession = dayjs(dataSessionModalize?.start_time).format('ddd D MMM YYYY').trim();

	const { height: initialHeightModalize } = Dimensions.get('window');
	const heightModalize = initialHeightModalize;

	const { handleOpenModalize } = useModalize();

	const modalizeConfirmTransfer = useRef(null);
	const modalizeInfosSession = useRef(null);
	const modalizeSessionBuy = useRef(null);
	const modalizeSessionCanceled = useRef(null);
	const modalizeHelpValueBalance = useRef(null);
	const modalizeHelpValueProcessing = useRef(null);

	const getDataWallet = async () => {
		setLoading(true);
		try {
			const { data } = await API.player.wallet(paginationWallet);
			dispatch(userActions.updateUser({ knowbie_points: data.balance }));

			if (data.statements != 0) {
				setTimeout(() => {
					setDataWalletStatements([...dataWalletStatements, ...data.statements]);

					setPaginationWallet({
						...paginationWallet,
						page: paginationWallet.page + 1
					});
					setLoading(false);
				}, 500);
			} else {
				setTimeout(() => {
					setLoading(false);
				}, 1000);
			}
		} catch (error) {
			setLoading(false);
			console.error(error);
		}
	};

	async function getPendingFunds() {
		try {
			const { data } = await API.player.getPendingFunds(user.id);
			setPendingFundsProcess(data);
		} catch (error) {
			console.error(error);
		}
	}

	const openModalize = {
		outcome: () => {
			handleOpenModalize(modalizeInfosSession);
		},
		income: () => {
			handleOpenModalize(modalizeSessionBuy);
		},
		canceled: () => {
			handleOpenModalize(modalizeSessionCanceled);
		}
	};

	const getDataSession = async (type, sessionId, buyerId, date) => {
		try {
			const { data } = await API.sessions.sessionOnly(sessionId, buyerId);
			setDataSessionModalize({ ...data, date: date });
			openModalize[type]();
		} catch (error) {
			console.error(error);
		}
	};

	async function getWithdrawById(withdrawId) {
		try {
			const { data } = await API.player.getWithdrawById(withdrawId);
			setWithdraw(data);

			if (data.status.toLowerCase() === t('wallet.status.completed').toLowerCase()) {
				goReceipt(data);
				return;
			}

			handleOpenModalize(modalizeConfirmTransfer);
		} catch (error) {
			console.error(error);

			if (error.message === 'Network Error') {
				setMessageErrorModal({
					title: t('wallet.error.title'),
					message: t('wallet.error.networkError'),
					visibleImage: true
				});

				setIsModalConfirmedVisibleError(true);
			} else {
				setMessageErrorModal({
					title: t('wallet.error.title'),
					message: t('wallet.error.genericError'),
					visibleImage: true
				});
				setIsModalConfirmedVisibleError(true);
			}
		}
	}

	function goReceipt(withdraw) {
		navigation.navigate('/receipt', { withdraw });
	}

	function handleVerificationType(item) {
		if (item?.Withdraw_request.length > 0) {
			getWithdrawById(item?.Withdraw_request[0]?.id);
			return;
		}

		getDataSession(item.type, item.session_id, item.buyer_id, item.date);
	}

	//FUNCTIONS - FLATLIST
	const renderItemStatements = useCallback(({ item }) => {
		return (
			<Card.CardTransactions
				type={item.type}
				date={item.date}
				description={item.description}
				title={item.title}
				amount={item.amount}
				onPress={() => handleVerificationType(item)}
			/>
		);
	});

	const keyExtractor = useCallback(item => item.id.toString(), []);

	//USEEFFECT

	useEffect(() => {
		if (isFocused) {
			getDataWallet();
			getPendingFunds();
		}

		if (!isFocused) {
			setPaginationWallet({
				...paginationWallet,
				page: 1
			});
			setDataWalletStatements([]);
		}
	}, [isFocused]);

	useEffect(() => {
		LogBox.ignoreLogs(['VirtualizedLists should never be nested']);
	}, []);

	const onRefresh = () => {
		setRefreshing(true);
		getDataWallet();
		getPendingFunds();
		setRefreshing(false);
	};

	const HeaderView = () => {
		return (
			<View style={{}}>
				<PageHeader title={t('wallet.title')} arrowBack={false} />

				<View>
					<View style={[styles.containerHeaderwallet, styles.headerWalletWhitoutPro]}>
						<View>
							{user.isPro?.active && (
								<TouchableOpacity
									activeOpacity={0.8}
									style={styles.containerAvailableWithdrawal}
									onPress={
										user.knowbie_points != null ? () => navigation.navigate('/withdraw') : null
									}>
									<View>
										<View style={styles.infoMessageWithdrawal}>
											<Typography.Text style={styles.textInfoWithdrawal}>
												{t('wallet.availableBalance')}
											</Typography.Text>
										</View>
										<View style={styles.valueAvailable}>
											<Typography.Text fontSize={40} style={styles.textValueAvailable}>
												R${' '}
												{user.knowbie_points != undefined && user.knowbie_points != null
													? formatPrice(user.knowbie_points)
													: ''}
											</Typography.Text>
										</View>
									</View>

									<AnglePurple style={styles.anglePurpleIcon} />
								</TouchableOpacity>
							)}

							{/* SAQUE EM PROCESSAMENTO */}
							<View style={styles.containerBalance}>
								<View style={styles.containerTitleBalanceValue}>
									<Typography.Text style={{ flex: 1, textAlign: 'left' }}>
										{t('wallet.withdrawInProcess')}
									</Typography.Text>
									<TouchableOpacity
										activeOpacity={0.8}
										onPress={() => {
											handleOpenModalize(modalizeHelpValueProcessing);
										}}>
										<View>
											<Interrogation style={styles.iconInterrogation} />
										</View>
									</TouchableOpacity>
								</View>
								<View style={styles.containerBalanceValue}>
									<Typography.Text fontSize={16} style={styles.valueBalance}>
										R${' '}
										{pendingFundsProcess?.processFunds != undefined &&
										pendingFundsProcess?.processFunds != null
											? formatPrice(pendingFundsProcess?.processFunds)
											: ''}
									</Typography.Text>
								</View>
							</View>

							{/* SALDO PENDENTE */}
							<View style={styles.containerBalance}>
								<View style={styles.containerTitleBalanceValue}>
									<Typography.Text style={{ flex: 1, textAlign: 'left' }}>
										{t('wallet.pendingBalance')}
									</Typography.Text>
									<TouchableOpacity
										activeOpacity={0.8}
										onPress={() => {
											handleOpenModalize(modalizeHelpValueBalance);
										}}>
										<View>
											<Interrogation style={styles.iconInterrogation} />
										</View>
									</TouchableOpacity>
								</View>
								<View style={styles.containerBalanceValue}>
									<Typography.Text fontSize={16} style={styles.valueBalance}>
										R${' '}
										{pendingFundsProcess?.pendingFunds != undefined ||
										pendingFundsProcess?.pendingFunds != null
											? formatPrice(pendingFundsProcess?.pendingFunds)
											: ''}
									</Typography.Text>
								</View>
							</View>
						</View>
					</View>
				</View>

				<View style={styles.containerTitleList}>
					<Typography.Text fontSize={16} style={styles.textHistory}>
						{t('wallet.history')}
					</Typography.Text>
				</View>
			</View>
		);
	};

	return (
		<View style={styles.container}>
			<FlatList
				style={[styles.containerListStatements, styles.containerScroll]}
				contentContainerStyle={styles.statementsContainer}
				data={dataWalletStatements}
				keyExtractor={keyExtractor}
				renderItem={renderItemStatements}
				initialNumToRender={10}
				showsVerticalScrollIndicator={false}
				ListHeaderComponent={HeaderView()}
				ListFooterComponentStyle={styles.footerList}
				ListFooterComponent={
					loading ? (
						<LoadingSpinner />
					) : dataWalletStatements.length != 0 ? (
						<ButtonCircleOutline style={{ marginBottom: 20 }} onPress={() => getDataWallet()}>
							<Plus />
						</ButtonCircleOutline>
					) : null
				}
				ListEmptyComponent={!loading && <EmptyState title={t('wallet.emptyHistory')} />}
				refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
			/>
			<Modalize
				modalizeRef={modalizeInfosSession}
				height={heightModalize - 100}
				maxHeight={heightModalize - 100}
				HeaderComponent={
					<View style={styles.headerModalizeHistory}>
						<Typography.H1 style={styles.textTitleHistoryModalize}>
							{t('wallet.modal.sessionPurchase')}
						</Typography.H1>
						<Typography.Text style={styles.textDateHistoryModalize}>
							{dayjs(dataSessionModalize?.date).format('DD MMM YYYY - H:mm')}
						</Typography.Text>
					</View>
				}>
				<View style={{ paddingHorizontal: 15, marginTop: 18, marginBottom: 30 }}>
					<View style={styles.containerInfoPlayer}>
						<View style={styles.infoPlayerNickname}>
							<Image
								style={styles.playerAvatar}
								source={
									dataSessionModalize?.pro_player_avatar
										? {
												uri: dataSessionModalize?.pro_player_avatar
											}
										: require('../../assets/images/placeholderAvatar.png')
								}
							/>
							<Typography.Text>@{dataSessionModalize?.pro_player_nickname}</Typography.Text>
						</View>
					</View>

					<View style={styles.containerInfoSession}>
						<View style={styles.detailsSession}>
							<Typography.Text>{t('wallet.modal.game')}</Typography.Text>
							<View style={{ height: 45 }}>
								<SvgUri width={70} height={40} uri={dataSessionModalize?.game_logo} />
							</View>
						</View>
						<View style={styles.detailsSession}>
							<Typography.Text fontSize={16}>{t('wallet.modal.date')}</Typography.Text>
							<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
								{dayjs(dataSessionModalize?.start_time).format('ddd DD MMM YYYY')}
							</Typography.Text>
						</View>
						<View style={styles.detailsSession}>
							<Typography.Text fontSize={16}>{t('wallet.modal.time')}</Typography.Text>
							<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
								{hourSession}
							</Typography.Text>
						</View>
						<View style={styles.detailsSession}>
							<Typography.Text fontSize={16}>{t('wallet.modal.duration')}</Typography.Text>
							<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
								{hourDifference > 1
									? t('wallet.modal.hours', { count: hourDifference })
									: t('wallet.modal.hour', { count: hourDifference })}
							</Typography.Text>
						</View>
						<View style={styles.detailsSession}>
							<Typography.Text fontSize={16}>{t('wallet.modal.type')}</Typography.Text>
							<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
								{dataSessionModalize?.seats > 1
									? t('wallet.modal.teamUp', { count: dataSessionModalize?.seats })
									: t('wallet.modal.private')}
							</Typography.Text>
						</View>
					</View>

					<View style={styles.containerValueSession}>
						<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
							{t('wallet.modal.valueCharged')}
						</Typography.Text>

						<View style={styles.priceSession}>
							{dataSessionModalize?.price === 0 ? (
								<Typography.H1>{t('wallet.modal.free')}</Typography.H1>
							) : (
								<Typography.H1>R$ {formatPrice(dataSessionModalize?.price)}</Typography.H1>
							)}
						</View>
					</View>
					<View style={styles.containerButtonShare}>
						<SessionReceipt
							textButton={t('wallet.modal.shareButton')}
							seats={dataSessionModalize?.seats}
							price={dataSessionModalize?.price}
							playerNickname={dataSessionModalize?.pro_player_nickname}
							logoGame={dataSessionModalize?.game_logo}
							hourDifference={hourDifference}
							daySession={daySession}
							hourSession={hourSession}
						/>
					</View>
				</View>
			</Modalize>

			<Modalize
				modalizeRef={modalizeSessionBuy}
				height={heightModalize - 150}
				maxHeight={heightModalize - 150}
				HeaderComponent={
					<View style={styles.headerModalizeHistory}>
						<Typography.H1 style={styles.textTitleHistoryModalize}>
							{t('wallet.modal.sessionSale')}
						</Typography.H1>
						<Typography.Text style={styles.textDateHistoryModalize}>
							{dayjs(dataSessionModalize?.date).format('DD MMM YYYY - H:mm')}
						</Typography.Text>
					</View>
				}>
				<View style={{ paddingHorizontal: 15, marginTop: 2, marginBottom: 30 }}>
					<View style={styles.containerInfoSession}>
						<View style={styles.detailsSession}>
							<Typography.Text>{t('wallet.modal.game')}</Typography.Text>
							<View style={{ height: 45 }}>
								<SvgUri width={70} height={40} uri={dataSessionModalize?.game_logo} />
							</View>
						</View>
						<View style={styles.detailsSession}>
							<Typography.Text fontSize={16}>{t('wallet.modal.time')}</Typography.Text>
							<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
								{hourSession}
							</Typography.Text>
						</View>
						<View style={styles.detailsSession}>
							<Typography.Text fontSize={16}>{t('wallet.modal.duration')}</Typography.Text>
							<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
								{hourDifference > 1
									? t('wallet.modal.hours', { count: hourDifference })
									: t('wallet.modal.hour', { count: hourDifference })}
							</Typography.Text>
						</View>
						<View style={styles.detailsSession}>
							<Typography.Text fontSize={16}>{t('wallet.modal.type')}</Typography.Text>
							<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
								{dataSessionModalize?.seats > 1
									? t('wallet.modal.teamUp', { count: dataSessionModalize?.seats })
									: t('wallet.modal.private')}
							</Typography.Text>
						</View>
					</View>

					<View style={[styles.containerValueSession, styles.totalCoinsSession]}>
						<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
							{t('wallet.modal.valueReceived')}
						</Typography.Text>

						<View style={styles.priceSession}>
							{dataSessionModalize?.price === 0 ? (
								<Typography.H1 fontSize={16}>{t('wallet.modal.free')}</Typography.H1>
							) : (
								<Typography.H1 fontSize={16}>
									R$ {formatPrice(dataSessionModalize?.price)}
								</Typography.H1>
							)}
						</View>
					</View>

					<View style={styles.containerPlayerInSession}>
						<View style={styles.containerAvatarNickPlayer}>
							<View style={styles.containerAvatarPlayer}>
								<Image
									style={styles.avatar}
									source={
										dataSessionModalize?.player?.player_avatar
											? {
													uri: dataSessionModalize?.player?.player_avatar
												}
											: require('../../assets/images/placeholderAvatar.png')
									}
								/>
							</View>
							<Typography.Text>@{dataSessionModalize?.player?.nickname}</Typography.Text>
						</View>

						<Angle style={styles.angle} fill={theme.colors.white_1} />
					</View>

					<View style={styles.containerButtonShare}>
						<SessionReceipt
							textButton={t('wallet.modal.shareButton')}
							seats={dataSessionModalize?.seats}
							price={dataSessionModalize?.price}
							playerNickname={dataSessionModalize?.pro_player_nickname}
							logoGame={dataSessionModalize?.game_logo}
							hourDifference={hourDifference}
							daySession={daySession}
							hourSession={hourSession}
						/>
					</View>
				</View>
			</Modalize>

			<Modalize
				modalizeRef={modalizeSessionCanceled}
				height={heightModalize - 150}
				maxHeight={heightModalize - 150}
				HeaderComponent={
					<View style={styles.headerModalizeHistory}>
						<Typography.H1 style={styles.textTitleHistoryModalize}>
							{t('wallet.modal.refundedPurchase')}
						</Typography.H1>
						<Typography.Text style={styles.textDateHistoryModalize}>
							{dayjs(dataSessionModalize?.date).format('DD MMM YYYY - H:mm')}
						</Typography.Text>
					</View>
				}>
				<View style={{ paddingHorizontal: 15, marginTop: 2, marginBottom: 30 }}>
					<View style={styles.containerInfoSession}>
						<View style={styles.detailsSession}>
							<Typography.Text>{t('wallet.modal.game')}</Typography.Text>
							<View style={{ height: 45 }}>
								<SvgUri width={70} height={40} uri={dataSessionModalize?.game_logo} />
							</View>
						</View>
						<View style={styles.detailsSession}>
							<Typography.Text fontSize={16}>{t('wallet.modal.time')}</Typography.Text>
							<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
								{hourSession}
							</Typography.Text>
						</View>
						<View style={styles.detailsSession}>
							<Typography.Text fontSize={16}>{t('wallet.modal.duration')}</Typography.Text>
							<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
								{hourDifference > 1
									? t('wallet.modal.hours', { count: hourDifference })
									: t('wallet.modal.hour', { count: hourDifference })}
							</Typography.Text>
						</View>
						<View style={styles.detailsSession}>
							<Typography.Text fontSize={16}>{t('wallet.modal.type')}</Typography.Text>
							<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
								{dataSessionModalize?.seats > 1
									? t('wallet.modal.teamUp', { count: dataSessionModalize?.seats })
									: t('wallet.modal.private')}
							</Typography.Text>
						</View>
					</View>

					<View style={[styles.containerValueSession, styles.totalCoinsSession]}>
						<Typography.Text fontSize={16} style={styles.infoDetailsSession}>
							{t('wallet.modal.valueReceived')}
						</Typography.Text>

						<View style={styles.priceSession}>
							{dataSessionModalize?.price === 0 ? (
								<Typography.H1 fontSize={16}>{t('wallet.modal.free')}</Typography.H1>
							) : (
								<Typography.H1 fontSize={16}>
									R$ {formatPrice(dataSessionModalize?.price)}
								</Typography.H1>
							)}
						</View>
					</View>

					<View style={styles.containerPlayerInSession}>
						<View style={styles.containerAvatarNickPlayer}>
							<View style={styles.containerAvatarPlayer}>
								<Image
									style={styles.avatar}
									source={
										dataSessionModalize?.player?.player_avatar
											? {
													uri: dataSessionModalize?.player?.player_avatar
												}
											: require('../../assets/images/placeholderAvatar.png')
									}
								/>
							</View>
							<Typography.Text>@{dataSessionModalize?.pro_player_nickname}</Typography.Text>
						</View>

						<Angle style={styles.angle} fill={theme.colors.white_1} />
					</View>

					<View style={styles.containerButtonShare}>
						<SessionReceipt
							textButton={t('wallet.modal.shareButton')}
							seats={dataSessionModalize?.seats}
							price={dataSessionModalize?.price}
							playerNickname={dataSessionModalize?.pro_player_nickname}
							logoGame={dataSessionModalize?.game_logo}
							hourDifference={hourDifference}
							daySession={daySession}
							hourSession={hourSession}
						/>
					</View>
				</View>
			</Modalize>

			<ModalizeHelpValueBalance
				modalizeRef={modalizeHelpValueBalance}
				title={t('wallet.help.pendingBalanceTitle')}
				description={t('wallet.help.pendingBalanceDescription')}
			/>

			<ModalizeHelpValueBalance
				modalizeRef={modalizeHelpValueProcessing}
				title={t('wallet.help.processingBalanceTitle')}
				description={t('wallet.help.processingBalanceDescription')}
			/>
			<ModalizePaymentConfirmed
				modalizeConfirmTransfer={modalizeConfirmTransfer}
				dataTransfer={withdraw}
			/>

			<ModalMessageError
				messageErrorModal={messageErrorModal}
				isModalConfirmedVisibleError={isModalConfirmedVisibleError}
				closeModalError={() => setIsModalConfirmedVisibleError(false)}
			/>
		</View>
	);
}
