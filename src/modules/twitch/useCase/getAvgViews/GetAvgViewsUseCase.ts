import { ITwitchDevelopers } from "@shared/container/providers/TwitchDevelopers/ITwitchDevelopers";
import { forEach } from "lodash";
import { inject, injectable } from "tsyringe";

@injectable()
export class GetAvgViewsUseCase {
  constructor(
    @inject("TwitchDevelopers") private twitchDevelopers: ITwitchDevelopers // private twitchDevelopers: ITwitchDevelopers
  ) {}

  async execute(twitchChannel: string): Promise<any> {
    const credentials = await this.twitchDevelopers.clientCredentials();
    const { access_token } = credentials;
    const { id } = await this.twitchDevelopers.getUserData(
      access_token,
      twitchChannel
    );

    const stream = await this.twitchDevelopers.getAvgViews(access_token, id);

    let avg_views = 0;

    stream.data.forEach((element) => {
      avg_views += element.view_count;
    });

    return Math.round(avg_views / stream.data.length);
  }
}
