import { Request, Response } from "express";
import { container } from "tsyringe";
import { GetAvgViewsUseCase } from "./GetAvgViewsUseCase";

export class GetAvgViewsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { twitchChannel } = request.body;

    const getAvgViewsUseCase = container.resolve(GetAvgViewsUseCase);

    const following = await getAvgViewsUseCase.execute(twitchChannel);

    return response.json(following);
  }
}
