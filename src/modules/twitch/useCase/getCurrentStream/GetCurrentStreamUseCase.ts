import { ITwitchDevelopers } from "@shared/container/providers/TwitchDevelopers/ITwitchDevelopers";
import { container, inject, injectable } from "tsyringe";

@injectable()
export class GetCurrentStreamUseCase {
  constructor(
    @inject("TwitchDevelopers") private twitchDevelopers: ITwitchDevelopers // private twitchDevelopers: ITwitchDevelopers
  ) {}

  async execute(twitchChannel: string): Promise<any> {
    const credentials = await this.twitchDevelopers.clientCredentials();
    const { access_token } = credentials;
    const { id } = await this.twitchDevelopers.getUserData(
      access_token,
      twitchChannel
    );
    console.log("1");
    const stream = await this.twitchDevelopers.getCurrentStream(
      access_token,
      id
    );
    console.log(stream);

    return stream;
  }
}
