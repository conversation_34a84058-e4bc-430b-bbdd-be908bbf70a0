import { container } from "tsyringe";
import { Request, Response } from "express";
import { GetCurrentStreamUseCase } from "./GetCurrentStreamUseCase";

export class GetCurrentStreamController {
  async handle(request: Request, response: Response): Promise<Response> {
    console.log(request.body);
    const { twitchChannel } = request.body;

    const getCurrentStreamUseCase = container.resolve(GetCurrentStreamUseCase);

    const userData = await getCurrentStreamUseCase.execute(twitchChannel);

    return response.json(userData);
  }
}
