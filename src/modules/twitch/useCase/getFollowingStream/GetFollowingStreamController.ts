import { Request, Response } from "express";
import { container } from "tsyringe";
import { GetFollowingStreamUseCase } from "./GetFollowingStreamUseCase";

export class GetFollowingStreamController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { twitchChannel } = request.body;

    const getFollowingStreamUseCase = container.resolve(
      GetFollowingStreamUseCase
    );

    const following = await getFollowingStreamUseCase.execute(twitchChannel);

    return response.json(following);
  }
}
