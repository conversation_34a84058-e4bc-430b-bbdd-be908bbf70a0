import { Request, Response } from "express";
import { container } from "tsyringe";
import { GetUserDataUseCase } from "./GetUserDataUseCase";

export class GetUserDataController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { twitchChannel } = request.body;

    const getUserDataUseCase = container.resolve(GetUserDataUseCase);

    const userData = await getUserDataUseCase.execute(twitchChannel);

    return response.json(userData);
  }
}
