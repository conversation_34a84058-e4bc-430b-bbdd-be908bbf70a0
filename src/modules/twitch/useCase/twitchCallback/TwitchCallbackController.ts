import { Request, Response } from "express";
import { container } from "tsyringe";
import { TwitchCallbackUseCase } from "./TwitchCallbackUseCase";

export class TwitchCallbackController {
  async handle(request: Request, response: Response): Promise<void> {
    const { code, state } = request.query;

    const twitchCallbackUseCase = container.resolve(TwitchCallbackUseCase);
    const id = state
    const following = await twitchCallbackUseCase.execute(
      String(code),
      Number(id),
      response
    );
  }
}
