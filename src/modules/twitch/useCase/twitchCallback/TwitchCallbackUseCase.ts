import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ITwitchDevelopers } from "@shared/container/providers/TwitchDevelopers/ITwitchDevelopers";
import axios from "axios";
import { Response } from "express";
import { container, inject, injectable } from "tsyringe";

@injectable()
export class TwitchCallbackUseCase {
  constructor(
    @inject("TwitchDevelopers") private twitchDevelopers: ITwitchDevelopers, // private twitchDevelopers: ITwitchDevelopers
    @inject("PlayerRepository") private playerRepository: IPlayerRepository // private twitchDevelopers: ITwitchDevelopers
  ) {}

  async execute(code: string, id: number, response: Response): Promise<void> {
    try {
      const tokenResponse = await axios.post(
        "https://id.twitch.tv/oauth2/token",
        null,
        {
          params: {
            client_id: process.env.TWITCH_CLIENT_ID,
            client_secret: process.env.TWITCH_CLIENT_SECRET,
            code,
            grant_type: "authorization_code",
            redirect_uri: process.env.TWITCH_REDIRECT_URI,
          },
        }
      );

      const { access_token, refresh_token } = tokenResponse.data;

      const userData = await this.twitchDevelopers.getUserData(access_token);
      const avgViews = await this.twitchDevelopers.getAvgViews(
        access_token,
        userData.id
      );
      const followingsStream = await this.twitchDevelopers.getFollowingStream(
        access_token,
        userData.id
      );
      let avg_views = 0;

      avgViews.data.forEach((element) => {
        avg_views += element.view_count;
      });

      await this.playerRepository.updateById({
        id,
        nickname_twitch: userData.login,
        total_views_twitch:
          avgViews.data.length === 0
            ? 0
            : Math.round(avg_views / avgViews.data.length),
        subscribe_twitch: followingsStream.total,
      });

      response.redirect(`knowbie://auth/callback`);
    } catch (error) {
      console.error(error);
      response.status(500).send("Authentication failed");
    }
  }
}
