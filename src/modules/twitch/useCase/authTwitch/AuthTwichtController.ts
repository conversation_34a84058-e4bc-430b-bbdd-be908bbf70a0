import { Request, Response } from "express";

export class AuthTwichtController {
  async handle(request: Request, response: Response): Promise<void> {
    const { id } = request.query;
    const scope = "user:read:email";
    const redirectUri = `https://id.twitch.tv/oauth2/authorize?response_type=code&client_id=${process.env.TWITCH_CLIENT_ID}&redirect_uri=${process.env.TWITCH_REDIRECT_URI}&scope=${scope}&state=${id}`;
    response.redirect(redirectUri);
  }
}
