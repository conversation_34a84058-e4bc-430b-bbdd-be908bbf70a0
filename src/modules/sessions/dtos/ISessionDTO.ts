import { TPlayerStats } from "@modules/players/dtos/IPlayerDTO";

import {
  Game,
  Player_stats,
  Seat,
  Session,
  Cancelation_option,
} from ".prisma/client";

export type ISession = Session & {
  players_seats: Seat[];
};

export type listSessions = Session & {
  Game: Game;
  players_seats: Seat[];
  avaliation?: number;
  avaliations?: {
    average: number;
    tags: { name: string; quantity: number }[];
    comments: { points: number;  }[];
  };
  Input?: {
    id?: number;
    description?: string;
  };
  Platform?: {
    id?: number;
    description?: string;
  };
  Player: {
    avatar: string;
    nickname: string;
    player_stats?: TPlayerStats[];
  };
};

export type pagination = {
  info: {
    count: number;
    pages: number;
    next: number;
    prev: number;
  };
  result: listSessions[];
};

export type cancelationOption = Cancelation_option;

export interface ISessionDTO {
  id: number;
  start_time: Date;
  end_time: Date;
  price: number;
  seats: number;
  available_seats: number;
  pro_player_id: number;
  pro_player_avatar: string;
  pro_player_nickname: string;
  communication_link?: string;
  platform_id?: number;
  input_id?: number;
  input_description?: string;
  platform_description?: string;
  game_name: string;
  game_logo: string;
  game_banner: string;
  game_id: number;
  avaliation?: number | string;
  player_stats: {
    name: string;
    slug: string;
    value: number;
  }[];
  canceledAt?: Date;
  playerHasSeat?: Seat[];
}

export interface ILastSessionDTO {
  id: number;
  price: number;
  start_time: Date;
  end_time: Date;
  isFinished: boolean;
  isConfirmedAfterSession: boolean;
  seats: number;
  points: number;
  completedDate: Date;
  Player: {
    avatar: string;
    id: number;
    nickname: string;
  };
  Game: {
    name: string;
    id: number;
    logo: string;
  };
}

export type IlistEndedSessionsDTO = Session & {
  players_seats: Seat[];
};
