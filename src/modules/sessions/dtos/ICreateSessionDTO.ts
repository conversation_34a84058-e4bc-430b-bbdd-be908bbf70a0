export interface ICreateSessionDTO {
  start_time: Date;
  end_time: Date;
  game_id: number;
  player_id: number;
  communication_link?: string;
  platform_id?: number;
  input_id?: number;
  price: number;
  seats: number;
  session_base_id?: number;
  repeat?: {
    freq: string;
    interval: number;
    byweekday?: string[] | null;
    until?: Date | null;
    count?: number | null;
  };
  type_session: "PLAY" | "TIPS";
}
