export interface IListSessionsDTO {
  page: number;
  limit: number;
  game_id?: number;
  price?: {
    max?: number;
    min?: number;
  };
  seats?: Array<number>;
  communication_link?: string;
  platform_id?: number;
  input_id?: number;
  // date?: string[];
  date?: string;
  time?: string;
  duration?: number;
  avaliation?: {
    max?: number;
    min?: number;
  };
  order?: string;
  available?: boolean;
  player?: string;
  player_id?: number;
  start_date?: string;
  end_date?: string;
  tags?: string[];
}
