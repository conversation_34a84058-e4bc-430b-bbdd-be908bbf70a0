import { TStatement } from "@modules/players/dtos/IPlayerDTO";

import {
  Rating,
  Session,
  Seat,
  Recurring_sessions,
  Tag,
  Promoted_session,
  Session_metrics,
  Game,
  Disputes,
  Player,
} from ".prisma/client";

import { ICreateSessionDTO } from "../dtos/ICreateSessionDTO";
import { IHistoricChatDTO } from "../dtos/IHistoricChatDTO";
import { IRateSessionDTO } from "../dtos/IRateSessionDTO";
import { IScheduleSessionDTO } from "../dtos/IScheduleSessionDTO";
import {
  IlistEndedSessionsDTO,
  cancelationOption,
  listSessions,
} from "../dtos/ISessionDTO";
import { IUpdateSessionDTO } from "../dtos/IUpdateSessionDTO";
import { IFullSeatDTO } from "../dtos/IFullSeatDTO";
import { Sessions } from "aws-sdk/clients/lightsail";
import { Chargeback, Invite_Discord, Payment_info, chargeback_dispute } from "@prisma/client";

export interface ISessionRepository {
  create(data: ICreateSessionDTO): Promise<any>;
  scheduleSession(data: IScheduleSessionDTO): Promise<Seat>;
  findSeat(player_id: number, session_id: number): Promise<Seat>;
  findSeatsBySession(session_id: number): Promise<
    (Seat & {
      Session: Session & {
        Player: Player;
      };
      Player: Player;
    })[]
  >;
  findSeatsByPlayer(player_id: number): Promise<Seat[]>;
  findById(session_id: number): Promise<listSessions>;
  findSeatBySessionAndPlayer(
    session_id: number,
    player_id: number
  ): Promise<IFullSeatDTO>;
  findSessionsScheduledbyPlayerId(player_id: number): Promise<listSessions[]>;
  finishSession(session_id: number): Promise<void>;
  updateSeat(data: {
    seat_id: number;
    isConfirmed?: boolean;
    hasDispute?: boolean;
    hasChargeback?: boolean;
    isFinished?: boolean;
    isConfirmedAfterSession?: boolean;
  }): Promise<void>;
  deleteSeat(seat_id: number): Promise<any>;
  listAll(): Promise<listSessions[]>;
  listSessionsNowByPlayer(player_id:number): Promise<listSessions[]>;
  listSessionsNowByMentor(mentor_id:number): Promise<listSessions[]>;
  listCancelationOptions(): Promise<cancelationOption[]>;
  listSessionNow(): Promise<listSessions[]>;
  listActiveSessions(
    limit?: number,
    offset?: number,
    game_id?: number,
    order?: string,
    price?: {
      max?: number;
      min?: number;
    },
    time?: string,
    date?: string,
    player?: string,
    player_id?: number
  ): Promise<listSessions[]>;
  listSessionsWithRecurrence(session_base_id: number): Promise<listSessions[]>;
  listSessionsByPlayer(player_id: number): Promise<
    (Session & {
      players_seats: Seat[];
    })[]
  >;
  listHighlightsSessions(): Promise<
    (Promoted_session & {
      Session: Session & {
        Game: Game;
        players_seats: Seat[];
        Player: {
          avatar: string;
          nickname: string;
        };
      };
    })[]
  >;
  rateSession(data: IRateSessionDTO): Promise<Rating>;
  findAllRatings(): Promise<
    (Rating & {
      Session: Session & {
        Game: Game;
      };
    })[]
  >;
  findRatingsBySessionId(session_id: number): Promise<Rating[]>;
  scheduleJob(expire_date: Date, seat_id: number): Promise<void>;
  deleteJob(seat_id: number): Promise<void>;
  updateSession(data: IUpdateSessionDTO): Promise<Session>;
  deleteSession(session_id: number): Promise<void>;
  deleteSessionWithRecurrence(session_base_id: number): Promise<void>;
  saveRecurrence(
    session_id: number,
    pattern: string,
    startDate: Date,
    nextGenDate?: Date,
    untilDate?: Date
  ): Promise<void>;
  findRecurrence(recurrence_id: number): Promise<Recurring_sessions>;
  findRecurrenceBySessionId(session_id: number): Promise<Recurring_sessions>;
  deleteRecurrence(recurrence_id: number): Promise<void>;
  updateRecurrence(data: {
    recurrence_id: number;
    session_id?: number;
    pattern?: string;
    nextGenDate?: Date;
    untilDate?: Date;
  }): Promise<void>;
  listRecurringSessions(): Promise<Recurring_sessions[]>;
  createManySessions(data): Promise<any>;
  findStatementsBySession(session_id: number): Promise<TStatement[]>;
  listTags(): Promise<Tag[]>;
  promoteSession(data: {
    session_id: number;
    until: Date;
    days_to_expire: number;
    price: number;
    reach_max?: number;
    reach_min?: number;
    active?: boolean;
    payment_type: string;
    price_per_day: number;
    payment_info_id?: number;
  }): Promise<Promoted_session>;
  listPromotedSessionsByPlayer(player_id: number): Promise<
    (Promoted_session & {
      Session: Session & {
        Game: Game;
      };
    })[]
  >;
  listPromotedSessions(): Promise<
    (Promoted_session & {
      Session: Session & {
        Game: Game;
      };
    })[]
  >;
  listAllPromotedSession(): Promise<Promoted_session[]>;
  finishPromoteSession(id:number): Promise<Promoted_session>;
  confirmPromoteSession(id:number): Promise<Promoted_session>;
  findPromotedSessionBySessionId(session_id: number): Promise<Promoted_session>;
  findPromotedSessionById(
    promoted_session_id: number
  ): Promise<Promoted_session>;
  updatePromotedSession(data: {
    promoted_session_id: number;
    until?: Date;
    days_to_expire?: number;
    active: boolean;
  }): Promise<Promoted_session>;
  listMetricsBySession(session_id: number): Promise<Session_metrics[]>;
  createMetrics(data: {
    session_id: number;
    player_id: number;
    clicks?: number;
    reach?: number;
    views?: number;
    checkouts?: number;
    date?: Date;
  }): Promise<Session_metrics>;
  updateMetrics(data: {
    clicks?: number;
    reach?: number;
    views?: number;
    checkouts?: number;
    session_metrics_id: number;
  }): Promise<Session_metrics>;
  openDispute(data: {
    session_id: number;
    player_id: number;
    description: string;
    attachment?: string;
    status: string;
  }): Promise<any>;
  updateDispute(data: {
    dispute_id: number;
    user_id?: number;
    description?: string;
    attachment?: string;
    status?: string;
    mentor_response_date?: Date;
    decision?: string;
    winner_id?: number;
    resolution_date?: Date;
    moderator_description?: string;
  }): Promise<Disputes>;
  findDisputeById(dispute_id: number): Promise<
    Disputes & {
      Session: Session;
    }
  >;
  listDisputes(): Promise<
    (Disputes & {
      Player: Player;
    })[]
  >;
  createInviteDiscord(session_id:number, invite:string, channelId:string):Promise<Invite_Discord>;
  createLinkDiscord(channelId): Promise<any>;
  getInviteDiscord(session_id:number):Promise<Invite_Discord>;
  listChargeback(): Promise<Chargeback[]>;
  findChargebackById(chargeback_id:number): Promise<Chargeback>;
  sendAnswerChargeback(data:{
    chargeback_id:number, 
    mentor_response:string,
    mentor_attachment?:string,
    mentor_response_date: Date
  }): Promise<chargeback_dispute>;
  assignChargeback(data:{
    chargeback_id:number, 
    user_id:number,
  }): Promise<chargeback_dispute>;
  requestAnswerChargeback(data:{
    chargeback_id:number, 
    mentor_requested_response:boolean, 
    mentor_response_date: Date
  }): Promise<chargeback_dispute>;
  findChargebackInfoById(chargeback_id:number): Promise<chargeback_dispute>;
  findPaymentInfoByPaymentId(external_payment_id:bigint): Promise<Payment_info>;
  findSeatById(seat_id:number): Promise<Seat>;
  listAllDisputes(): Promise<
    (Disputes & { Player: Player; Session: Session })[]
  >;
  listDisputesByMentorId(): Promise<
    (Disputes & { Player: Player; Session: Session })[]
  >;
  cancelSession(
    session_id: number,
    cancelation_option_id: number,
    player_id: number,
    motive_field: string
  ): Promise<Session>;
  findLastSessionWithoutRating(user_id: number): Promise<any>;
  historicChat(
    session_id: number,
    limit: number,
    page: number
  ): Promise<IHistoricChatDTO[]>;
  countMessagesChatById(session_id: number): Promise<number>;
  findFullPastSessionsByPlayerId(
    player_id: number,
    page: number,
    limit: number
  ): Promise<listSessions[]>;
  findSessionsEndedBefore(time: Date): Promise<IlistEndedSessionsDTO[]>;
  notConfirmSession(
    seat: Seat,
    sessionOwner: Player,
    statementBySession: TStatement,
    ownerStatementBySession: number
  ): Promise<any>;
  getSessionPaymentStatus(session_id: number, player_id: number): Promise<any>;
}
