import { inject, injectable } from "tsyringe";

import { TStatement } from "@modules/players/dtos/IPlayerDTO";
import { ICreateSessionDTO } from "@modules/sessions/dtos/ICreateSessionDTO";
import { IHistoricChatDTO } from "@modules/sessions/dtos/IHistoricChatDTO";
import { IRateSessionDTO } from "@modules/sessions/dtos/IRateSessionDTO";
import { IScheduleSessionDTO } from "@modules/sessions/dtos/IScheduleSessionDTO";
import {
  IlistEndedSessionsDTO,
  cancelationOption,
  listSessions,
} from "@modules/sessions/dtos/ISessionDTO";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IUpdateSessionDTO } from "@modules/sessions/dtos/IUpdateSessionDTO";

import {
  PrismaClient,
  Player,
  Rating,
  Session,
  Seat,
  Recurring_sessions,
  Tag,
  Promoted_session,
  Session_metrics,
  Game,
  Disputes,
} from ".prisma/client";
import { MercadoPagoProvider } from "@shared/container/providers/PaymentGatewayProvider/implementations/MercadoPagoProvider";
import { IFullSeatDTO } from "@modules/sessions/dtos/IFullSeatDTO";
import { Chargeback, Invite_Discord, Payment_info, chargeback_dispute } from "@prisma/client";
import axios from "axios";

@injectable()
export class SessionRepository implements ISessionRepository {
  constructor(
    @inject("PrismaClient") private prisma: PrismaClient,
    @inject("MercadoPagoProvider")
    private mercadoPagoProvider: MercadoPagoProvider
  ) {}

  async create({
    start_time,
    end_time,
    game_id,
    player_id,
    communication_link,
    input_id,
    platform_id,
    price,
    seats,
    session_base_id,
    type_session,
  }: ICreateSessionDTO): Promise<any> {
    const data: any = {
      start_time,
      end_time,
      price,
      seats,
      communication_link,
      type_session,
      Game: {
        connect: {
          id: game_id,
        },
      },
      Player: {
        connect: {
          id: player_id,
        },
      },
    };
  
    if (input_id) {
      data.Input = {
        connect: {
          id: input_id,
        },
      };
    }
  
    if (platform_id) {
      data.Platform = {
        connect: {
          id: platform_id,
        },
      };
    }
  
    if (session_base_id) {
      data.session_base = {
        connect: {
          id: session_base_id,
        },
      };
    }
  
    const session = await this.prisma.session.create({
      data,
    });
  
    return session;
  }
  

  async updateSession(data: IUpdateSessionDTO): Promise<Session> {
    const session = await this.prisma.session.update({
      where: {
        id: data.session_id,
      },
      data: {
        start_time: data.start_time,
        end_time: data.end_time,
        price: data.price,
        seats: data.seats,
        game_id: data.game_id,
        session_base_id: data.session_base_id,
        communication_link: data.communication_link,
        input_id: data.input_id,
        platform_id: data.platform_id,
        type_session: data.type_session,
      },
    });

    return session;
  }

  async deleteSession(session_id: number): Promise<void> {
    await this.prisma.session.update({
      where: {
        id: session_id,
      },
      data: {
        deletedAt: new Date(),
      },
    });
  }

  async deleteSessionWithRecurrence(session_base_id: number): Promise<void> {
    await this.prisma.session.deleteMany({
      where: {
        session_base_id,
      },
    });
  }

  async findById(session_id: number): Promise<listSessions> {
    const session = await this.prisma.session.findFirst({
      where: {
        id: session_id,
        deletedAt: null,
      },
      include: {
        players_seats: {
          select: {
            id: true,
            isConfirmed: true,
            isFinished: true,
            hasDispute: true,
            hasChargeback:true,
            isConfirmedAfterSession: true,
            player_id: true,
            session_id: true,
            Player: {
              select: {
                avatar: true,
                nickname: true,
              },
            },
            Payment_info: {
              select: {
                status: true,
                payment_from_external: true,
                payment_from_wallet: true,
                player_id: true,
                payment_url: true,
              },
            },
          },
        },
        Game: true,
        Player: {
          select: {
            avatar: true,
            nickname: true,
            player_stats: {
              select: {
                value: true,
                Game_stats: {
                  select: {
                    name: true,
                    slug: true,
                    game_id: true,
                  },
                },
              },
            },
          },
        },
        Input: true,
        Platform: true,
      },
    });

    return session;
  }

  async findSessionsScheduledbyPlayerId(
    player_id: number
  ): Promise<listSessions[]> {
    const session = await this.prisma.session.findMany({
      where: {
        player_id,
        start_time: {
          gte: new Date(),
        },
        deletedAt: null,
      },
      include: {
        Game: true,
        players_seats: true,
        Player: {
          select: {
            avatar: true,
            nickname: true,
          },
        },
      },
    });

    return session;
  }

  async scheduleSession({
    player_id,
    session_id,
  }: IScheduleSessionDTO): Promise<Seat> {
    const session = await this.prisma.seat.create({
      data: {
        Player: {
          connect: {
            id: player_id,
          },
        },
        Session: {
          connect: {
            id: session_id,
          },
        },
      },
    });

    return session;
  }

  async listSessionsByPlayer(player_id: number): Promise<
    (Session & {
      players_seats: Seat[];
    })[]
  > {
    const sessions = await this.prisma.session.findMany({
      where: {
        Player: {
          id: player_id,
        },
        isActive: true,
        deletedAt: null,
      },
      include: {
        players_seats: true,
        Input: true,
        Platform: true,
      },
    });

    return sessions;
  }

  async listActiveSessions(
    limit: number,
    offset: number,
    game_id: number,
    order: string,
    price: {
      max: number;
      min: number;
    },
    time: string,
    date: string,
    player: string,
    player_id: number
  ): Promise<listSessions[]> {
    const sessions = await this.prisma.session.findMany({
      include: {
        rating: true,
        Game: true,
        players_seats: true,
        Player: {
          select: {
            id: true,
            avatar: true,
            nickname: true,
            player_stats: {
              select: {
                value: true,
                Game_stats: {
                  select: {
                    name: true,
                    slug: true,
                    game_id: true,
                  },
                },
              },
            },
            // Rating: true,
          },
        },
        Input: true,
        Platform: true,
      },
      where: {
        isActive: true,
        deletedAt: null,
        game_id: game_id || undefined,
        start_time: {
          gte:
            date && time
              ? new Date(`${date} ${time}`)
              : date
              ? new Date(date)
              : new Date(),
          lte:
            date && time
              ? new Date(`${date} ${time}`)
              : date
              ? new Date(`${date} 23:59`)
              : time
              ? undefined
              : undefined,
        },
        price: {
          gte: price?.min ? price.min : undefined,
          lte: price?.max ? price.max : undefined,
        },
        canceledAt: null,
        Player: {
          NOT: {
            id: player_id || undefined,
          },
          nickname: {
            contains: player,
          },
        },
        players_seats: {
          none: {
            player_id: player_id || undefined,
          },
        },
      },

      orderBy: [
        {
          start_time: "asc",
        },
        {
          price:
            order === "price_low" ? "asc" : "price_high" ? "desc" : undefined,
        },
        {
          Player: {
            name: order === "name" ? "asc" : undefined,
          },
        },
      ],
      // take: limit,
      // skip: (offset - 1) * limit,
    });

    return sessions;
  }

  async listAll(): Promise<listSessions[]> {
    const sessions = await this.prisma.session.findMany({
      where: {
        deletedAt: null,
      },
      include: {
        Game: true,
        players_seats: true,
        Player: {
          select: {
            avatar: true,
            nickname: true,
          },
        },
        Input: true,
        Platform: true,
      },
    });

    return sessions;
  }
  async listSessionsNowByPlayer(player_id:number): Promise<listSessions[]> {
    const now = new Date();
    const sessions = await this.prisma.session.findMany({
      where: {
        players_seats: {
          some: {
            player_id: player_id,
          },
        },
        start_time: {
          lte: now,
        },
        end_time: {
          gte: now,
        },
      },
      include: {
        Game: true,
        players_seats: true,
        Player: {
          select: {
            avatar: true,
            nickname: true,
          },
        },
        Input: true,
        Platform: true,
      },
    });

    return sessions;
  }
  async listSessionsNowByMentor(mentor_id:number): Promise<listSessions[]> {
    const now = new Date();
    const sessions = await this.prisma.session.findMany({
      where: {
        player_id: mentor_id,
        start_time: {
          lte: now,
        },
        end_time: {
          gte: now,
        },
      },
      include: {
        Game: true,
        players_seats: {
          include: {
            Player: {
              select: {
                avatar: true,
                nickname: true,
              },
            },
          },
        },
        Player: {
          select: {
            avatar: true,
            nickname: true,
          },
        },
        Input: true,
        Platform: true,
      },
    });

    return sessions;
  }

  async listSessionNow(): Promise<listSessions[]>{
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    const sessions = await this.prisma.session.findMany({
      where: {
        start_time: {
          lte: now,
        },
        end_time: {
          gte: yesterday,
        },
        isActive:true
      },
      include: {
        Game: true,
        players_seats: {
          include: {
            Player: {
              select: {
                avatar: true,
                nickname: true,
              },
            },
          },
        },
        Player: {
          select: {
            avatar: true,
            nickname: true,
          },
        },
        Input: true,
        Platform: true,
      },
    });

    return sessions;
  }

  async listHighlightsSessions(): Promise<
    (Promoted_session & {
      Session: Session & {
        Game: Game;
        players_seats: Seat[];
        Player: {
          avatar: string;
          nickname: string;
        };
      };
    })[]
  > {
    const sessions = await this.prisma.promoted_session.findMany({
      where: {
        active: true,
      },
      include: {
        Session: {
          include: {
            Game: true,
            players_seats: true,
            Player: {
              select: {
                avatar: true,
                nickname: true,
              },
            },
            Input: true,
            Platform: true,
          },
        },
      },
    });
    return sessions;
  }

  async rateSession({
    description,
    player_id,
    points,
    session_id,
    tags,
    isAnonymous,
  }: IRateSessionDTO): Promise<Rating> {
    let rating: Rating;

    if (tags && description) {
      rating = await this.prisma.rating.create({
        data: {
          description,
          points,
          isAnonymous,
          Tag: {
            connect: tags.map((tag) => ({ id: tag })),
          },
          Session: {
            connect: {
              id: session_id,
            },
          },
          Player: {
            connect: {
              id: player_id,
            },
          },
        },
      });

      return rating;
    }

    if (tags) {
      rating = await this.prisma.rating.create({
        data: {
          points,
          Tag: {
            connect: tags.map((tag) => ({ id: tag })),
          },
          Player: {
            connect: {
              id: player_id,
            },
          },
          Session: {
            connect: {
              id: session_id,
            },
          },
        },
      });

      return rating;
    }

    if (description) {
      rating = await this.prisma.rating.create({
        data: {
          description,
          points,
          isAnonymous,
          Player: {
            connect: {
              id: player_id,
            },
          },
          Session: {
            connect: {
              id: session_id,
            },
          },
        },
      });

      return rating;
    }

    rating = await this.prisma.rating.create({
      data: {
        points,
        Player: {
          connect: {
            id: player_id,
          },
        },
        Session: {
          connect: {
            id: session_id,
          },
        },
      },
    });

    return rating;
  }

  async findAllRatings(): Promise<
    (Rating & {
      Session: Session & {
        Game: Game;
      };
    })[]
  > {
    const ratings = await this.prisma.rating.findMany({
      include: {
        Session: {
          include: {
            Game: true,
          },
        },
      },
    });

    return ratings;
  }

  async findRatingsBySessionId(session_id: number): Promise<Rating[]> {
    const rating = await this.prisma.rating.findMany({
      where: {
        session_id,
      },
    });

    return rating;
  }

  async findSeat(player_id: number, session_id: number): Promise<Seat> {
    const seat = await this.prisma.seat.findFirst({
      where: {
        session_id,
        player_id,
      },
    });

    return seat;
  }

  async findSeatsByPlayer(player_id: number): Promise<Seat[]> {
    const seat = await this.prisma.seat.findMany({
      where: {
        player_id,
      },
    });

    return seat;
  }

  async findSeatsBySession(session_id: number): Promise<
    (Seat & {
      Session: Session & {
        Player: Player;
      };
      Player: Player;
    })[]
  > {
    const seats = await this.prisma.seat.findMany({
      where: {
        session_id,
      },
      include: {
        Session: {
          include: {
            Player: true,
          },
        },
        Player: true,
      },
    });

    return seats;
  }

  async updateSeat(data: {
    seat_id: number;
    isConfirmed?: boolean;
    hasDispute?: boolean;
    hasChargeback?: boolean;
    isFinished?: boolean;
    isConfirmedAfterSession?: boolean;
  }): Promise<void> {
    if (data.isConfirmed && data.seat_id) {
      await this.deleteJob(data.seat_id);
    }

    await this.prisma.seat.update({
      data: {
        isConfirmed: data.isConfirmed,
        hasDispute: data.hasDispute,
        hasChargeback: data.hasChargeback,
        isFinished: data.isFinished,
        isConfirmedAfterSession: data.isConfirmedAfterSession,
      },
      where: {
        id: data.seat_id,
      },
    });
  }

  // TODO: move this method to Jobs Module
  async deleteJob(seat_id: number): Promise<void> {
    const job = await this.prisma.jobs.findFirst({
        where: {
            Seat: {
                id: seat_id,
            },
        },
    });

    if (job) {
        await this.prisma.jobs.delete({
            where: {
                id: job.id,
            },
        });
    }
  }


  async deleteSeat(seat_id: number): Promise<any> {
    await this.deleteJob(seat_id);

    const session = await this.prisma.seat.delete({
      where: {
        id: seat_id,
      },
    });

    return session;
  }

  // TODO: move this method to Jobs module
  async scheduleJob(expire_date: Date, seat_id: number): Promise<void> {
    await this.prisma.jobs.create({
      data: {
        expiresDate: expire_date,
        Seat: {
          connect: {
            id: seat_id,
          },
        },
      },
    });
  }

  async finishSession(session_id: number): Promise<void> {
    await this.prisma.session.update({
      data: {
        isActive: false,
      },
      where: {
        id: session_id,
      },
    });
  }

  async saveRecurrence(
    session_id: number,
    pattern: string,
    startDate: Date,
    nextGenDate?: Date,
    untilDate?: Date
  ): Promise<void> {
    await this.prisma.recurring_sessions.create({
      data: {
        pattern,
        startDate,
        nextGenDate,
        untilDate,
        session: {
          connect: {
            id: session_id,
          },
        },
      },
    });
  }

  async findRecurrence(recurrence_id: number): Promise<Recurring_sessions> {
    const recurrence = await this.prisma.recurring_sessions.findFirst({
      where: {
        id: recurrence_id,
      },
    });

    return recurrence;
  }

  async findRecurrenceBySessionId(
    session_id: number
  ): Promise<Recurring_sessions> {
    const recurrence = await this.prisma.recurring_sessions.findFirst({
      where: {
        session_id,
      },
    });

    return recurrence;
  }

  async deleteRecurrence(recurrence_id: number): Promise<void> {
    await this.prisma.recurring_sessions.delete({
      where: {
        id: recurrence_id,
      },
    });
  }

  async updateRecurrence(data: {
    recurrence_id: number;
    session_id?: number;
    pattern?: string;
    nextGenDate?: Date;
    untilDate?: Date;
  }): Promise<void> {
    await this.prisma.recurring_sessions.update({
      where: {
        id: data.recurrence_id,
      },
      data: {
        pattern: data.pattern,
        nextGenDate: data.nextGenDate,
        untilDate: data.untilDate,
        session_id: data.session_id,
      },
    });
  }

  async listRecurringSessions(): Promise<Recurring_sessions[]> {
    const recurrences = await this.prisma.recurring_sessions.findMany();
    return recurrences;
  }

  async createManySessions(data): Promise<any> {
    return this.prisma.session.createMany({
      data,
    });
  }

  async listSessionsWithRecurrence(
    session_base_id: number
  ): Promise<listSessions[]> {
    const sessions = await this.prisma.session.findMany({
      where: {
        session_base_id,
        deletedAt: null,
      },
      include: {
        Game: true,
        players_seats: true,
        Player: {
          select: {
            avatar: true,
            nickname: true,
          },
        },
      },
    });

    return sessions;
  }

  async findStatementsBySession(session_id: number): Promise<TStatement[]> {
    return this.prisma.account_statement.findMany({
      where: {
        Session: {
          id: session_id,
        },
      },
      include: {
        Session: true,
        Payment_info: true,
      },
    });
  }

  async listTags(): Promise<Tag[]> {
    return this.prisma.tag.findMany();
  }

  async promoteSession(data: {
    session_id: number;
    until: Date;
    days_to_expire: number;
    price: number;
    reach_max?: number;
    reach_min?: number;
    active?: boolean;
    payment_type: string;
    price_per_day: number;
    payment_info_id: number;
  }): Promise<Promoted_session> {
    const promoted_session = await this.prisma.promoted_session.create({
      data: {
        until: data.until,
        days_to_expire: data.days_to_expire,
        price: data.price,
        expected_reach_max: data.reach_max,
        expected_reach_min: data.reach_min,
        payment_type: data.payment_type,
        price_per_day: data.price_per_day,
        active: data.active,
        payment_info_id: data.payment_info_id,
        Session: {
          connect: {
            id: data.session_id,
          },
        },
      },
    });

    return promoted_session;
  }

  listPromotedSessionsByPlayer(player_id: number): Promise<
    (Promoted_session & {
      Session: Session & {
        Game: Game;
      };
    })[]
  > {
    const promoted_sessions = this.prisma.promoted_session.findMany({
      where: {
        Session: {
          Player: {
            id: player_id,
          },
        },
      },
      include: {
        Session: {
          include: {
            Game: true,
          },
        },
      },
    });

    return promoted_sessions;
  }

  async listPromotedSessions(): Promise<
  (Promoted_session & {
    Session: Session & {
      Game: Game;
    };
  })[]
> {
  let promoted_sessions = await this.prisma.promoted_session.findMany({
    where:{
      active:true
    },
    include: {
      Session: {
        include: {
          Game: true,
        },
      },
    },
  });
  promoted_sessions = promoted_sessions.sort((a, b) => {
    if (a.payment_type === "now" && b.payment_type !== "now") {
      return -1;
    }
    if (a.payment_type !== "now" && b.payment_type === "now") {
      return 1;
    }
    return b.price_per_day - a.price_per_day;
  });
  return promoted_sessions;
}

async listAllPromotedSession(): Promise<Promoted_session[]> {
  const promoted_sessions = await this.prisma.promoted_session.findMany()
  return promoted_sessions
}

async finishPromoteSession(id: number): Promise<Promoted_session> {
  const promoteSession = await this.prisma.promoted_session.update({
    where: {
      id: id,
    },
    data: {
      active: false,
    },
  });
  
  return promoteSession;
}
async confirmPromoteSession(id: number): Promise<Promoted_session> {
  const promoteSession = await this.prisma.promoted_session.update({
    where: {
      id: id,
    },
    data: {
      active: true,
    },
  });
  
  return promoteSession;
}

  async findPromotedSessionById(
    promoted_session_id: number
  ): Promise<Promoted_session> {
    const promoted_session = await this.prisma.promoted_session.findFirst({
      where: {
        id: promoted_session_id,
      },
      include: {
        Session: true,
      },
    });

    return promoted_session;
  }

  async findPromotedSessionBySessionId(
    session_id: number
  ): Promise<Promoted_session> {
    const promoted_session = await this.prisma.promoted_session.findFirst({
      where: {
        Session: {
          id: session_id,
        },
      },
    });

    return promoted_session;
  }

  async updatePromotedSession(data: {
    promoted_session_id: number;
    until?: Date;
    days_to_expire?: number;
    active: boolean;
  }): Promise<Promoted_session> {
    const promoted_session = await this.prisma.promoted_session.update({
      where: {
        id: data.promoted_session_id,
      },
      data: {
        until: data.until,
        days_to_expire: data.days_to_expire,
        active: data.active,
      },
    });

    return promoted_session;
  }

  async listMetricsBySession(session_id: number): Promise<Session_metrics[]> {
    const metrics = await this.prisma.session_metrics.findMany({
      where: {
        Session: {
          id: session_id,
        },
      },
    });

    return metrics;
  }

  async createMetrics(data: {
    session_id: number;
    player_id: number;
    clicks?: number;
    reach?: number;
    views?: number;
    checkouts?: number;
    date?: Date;
  }): Promise<Session_metrics> {
    const metrics = await this.prisma.session_metrics.create({
      data: {
        clicks: data.clicks,
        reach: data.reach,
        views: data.views,
        checkouts: data.checkouts,
        date: data.date,
        Session: {
          connect: {
            id: data.session_id,
          },
        },
        Player: {
          connect: {
            id: data.player_id,
          },
        },
      },
    });

    return metrics;
  }

  async updateMetrics(data: {
    clicks?: number;
    reach?: number;
    views?: number;
    checkouts?: number;
    session_metrics_id: number;
  }): Promise<Session_metrics> {
    const metrics = await this.prisma.session_metrics.findFirst({
      where: {
        id: data.session_metrics_id,
      },
    });

    const clicks = data.clicks ? data.clicks + metrics.clicks : metrics.clicks;
    const reach = data.reach ? data.reach + metrics.reach : metrics.reach;
    const views = data.views ? data.views + metrics.views : metrics.views;
    const checkouts = data.checkouts
      ? data.checkouts + metrics.checkouts
      : metrics.checkouts;

    const updated_metrics = await this.prisma.session_metrics.update({
      where: {
        id: data.session_metrics_id,
      },
      data: {
        clicks,
        reach,
        views,
        checkouts,
      },
    });

    return updated_metrics;
  }

  async openDispute(data: {
    session_id: number;
    player_id: number;
    description: string;
    attachment?: string;
    status: string;
  }): Promise<any> {
    const dispute = await this.prisma.disputes.create({
      data: {
        player_description: data.description,
        player_attachment: data.attachment,
        status: data.status,
        Session: {
          connect: {
            id: data.session_id,
          },
        },
        Player: {
          connect: {
            id: data.player_id,
          },
        },
      },
    });

    return dispute;
  }

  async updateDispute(data: {
    dispute_id: number;
    user_id?: number;
    description?: string;
    attachment?: string;
    status?: string;
    mentor_response_date?: Date;
    decision?: string;
    winner_id?: number;
    resolution_date?: Date;
    moderator_description?: string;
  }): Promise<Disputes> {
    const updated_dispute = await this.prisma.disputes.update({
      where: {
        id: data.dispute_id,
      },
      data: {
        winner_id: data.winner_id,
        decision: data.decision,
        resolution_date: data.resolution_date,
        mentor_response_date: data.mentor_response_date,
        mentor_description: data.description,
        mentor_attachment: data.attachment,
        status: data.status,
        moderator_description: data.moderator_description,
        Moderator: data.user_id && {
          connect: {
            id: data.user_id,
          },
        },
      },
    });

    return updated_dispute;
  }

  async findDisputeById(dispute_id: number): Promise<
    Disputes & {
      Session: Session;
    }
  > {
    const dispute = await this.prisma.disputes.findFirst({
      where: {
        id: dispute_id,
      },
      include: {
        Session: true,
      },
    });

    return dispute;
  }

  async listDisputes(): Promise<
    (Disputes & {
      Player: Player;
    })[]
  > {
    const disputes = await this.prisma.disputes.findMany({
      include: {
        Player: true,
      },
    });

    return disputes;
  }
  async createInviteDiscord(session_id: number, invite: string, channelId: string): Promise<Invite_Discord> {
    const newInvite = await this.prisma.invite_Discord.create({
      data: {
        session_id: session_id,
        link_discord: invite,
        channel_id:channelId
      },
    });
    return newInvite;
  };

  async createLinkDiscord(channelId: string): Promise<any> {
    try {
      const response = await axios.post(
        `https://discord.com/api/v10/channels/${channelId}/invites`,
        {
          max_age: 86400,
          max_uses: 5
        },
        {
          headers: {
            'Authorization': `Bot ${process.env.DISCORD_BOT_TOKEN}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const message = error.response?.data?.message || error.message;
        throw new Error(`Erro ao criar o convite: ${message}`);
      } else {
        throw new Error('Erro inesperado ao criar o convite.');
      }
    }
  }
  async getInviteDiscord(session_id:number):Promise<Invite_Discord>{
    const invites = await this.prisma.invite_Discord.findFirst({
      where:{
        session_id:session_id
      }
    });
    return invites
  }
  async listChargeback(): Promise<Chargeback[]> {
    const chargeback = await this.prisma.chargeback.findMany();
    return chargeback; 
  };
  async findChargebackById(chargeback_id:number): Promise<Chargeback>{
    const chargeback = await this.prisma.chargeback.findFirst({
      where: {
        id: chargeback_id
      },
    });
    return chargeback;
  };
  async sendAnswerChargeback(data:{
    chargeback_id:number, 
    mentor_response:string, 
    mentor_attachment?: string,
    mentor_response_date: Date
  }): Promise<chargeback_dispute>{
    const updatedChargeback = await this.prisma.chargeback_dispute.update({
      where:{
        id: data.chargeback_id,
      },
      data:{
        mentor_response: data.mentor_response,
        mentor_attachment: data.mentor_attachment,
        mentor_date_response: data.mentor_response_date,
      }
    })
    return updatedChargeback;
  };
  async requestAnswerChargeback(data:{
    chargeback_id:number, 
    mentor_requested_response:boolean, 
    mentor_response_date: Date
  }): Promise<chargeback_dispute>{
    const updatedChargeback = await this.prisma.chargeback_dispute.update({
      where:{
        id: data.chargeback_id,
      },
      data:{
        mentor_requested_response: data.mentor_requested_response,
        mentor_date_response: data.mentor_response_date,
      }
    })
    return updatedChargeback;
  }
  async assignChargeback(data:{
    chargeback_id:number, 
    user_id:number,
  }): Promise<chargeback_dispute>{
    const updatedChargeback = await this.prisma.chargeback_dispute.update({
      where:{
        id: data.chargeback_id,
      },
      data:{
        user_id: data.user_id
      }
    })
    return updatedChargeback;
  }
  async findChargebackInfoById(chargeback_id:number): Promise<chargeback_dispute>{
    const chargeback = await this.prisma.chargeback_dispute.findFirst({
      where: {
        chargeback_id: chargeback_id
      },
    });
    return chargeback;
  };
  async findPaymentInfoByPaymentId(external_payment_id:bigint): Promise<Payment_info>{

    const paymentInfo = await this.prisma.payment_info.findFirst({
      where:{
        external_payment_id: external_payment_id.toString(),
      }
    });
    return paymentInfo;
  };
  async findSeatById(seat_id:number): Promise<Seat>{
    const seat = await this.prisma.seat.findFirst({
      where:{
        id:seat_id,
      }
    });
    return seat; 
  };
  async listAllDisputes(): Promise<
    (Disputes & { Player: Player; Session: Session })[]
  > {
    const disputes = await this.prisma.disputes.findMany({
      include: {
        Player: true,
        Session: true,
      }
    });
    return disputes;
  }

  async listDisputesByMentorId(): Promise<
    (Disputes & { Player: Player; Session: Session })[]
  > {
    const disputes = await this.prisma.disputes.findMany({
      include: {
        Player: true,
        Session: true,
      },
    });

    return disputes;
  }

  async listCancelationOptions(): Promise<cancelationOption[]> {
    return this.prisma.cancelation_option.findMany();
  }

  async cancelSession(
    session_id: number,
    cancelation_option_id: number,
    player_id: number,
    motive_field: string
  ): Promise<Session> {
    await this.prisma.cancelation_motive.create({
      data: {
        Session: {
          connect: {
            id: session_id,
          },
        },
        Player: {
          connect: {
            id: player_id,
          },
        },
        Cancelation_option: {
          connect: {
            id: cancelation_option_id,
          },
        },
        motive_field,
      },
    });

    const session = await this.prisma.session.update({
      where: {
        id: session_id,
      },
      data: {
        canceledAt: new Date(),
      },
    });

    return session;
  }

  async findLastSessionWithoutRating(user_id: number): Promise<any> {
    const session = await this.prisma
      .$queryRaw<Session>`SELECT ac.type, g."name" AS game_name, g.id AS game_id, g.logo, p.avatar, p.id AS player_id, p.nickname, s.end_time, s.id AS session_id, s.price, r2.points, s.seats, s.start_time, st."isFinished" 
      FROM "Session" s 
          JOIN "Seat" st ON s.id = st.session_id 
          JOIN "Game" g ON s.game_id = g.id
          JOIN "Player" p ON s.player_id = p.id 
          JOIN "Account_statement" ac ON ac.session_id = s.id
          LEFT JOIN "Rating" r2 ON r2.session_id = s.id 
  WHERE NOT EXISTS (
      SELECT 1 FROM "Rating" r 
          WHERE r.session_id = s.id )
              AND s.player_id = ${user_id}
              AND st."isFinished" = true
          ORDER BY s.end_time DESC                
                  LIMIT 1
                  `;

    return session;
  }

  async historicChat(
    session_id: number,
    limit: number,
    page: number
  ): Promise<IHistoricChatDTO[]> {
    return this.prisma.message_chat.findMany({
      include: {
        Player: {
          select: {
            id: true,
            name: true,
            nickname: true,
            avatar: true,
          },
        },
      },
      where: {
        session_id,
      },
      take: limit || 100,
      skip: (page - 1) * limit || 0,
      orderBy: {
        data_message: "desc",
      },
    });
  }
  async countMessagesChatById(session_id: number): Promise<number> {
    return this.prisma.message_chat.count({
      where: {
        session_id,
      },
    });
  }

  async findSeatBySessionAndPlayer(
    session_id: number,
    player_id: number
  ): Promise<IFullSeatDTO> {
    return this.prisma.seat.findFirst({
      where: {
        session_id,
        player_id,
      },
      include: {
        Session: {
          include: {
            Player: true,
          },
        },
        Payment_info: true,
      },
    });
  }

  async findFullPastSessionsByPlayerId(
    player_id: number,
    page: number,
    limit: number
  ): Promise<listSessions[]> {
    return this.prisma.session.findMany({
      where: {
        player_id,
        start_time: {
          lt: new Date(),
        },
      },
      orderBy: {
        start_time: "desc",
      },
      skip: (page - 1) * limit,
      take: limit,
      include: {
        promoted_session: true,
        Game: true,
        players_seats: {
          include: {
            Player: {
              select: {
                avatar: true,
                nickname: true,
              },
            },
          },
        },
        Player: {
          select: {
            id: true,
            avatar: true,
            nickname: true,
            player_stats: {
              select: {
                value: true,
                Game_stats: {
                  select: {
                    name: true,
                    slug: true,
                    game_id: true,
                  },
                },
              },
            },
          },
        },
        Input: true,
        Platform: true,
      },
    });
  }

  async findSessionsEndedBefore(time: Date): Promise<IlistEndedSessionsDTO[]> {
    const sessions = await this.prisma.session.findMany({
      where: {
        end_time: {
          lt: time,
        },
        players_seats: {
          some: {
            isFinished: false,
            hasDispute: {
              equals: false,
            },
          },
        },
      },
      include: {
        players_seats: true,
      },
    });

    return sessions;
  }

  async notConfirmSession(
    seat: Seat,
    sessionOwner: Player,
    statementBySession: TStatement,
    ownerStatementBySession_id: number
  ): Promise<any> {
    await this.prisma.$transaction(async (tx) => {
      await tx.account_statement.create({
        data: {
          title: "Compra de sessão",
          description: sessionOwner.nickname,
          quantity: statementBySession.quantity,
          type: "canceled",
          Wallet: {
            connect: {
              player_id: seat.player_id,
            },
          },
          Session: {
            connect: {
              id: statementBySession.session_id,
            },
          },
        },
      });
      if (statementBySession.Payment_info?.external_payment_id == null) {
        const { total } = await this.prisma.wallet.findFirst({
          where: { player_id: seat.player_id },
        });

        const new_total = total + statementBySession.quantity;

        await tx.wallet.update({
          where: {
            player_id: seat.player_id,
          },
          data: {
            total: new_total,
          },
        });
      } else {
        this.mercadoPagoProvider.createRefund(
          statementBySession.Payment_info.external_payment_id
        );
      }
      await tx.account_statement.delete({
        where: {
          id: ownerStatementBySession_id,
        },
      });
      await this.deleteJob(seat.id);

      await this.prisma.seat.delete({
        where: {
          id: seat.id,
        },
      });
    });
  }
  async getSessionPaymentStatus(
    session_id: number,
    player_id: number
  ): Promise<any> {
    return this.prisma.session.findFirst({
      where: {
        id: session_id,
      },
      include: {
        players_seats: {
          where: {
            player_id,
          },
          include: {
            Payment_info: {
              select: {
                status: true,
              },
            },
          },
        },
      },
    });
  }
}
