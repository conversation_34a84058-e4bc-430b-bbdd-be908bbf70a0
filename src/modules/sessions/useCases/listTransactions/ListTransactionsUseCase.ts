import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

interface IRequest {
  filter:
    | "buy_session"
    | "sell_session"
    | "knowbie_points"
    | "withdraw"
    | "canceled";
  date: Date | string | undefined;
  page: number;
  limit: number;
}

@injectable()
export class ListTransactionsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ filter, date, page, limit }: IRequest): Promise<any> {
    const statements = await this.playerRepository.listStatements();
    const buy_session = statements.filter(
      (statement) =>
        statement.title.includes("Compra de sessão") &&
        statement.type === "outcome"
    );
    const sell_session = statements.filter(
      (statement) =>
        statement.title.includes("Venda de sessão") &&
        statement.type === "income"
    );
    const knowbie_points = statements.filter(
      (statement) =>
        statement.title.includes("Compra de pacote") &&
        statement.type === "income"
    );
    const withdraw = statements.filter(
      (statement) =>
        statement.title.includes("Saque") && statement.type === "outcome"
    );
    const canceled = statements.filter(
      (statement) => statement.type === "canceled"
    );

    let filteredStatements = statements;

    switch (filter) {
      case "buy_session":
        filteredStatements = buy_session;
        break;
      case "sell_session":
        filteredStatements = sell_session;
        break;
      case "knowbie_points":
        filteredStatements = knowbie_points;
        break;
      case "withdraw":
        filteredStatements = withdraw;
        break;
      case "canceled":
        filteredStatements = canceled;
        break;
      default:
        filteredStatements = statements;
        break;
    }

    if (date) {
      filteredStatements = filteredStatements.filter((statement) =>
        this.dateProvider.compareIfSameDay(statement.createdAt, date)
      );
    }

    const mappedStatements = filteredStatements
      .map((statement) => {
        return {
          id: statement.id,
          title: statement.title,
          description: statement.description,
          type: statement.type,
          quantity: statement.quantity,
          createdAt: statement.createdAt,
          player_name:
            statement.Wallet.Player.name || statement.Wallet.Player.nickname,
        };
      })
      .filter((statement) => statement.type !== "pending")
      .sort((a, b) => +new Date(b.createdAt) - +new Date(a.createdAt));

    if (page && limit) {
      return {
        total: [
          {
            name: "Compras de knowbie points",
            quantity: knowbie_points.length,
          },
          {
            name: "Compras de sessão",
            quantity: buy_session.length,
          },
          {
            name: "Vendas de sessão",
            quantity: sell_session.length,
          },
          {
            name: "Saques",
            quantity: withdraw.length,
          },
          {
            name: "Estornos",
            quantity: canceled.length,
          },
        ],
        transactions: mappedStatements.sort(
          (a, b) => +new Date(b.createdAt) - +new Date(a.createdAt)
        ),
      };
    }

    return {
      total: [
        {
          name: "Compras de knowbie points",
          quantity: knowbie_points.length,
        },
        {
          name: "Compras de sessão",
          quantity: buy_session.length,
        },
        {
          name: "Vendas de sessão",
          quantity: sell_session.length,
        },
        {
          name: "Saques",
          quantity: withdraw.length,
        },
        {
          name: "Estornos",
          quantity: canceled.length,
        },
      ],
      transactions: mappedStatements,
    };
  }
}
