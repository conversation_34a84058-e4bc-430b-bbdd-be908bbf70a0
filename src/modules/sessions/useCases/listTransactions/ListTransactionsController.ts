import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListTransactionsUseCase } from "./ListTransactionsUseCase";

export class ListTransactionsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { filter, date, limit, page } = request.query;

    const listTransactionUseCase = container.resolve(ListTransactionsUseCase);

    const transactions = await listTransactionUseCase.execute({
      filter: filter as
        | "buy_session"
        | "sell_session"
        | "knowbie_points"
        | "withdraw"
        | "canceled",
      date: date as string | Date,
      limit: Number(limit),
      page: Number(page),
    });

    return response.json(transactions);
  }
}
