import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { INotificationsProvider } from "@shared/container/providers/NotificationsProvider/INotificationsProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  session_id: number;
  player_id: number;
}

@injectable()
export class CancelAppointmentUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("NotificationsProvider")
    private notificationsProvider: INotificationsProvider,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ player_id, session_id }: IRequest): Promise<any> {
    const player = await this.playerRepository.findById(player_id);
    const session = await this.sessionRepository.findById(session_id);
    const sessionOwner = await this.playerRepository.findById(
      session.player_id
    );

    if (!session) {
      throw new AppError("Session not found");
    }

    const playerSeat = session.players_seats.find(
      (seat) => seat.player_id === player_id
    );

    if (!playerSeat) {
      throw new AppError("Player is not in this session");
    }

    if (!playerSeat.isConfirmed) {
      const oldStatement = await this.playerRepository.findStatementByPlayer(
        player_id,
        "createdAt"
      );

      const [statementBySession] = oldStatement
        .filter((statement) => statement.session_id === session.id)
        .slice(-1); // always get last statement

      await this.playerRepository.saveStatement(
        "Compra de sessão",
        `${sessionOwner.nickname}`,
        statementBySession.quantity,
        "canceled",
        player_id,
        statementBySession.session_id
      );

      await this.playerRepository.updateWallet(
        player_id,
        "income",
        statementBySession.quantity
      );

      const ownerStatement = await this.playerRepository.findStatementByPlayer(
        sessionOwner.id,
        "createdAt"
      );

      const [ownerStatementBySession] = ownerStatement
        .filter(
          (statement) =>
            statement.buyer_id === player_id &&
            statement.session_id === session.id &&
            statement.type === "pending"
        )
        .slice(-1);

      await this.playerRepository.deleteStatementById(
        ownerStatementBySession.id
      );

      await this.sessionRepository.deleteSeat(playerSeat.id);

      // const isPromotedSession =
      //   await this.sessionRepository.findPromotedSessionBySessionId(session_id);

      // if (isPromotedSession) {
      //   const until = this.dateProvider.addDays(
      //     isPromotedSession.days_to_expire
      //   );

      //   await this.sessionRepository.updatePromotedSession({
      //     promoted_session_id: isPromotedSession.id,
      //     active: true,
      //     until,
      //   });
      // }

      await this.notificationsProvider.sendNotification(
        sessionOwner.push_token,
        "Cancelamento de reserva",
        `Uma reserva feita por @${player.nickname} foi cancelada. Acesse o app para mais informações.`,
        JSON.stringify({
          avatar: player.avatar
            ? `${process.env.AWS_BUCKET_URL}/avatar/${player.avatar}`
            : "",
        })
      );

      await this.playerRepository.saveNotification({
        player_id: player.id,
        body: `Uma reserva feita por @${player.nickname} foi cancelada. Acesse o app para mais informações.`,
        title: "Cancelamento de reserva",
        image: player.avatar
          ? `${process.env.AWS_BUCKET_URL}/avatar/${player.avatar}`
          : "",
      });

      return "appointment canceled";
    }

    if (playerSeat.isConfirmed) {
      const oldStatement = await this.playerRepository.findStatementByPlayer(
        player_id,
        "createdAt"
      );

      const [statementBySession] = oldStatement
        .filter((statement) => statement.session_id === session.id)
        .slice(-1); // always get last statement

      const cancellationTaxPercentage = 0.1;
      const cancellationTax = Math.ceil(
        statementBySession.quantity * cancellationTaxPercentage
      );
      const total = statementBySession.quantity - cancellationTax;

      await this.playerRepository.saveStatement(
        "Compra de sessão",
        `${sessionOwner.nickname} - Taxa de cancelamento: ${cancellationTax} KP`,
        total,
        "canceled",
        player_id,
        statementBySession.session_id
      );

      await this.playerRepository.updateWallet(player_id, "income", total);

      const ownerStatement = await this.playerRepository.findStatementByPlayer(
        sessionOwner.id,
        "createdAt"
      );

      const [ownerStatementBySession] = ownerStatement
        .filter(
          (statement) =>
            statement.buyer_id === player_id &&
            statement.session_id === session.id &&
            statement.type === "pending"
        )
        .slice(-1);

      await this.playerRepository.deleteStatementById(
        ownerStatementBySession.id
      );

      await this.sessionRepository.deleteSeat(playerSeat.id);

      // const isPromotedSession =
      //   await this.sessionRepository.findPromotedSessionBySessionId(session_id);

      // if (isPromotedSession) {
      //   const until = this.dateProvider.addDays(
      //     isPromotedSession.days_to_expire
      //   );

      //   await this.sessionRepository.updatePromotedSession({
      //     promoted_session_id: isPromotedSession.id,
      //     active: true,
      //     until,
      //   });
      // }

      await this.notificationsProvider.sendNotification(
        sessionOwner.push_token,
        "Cancelamento de reserva",
        `Uma reserva feita por @${player.nickname} foi cancelada. Acesse o app para mais informações.`,
        JSON.stringify({
          avatar: player.avatar
            ? `${process.env.AWS_BUCKET_URL}/avatar/${player.avatar}`
            : "",
        })
      );

      await this.playerRepository.saveNotification({
        player_id: player.id,
        body: `Uma reserva feita por @${player.nickname} foi cancelada. Acesse o app para mais informações.`,
        title: "Cancelamento de reserva",
        image: player.avatar
          ? `${process.env.AWS_BUCKET_URL}/avatar/${player.avatar}`
          : "",
      });

      return "appointment canceled";
    }

    return "appointment canceled";
  }
}
