import { Request, Response } from "express";
import { container } from "tsyringe";

import { CancelAppointmentUseCase } from "./CancelAppointmentUseCase";

export class CancelAppointmentController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { session_id } = request.body;

    const cancelAppointmentUseCase = container.resolve(
      CancelAppointmentUseCase
    );

    const result = await cancelAppointmentUseCase.execute({
      player_id: Number(id),
      session_id,
    });

    return response.json(result);
  }
}
