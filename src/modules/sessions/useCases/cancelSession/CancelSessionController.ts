import { Request, Response } from "express";
import { container } from "tsyringe";

import { CancelSessionUseCase } from "./CancelSessionUseCase";

export class CancelSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { session_id } = request.params;
    const { cancelation_option_id, motive_field } = request.body;

    const cancelSessionUseCase = container.resolve(CancelSessionUseCase);

    const confirmCancelation = await cancelSessionUseCase.execute({
      player_id: Number(id),
      session_id: Number(session_id),
      cancelation_option_id,
      motive_field,
    });

    return response.json(confirmCancelation);
  }
}
