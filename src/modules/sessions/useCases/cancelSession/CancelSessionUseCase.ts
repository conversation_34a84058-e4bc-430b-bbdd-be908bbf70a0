import { inject, injectable } from "tsyringe";

import { AppError } from "@shared/errors/AppError";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IJobsRepository } from "@modules/jobs/repositories/IJobsRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { IMercadoPagoProvider } from "@shared/container/providers/PaymentGatewayProvider/IMercadoPagoProvider";

interface IRequest {
  session_id: number;
  player_id: number;
  cancelation_option_id: number;
  motive_field: string;
}

@injectable()
export class CancelSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("JobsRepository") private jobsRepository: IJobsRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("MercadoPagoProvider")
    private mercadoPagoProvider: IMercadoPagoProvider
  ) {}

  async execute({
    player_id,
    session_id,
    cancelation_option_id,
    motive_field,
  }: IRequest): Promise<string> {
    const session = await this.sessionRepository.findById(session_id);
    let refund;

    if (!session) {
      throw new AppError("Session not found", 404);
    }

    if (session.player_id !== player_id) {
      throw new AppError("Player not allowed to cancel this session", 401);
    }

    if (session.canceledAt) {
      throw new AppError("Session already canceled");
    }

    const sessionHasOcurred = this.dateProvider.compareIfBefore(
      session.end_time,
      new Date()
    );
    if (sessionHasOcurred) {
      throw new AppError("Session has already ended");
    }

    const seats = await this.sessionRepository.findSeatsBySession(session_id);

    seats.forEach(async (seat) => {
      const oldStatement = await this.playerRepository.findStatementByPlayer(
        seat.player_id,
        "createdAt"
      );
      const [statementBySession] = oldStatement
        .filter((statement) => statement.session_id === seat.session_id)
        .slice(-1); // always get last statement
      console.log(statementBySession.Payment_info?.status);
      // return;

      await this.playerRepository.saveStatement(
        "Compra de sessão",
        `${seat.Session.Player.nickname}`,
        statementBySession.quantity,
        "canceled",
        seat.player_id,
        statementBySession.session_id
      ); // player statement

      if (statementBySession.Payment_info?.status === "approved") {
        console.log("approved");
        // if payment is pending we cannot refund
        if (statementBySession.Payment_info.payment_from_wallet) {
          console.log("wallet");
          // if payment is from wallet, we need to refund to wallet
          await this.playerRepository.updateWallet(
            seat.player_id,
            "income",
            statementBySession.Payment_info.payment_from_wallet
          );
        }
        if (statementBySession.Payment_info.payment_from_external) {
          console.log("refund");

          // if payment is from external payment API, we need to refund to original payment method
          refund = await this.mercadoPagoProvider.createRefund(
            statementBySession.Payment_info.external_payment_id
          );
        }
      }

      // TODO: salvar o refund no banco de dados

      const ownerStatement = await this.playerRepository.findStatementByPlayer(
        seat.Session.Player.id,
        "createdAt"
      );

      // TODO: testar com dois statements com status "pending"
      const [ownerStatementBySession] = ownerStatement
        .filter(
          (statement) =>
            statement.buyer_id === seat.player_id &&
            statement.session_id === seat.session_id &&
            statement.type === "pending"
        )
        .slice(-1);

      if (ownerStatementBySession) {
        await this.playerRepository.deleteStatementById(
          ownerStatementBySession.id
        );
      }
      await this.sessionRepository.deleteSeat(seat.id);
      /* await this.sessionRepository.deleteJob(seat.id) */
    });
    // return;
    await this.sessionRepository.cancelSession(
      session_id,
      cancelation_option_id,
      player_id,
      motive_field
    );

    return "Session canceled";
  }
}
