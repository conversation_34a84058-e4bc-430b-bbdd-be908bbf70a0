import { Request, Response } from "express";
import { container } from "tsyringe";
import { GetLinkDiscordBySessionUseCase } from "./GetLinkDiscordBySessionUseCase";


export class GetLinkDiscordBySessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { session_id } = request.params;

    const getLinkDiscordBySessionUseCase = container.resolve(GetLinkDiscordBySessionUseCase);

    
    const invite = await getLinkDiscordBySessionUseCase.execute({
      session_id: Number(session_id),
    });

    return response.json(invite);
  };
};
