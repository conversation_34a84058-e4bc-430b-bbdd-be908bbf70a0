import { inject, injectable } from "tsyringe";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
interface IRequest {
    session_id: number;
}

@injectable()
export class GetLinkDiscordBySessionUseCase {
    constructor(
        @inject("SessionRepository") private sessionRepository: ISessionRepository,
      ) {}
    
      async execute({ session_id }: IRequest): Promise<any> {
        const inviteDiscord = await this.sessionRepository.getInviteDiscord(session_id);
        
    
        return inviteDiscord;
      }
}
