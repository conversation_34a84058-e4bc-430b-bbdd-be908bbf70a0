import fs from "fs";
import { resolve } from "path";
import { inject, injectable } from "tsyringe";

import upload from "@config/upload";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { IStorageProvider } from "@shared/container/providers/StorageProvider/IStorageProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  session_id: number;
  player_id: number;
  description: string;
  attachment?: string;
}

@injectable()
export class OpenDisputeUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("StorageProvider") private storageProvider: IStorageProvider,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({
    session_id,
    player_id,
    description,
    attachment,
  }: IRequest): Promise<any> {
    const session = await this.sessionRepository.findById(session_id);

    if (!session) {
      throw new AppError("Session not found", 404);
    }

    if (!this.dateProvider.compareIfBefore(session.end_time, new Date())) {
      throw new AppError("Session is running", 409);
    }

    const playerHasSeat = session.players_seats.find(
      (seat) => seat.player_id === player_id
    );

    if (!playerHasSeat) {
      throw new AppError("Player is not in this session", 403);
    }

    if (this.dateProvider.compareInDays(session.end_time, new Date()) > 7) {
      throw new AppError("Player can't open a dispute after 7 days", 409);
    }

    if (playerHasSeat.isFinished) {
      throw new AppError(
        "Player can't open a dispute because he has confirmed the session", 
        409
      );
    }

    if (playerHasSeat.hasDispute) {
      throw new AppError(
        "Player can't open a dispute because he has already disputed",
        409
      );
    }

    if (attachment) {
      const dirName = "resized";

      if (!fs.existsSync(`${upload.tmpFolder}/${dirName}`)) {
        fs.mkdirSync(`${upload.tmpFolder}/${dirName}`);
      }

      await fs.promises.rename(
        resolve(`${upload.tmpFolder}`, attachment),
        resolve(`${upload.tmpFolder}/${dirName}`, attachment)
      );

      await this.storageProvider.save(attachment, "disputes");
    }

    await this.sessionRepository.updateSeat({
      seat_id: playerHasSeat.id,
      hasDispute: true,
    });

    const status = "requested";

    await this.sessionRepository.openDispute({
      session_id,
      player_id,
      description,
      attachment,
      status,
    });
  }
}
