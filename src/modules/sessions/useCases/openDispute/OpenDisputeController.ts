import { Request, Response } from "express";
import { container } from "tsyringe";

import { OpenDisputeUseCase } from "./OpenDisputeUseCase";

export class OpenDisputeController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { session_id, description } = request.body;

    const attachment = request.file?.filename;

    const openDisputeUseCase = container.resolve(OpenDisputeUseCase);

    const dispute = await openDisputeUseCase.execute({
      session_id: Number(session_id),
      player_id: Number(id),
      description,
      attachment,
    });

    return response.json(dispute);
  }
}
