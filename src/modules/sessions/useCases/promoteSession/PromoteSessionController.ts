import { Request, Response } from "express";
import { container } from "tsyringe";

import { PromoteSessionUseCase } from "./PromoteSessionUseCase";

export class PromoteSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { session_id, price, days, reach_max, reach_min, payment_type , payment_from_wallet } = request.body;

    const promoteSessionUseCase = container.resolve(PromoteSessionUseCase);

    const promoted_session = await promoteSessionUseCase.execute({
      days,
      price,
      reach_max,
      reach_min,
      session_id,
      player_id: Number(id),
      payment_type,
      payment_from_wallet
    });

    return response.json(promoted_session);
  }
}
