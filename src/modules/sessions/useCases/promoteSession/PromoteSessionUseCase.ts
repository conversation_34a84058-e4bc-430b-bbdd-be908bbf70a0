import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";
import { IMercadoPagoProvider } from "@shared/container/providers/PaymentGatewayProvider/IMercadoPagoProvider";
import { IWebhookRepository } from "@modules/webhook/repositories/IWebhookRepository";

interface IRequest {
  session_id: number;
  player_id: number;
  price: number;
  days: number;
  reach_max?: number;
  reach_min?: number;
  payment_type: "now" | "after_purchase";
  payment_from_wallet?: number;
}

@injectable()
export class PromoteSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("MercadoPagoProvider") private mercadoPagoProvider: IMercadoPagoProvider,
    @inject("WebhookRepository") private webhookRepository: IWebhookRepository,
  ) {}

  async execute({
    session_id,
    player_id,
    price,
    days,
    reach_max = 800,
    reach_min = 400,
    payment_type,
    payment_from_wallet
  }: IRequest): Promise<any> {
    const session = await this.sessionRepository.findById(session_id);

    if (!session) {
      throw new AppError("Session not found");
    }

    const isSessionPromoted =
      await this.sessionRepository.findPromotedSessionBySessionId(session_id);

    if (isSessionPromoted && isSessionPromoted.active) {
      throw new AppError("Session already promoted");
    }

    const player = await this.playerRepository.findById(player_id);
    const until = this.dateProvider.addDays(days);
    const price_per_day = price / days;

    switch (payment_type) {
      // case "from_wallet":
      //   if (player.wallet.total < price) {
      //     throw new AppError("Knowbie point is not enough");
      //   }

      //   await this.playerRepository.saveStatement(
      //     "promoção de sessão",
      //     "sessão promovida",
      //     price,
      //     "outcome",
      //     player_id
      //   );

      //   await this.playerRepository.updateWallet(player_id, "outcome", price);

      //   const promotedSessionNowWallet = await this.sessionRepository.promoteSession({
      //     session_id,
      //     price,
      //     until,
      //     days_to_expire: days,
      //     reach_max,
      //     reach_min,
      //     payment_type,
      //     price_per_day
      //   });
      //   return promotedSessionNowWallet;
      case "now":
        if(payment_from_wallet>=price){
          if (player.wallet.total < price) {
            throw new AppError("Knowbie point is not enough");
          }
  
          await this.playerRepository.saveStatement(
            "promoção de sessão",
            "sessão promovida",
            price,
            "outcome",
            player_id
          );
  
          await this.playerRepository.updateWallet(player_id, "outcome", price);
  
          const promotedSessionNowWallet = await this.sessionRepository.promoteSession({
            session_id,
            price,
            until,
            days_to_expire: days,
            reach_max,
            reach_min,
            payment_type,
            price_per_day
          });
          return promotedSessionNowWallet;
        };
        if(payment_from_wallet<price){
          if (player.wallet.total < payment_from_wallet) {
            throw new AppError("Knowbie point is not enough");
          }
          await this.playerRepository.updateWallet(player_id, "outcome", payment_from_wallet);
          const payment_pending = price - payment_from_wallet
          const date_of_expiration = new Date();
          date_of_expiration.setMinutes(date_of_expiration.getMinutes() + 20);
          const preferenceData = {
            items: [
              {
                title: `Sessão de ${player.nickname}`,
                description: `Promoção de sessão de ${player.nickname}`,
                picture_url: player.avatar || null,
                category_id: "Services",
                quantity: 1,
                currency_id: "BRL",
                unit_price: payment_pending,
              },
            ],
            payer: {
              identification: {
                type: player.cpf ? "CPF" : null,
                number: player.cpf || null,
              },
            },
            payment_methods: {
              excluded_payment_types: [
                {
                  id: "ticket,atm,debit_card",
                },
              ],
            },
            back_urls: {
              success: "knowbie://success",
              failure: "knowbie://failure",
              pending: "knowbie://pending",
            },
            auto_return: "approved",
            external_reference: "",
            date_of_expiration,
            // notification_url:
            //   "https://d8d1-45-7-1-242.ngrok-free.app/webhook/mercadopago",
            notification_url: `${process.env.APP_URL_BASE}/webhook/mercadopago`,
          };
    
          let mercadoPagoResponse;
          let payment_info

          payment_info = await this.webhookRepository.create({
            player_id,
            payment_from_external:payment_pending,
            service_type: "3"
          });
          preferenceData.external_reference = String(payment_info.id);

          mercadoPagoResponse = await this.mercadoPagoProvider.createPreference(
            preferenceData
          );

          await this.webhookRepository.update(payment_info.id, {
            payment_url: mercadoPagoResponse?.data?.init_point,
          });

          await this.playerRepository.saveStatement(
            "Promoção de sessão",
            `${player.nickname}`,
            price,
            "outcome",
            player_id,
            session_id,
            undefined,
            payment_info.id
          );

          const promotedSessionNow = await this.sessionRepository.promoteSession({
            session_id,
            price,
            until,
            days_to_expire: days,
            reach_max,
            reach_min,
            payment_type,
            price_per_day,
            active: false,
            payment_info_id: payment_info.id,
          });
          return {
            ...promotedSessionNow,
            payment_url: mercadoPagoResponse?.data?.init_point, //Link de pagamento
          };
        }
        

      case "after_purchase":
        price += price * 0.1;
        const promotedSessionAfterPurchase = await this.sessionRepository.promoteSession({
          session_id,
          price,
          until,
          days_to_expire: days,
          reach_max,
          reach_min,
          payment_type,
          price_per_day
        });
        return promotedSessionAfterPurchase;

      default:
        throw new AppError("Invalid payment type");
    }
  }
}
