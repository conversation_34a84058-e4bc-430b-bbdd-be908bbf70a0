import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { SessionMap } from "@modules/sessions/mapper/sessionMap";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";
import { listSessions } from "@modules/sessions/dtos/ISessionDTO";

interface IRequest {
  session_id: number;
}

@injectable()
export class GetFullSessionByIdUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ session_id }: IRequest): Promise<listSessions> {
    const session = await this.sessionRepository.findById(session_id);

    if (!session) {
      throw new AppError("Session not found");
    }

    return session;
  }
}
