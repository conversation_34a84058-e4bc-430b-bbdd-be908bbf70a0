import { Request, Response } from "express";
import { container } from "tsyringe";

import { GetFullSessionByIdUseCase } from "./GetFullSessionByIdUseCase";

export class GetFullSessionByIdController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { session_id } = request.params;

    const getFullSessionByIdUseCase = container.resolve(
      GetFullSessionByIdUseCase
    );

    const session = await getFullSessionByIdUseCase.execute({
      session_id: Number(session_id),
    });

    return response.json(session);
  }
}
