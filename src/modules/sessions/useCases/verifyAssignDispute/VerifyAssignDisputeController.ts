import { Request, Response } from "express";
import { container } from "tsyringe";
import { VerifyAssignDisputeUseCase } from "./VerifyAssignDisputeUseCase";

export class VerifyAssignDisputeController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { dispute_id } = request.params;

    const verifyAssignDisputeUseCase = container.resolve(VerifyAssignDisputeUseCase);

    const dispute = await verifyAssignDisputeUseCase.execute({
      dispute_id: Number(dispute_id),
    });

    return response.json(dispute);
  };
};
