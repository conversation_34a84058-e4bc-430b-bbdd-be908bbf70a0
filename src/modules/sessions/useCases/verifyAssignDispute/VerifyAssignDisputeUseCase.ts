import { inject, injectable } from "tsyringe";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  dispute_id: number;
}

@injectable()
export class VerifyAssignDisputeUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
  ) {}

  async execute({ dispute_id }: IRequest): Promise<any> {
    const dispute = await this.sessionRepository.findDisputeById(dispute_id);

    if (!dispute) {
      throw new AppError("Dispute not found");
    }

    const dateCreate = dispute.createdAt.getTime();
    const dateNow = new Date().getTime();
    const diffInDays = (dateNow - dateCreate) / (1000 * 60 * 60 * 24);
    if (dispute.mentor_description || diffInDays > 3){
        return "can be accepted"
    }else{
        throw new AppError("cannot be accepted")
    };
  };
};
