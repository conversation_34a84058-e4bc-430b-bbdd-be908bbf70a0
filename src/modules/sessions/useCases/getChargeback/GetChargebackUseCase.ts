import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
    chargeback_id: number;
}

@injectable()
export class GetChargebackUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("UserRepository") private userRepository: IUserRepository
  ) {}

  async execute({ chargeback_id}: IRequest): Promise<any> {
    const chargeback = await this.sessionRepository.findChargebackById(chargeback_id);
    if (!chargeback) {
        throw new AppError("Chargeback not found");
    }
    const paymentInfo = await this.sessionRepository.findPaymentInfoByPaymentId(chargeback.external_payment_id);
    const seat = await this.sessionRepository.findSeatById(paymentInfo.seat_id);
    const session = await this.sessionRepository.findById(seat.session_id);
    const player = await this.playerRepository.findById(paymentInfo.player_id);
    const mentor = await this.playerRepository.findById(session.player_id);
    
    return {
        id:chargeback.id,
        chargeback_date: chargeback.date_created,
        player_id: player.id,
        player: player.name,
        session: session.id,
        session_date: session.createdAt,
        mentor_id: mentor.id,
        mentor: mentor.name,
        session_price: session.price
    }
  }
}
