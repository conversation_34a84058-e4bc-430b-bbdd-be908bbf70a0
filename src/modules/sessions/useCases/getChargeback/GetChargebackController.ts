import { Request, Response } from "express";
import { container } from "tsyringe";
import { GetChargebackUseCase } from "./GetChargebackUseCase";


export class GetChargebackController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { chargeback_id } = request.params;

    const getChargebackUseCase = container.resolve(GetChargebackUseCase);

    
    const chargeback = await getChargebackUseCase.execute({
      chargeback_id: Number(chargeback_id),
    });

    return response.json(chargeback);
  };
};
