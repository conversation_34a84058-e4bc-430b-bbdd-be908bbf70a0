import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListMetricsUseCase } from "./ListMetricsUseCase";

export class ListMetricsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { session_id } = request.params;
    const { options } = request.query;

    const listMetricsUseCase = container.resolve(ListMetricsUseCase);

    const metrics = await listMetricsUseCase.execute({
      session_id: Number(session_id),
      options: options as string,
    });

    return response.json(metrics);
  }
}
