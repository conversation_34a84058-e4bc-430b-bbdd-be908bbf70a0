import { RRule } from "rrule";
import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  session_id: number;
  options: string;
}

interface IResponse {
  views: number;
  reach: number;
  clicks: number;
  checkouts: number;
  metricsByDate: any;
}

@injectable()
export class ListMetricsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ session_id, options }: IRequest): Promise<IResponse> {
    const session = await this.sessionRepository.findById(session_id);

    if (!session) {
      throw new AppError("Session not found");
    }

    const session_metrics = await this.sessionRepository.listMetricsBySession(
      session_id
    );

    if (Number(options)) {
      const lastDate = this.dateProvider.subtractDays(Number(options));

      const rule = new RRule({
        freq: RRule.DAILY,
        dtstart: new Date(lastDate),
        until: new Date(),
      });

      const allDates = rule.all().map((date) => {
        return {
          date: date.toISOString().split("T")[0],
          views: 0,
          reach: 0,
          clicks: 0,
          checkouts: 0,
        };
      });

      const metrics = session_metrics.filter(
        (metric) =>
          this.dateProvider.compareIfBefore(lastDate, metric.date) &&
          this.dateProvider.compareIfBefore(metric.date, new Date())
      );

      const views = metrics.reduce((acc, metric) => {
        return acc + metric.views;
      }, 0);

      const reach = metrics.reduce((acc, metric) => {
        return acc + metric.reach;
      }, 0);

      const clicks = metrics.reduce((acc, metric) => {
        return acc + metric.clicks;
      }, 0);

      const checkouts = metrics.reduce((acc, metric) => {
        return acc + metric.checkouts;
      }, 0);

      const metricsByDate = metrics.reduce((acc, metric) => {
        const date = metric.date.toISOString().split("T")[0];

        if (!acc[date]) {
          acc[date] = {
            views: 0,
            reach: 0,
            clicks: 0,
            checkouts: 0,
          };
        }

        acc[date].views += metric.views;
        acc[date].reach += metric.reach;
        acc[date].clicks += metric.clicks;
        acc[date].checkouts += metric.checkouts;

        return acc;
      }, {});

      const arrMetricsByDate = Object.keys(metricsByDate).map((date) => {
        return {
          date,
          views: metricsByDate[date].views,
          reach: metricsByDate[date].reach,
          clicks: metricsByDate[date].clicks,
          checkouts: metricsByDate[date].checkouts,
        };
      });

      const allMetricsArray = [];

      allDates.forEach((date) => {
        arrMetricsByDate.forEach((metric) => {
          if (date.date.includes(metric.date)) {
            allMetricsArray.push(metric);
          }
        });

        if (!allMetricsArray.find((metric) => metric.date === date.date)) {
          allMetricsArray.push(date);
        }
      });

      return {
        views,
        reach,
        clicks,
        checkouts,
        metricsByDate: allMetricsArray,
      };
    }

    // options === "NaN" returns all metrics
    const views = session_metrics.reduce((acc, metric) => {
      return acc + metric.views;
    }, 0);

    const reach = session_metrics.reduce((acc, metric) => {
      return acc + metric.reach;
    }, 0);

    const clicks = session_metrics.reduce((acc, metric) => {
      return acc + metric.clicks;
    }, 0);

    const checkouts = session_metrics.reduce((acc, metric) => {
      return acc + metric.checkouts;
    }, 0);

    const metricsByDate = session_metrics.reduce((acc, metric) => {
      const date = metric.date.toISOString().split("T")[0];

      if (!acc[date]) {
        acc[date] = {
          views: 0,
          reach: 0,
          clicks: 0,
          checkouts: 0,
        };
      }

      acc[date].views += metric.views;
      acc[date].reach += metric.reach;
      acc[date].clicks += metric.clicks;
      acc[date].checkouts += metric.checkouts;

      return acc;
    }, {});

    const arrMetricsByDate = Object.keys(metricsByDate).map((date) => {
      return {
        date,
        views: metricsByDate[date].views,
        reach: metricsByDate[date].reach,
        clicks: metricsByDate[date].clicks,
        checkouts: metricsByDate[date].checkouts,
      };
    });

    const lastDate = this.dateProvider.subtractDays(60);

    const rule = new RRule({
      freq: RRule.DAILY,
      dtstart: new Date(lastDate),
      until: new Date(),
    });

    const allDates = rule.all().map((date) => {
      return {
        date: date.toISOString().split("T")[0],
        views: 0,
        reach: 0,
        clicks: 0,
        checkouts: 0,
      };
    });

    const allMetricsArray = [];

    allDates.forEach((date) => {
      arrMetricsByDate.forEach((metric) => {
        if (date.date.includes(metric.date)) {
          allMetricsArray.push(metric);
        }
      });

      if (!allMetricsArray.find((metric) => metric.date === date.date)) {
        allMetricsArray.push(date);
      }
    });

    return { views, reach, clicks, checkouts, metricsByDate: allMetricsArray };
  }
}
