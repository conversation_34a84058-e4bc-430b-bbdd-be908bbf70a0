import fs from "fs";
import { resolve } from "path";
import { inject, injectable } from "tsyringe";

import upload from "@config/upload";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { IStorageProvider } from "@shared/container/providers/StorageProvider/IStorageProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  chargeback_id: number;
  mentor_requested_response: boolean;
}

@injectable()
export class RequestAnswerChargebackUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("StorageProvider") private storageProvider: IStorageProvider,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({
    chargeback_id,
    mentor_requested_response,
  }: IRequest): Promise<any> {
    const chargeback = await this.sessionRepository.findChargebackInfoById(chargeback_id);

    if (!chargeback) {
      throw new AppError("Chargeback not found");
    }
    const updatedChargeback = await this.sessionRepository.requestAnswerChargeback({
      chargeback_id: chargeback.id,
      mentor_requested_response,
      mentor_response_date: new Date(),
    });
    return updatedChargeback
  }
}
