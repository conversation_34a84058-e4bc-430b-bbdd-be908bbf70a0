import { Request, Response } from "express";
import { container } from "tsyringe";
import { RequestAnswerChargebackUseCase } from "./RequestAnswerChargebackUseCase";


export class RequestAnswerChargebackController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { chargeback_id } = request.params;
    const { mentor_requested_response } = request.body;


    const requestAnswerChargebackUseCase = container.resolve(RequestAnswerChargebackUseCase);

    const chargeback = await requestAnswerChargebackUseCase.execute({
      chargeback_id: Number(chargeback_id),
      mentor_requested_response,
    });

    return response.json(chargeback);
  }
}
