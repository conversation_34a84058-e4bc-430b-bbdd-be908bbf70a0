import { Request, Response } from "express";
import { container } from "tsyringe";

import { KnowbiePointsConsumptionsUseCase } from "./KnowbiePointsConsumptionsUseCase";

export class KnowbiePointsConsumptionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { filter, type, date } = request.query;

    const knowbiePointsConsumptionUseCase = container.resolve(
      KnowbiePointsConsumptionsUseCase
    );

    const infos = await knowbiePointsConsumptionUseCase.execute({
      filter: filter as "games" | "promotions",
      type: type as "days" | "months" | undefined,
      date: date as string | Date | undefined,
    });

    return response.json(infos);
  }
}
