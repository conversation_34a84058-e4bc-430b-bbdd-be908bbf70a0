import { inject, injectable } from "tsyringe";

import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

interface IRequest {
  filter: "games" | "promotions";
  type?: "days" | "months";
  date?: string | Date | undefined;
}

@injectable()
export class KnowbiePointsConsumptionsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("GamesRepository") private gamesRepository: IGamesRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ filter, type, date = new Date() }: IRequest): Promise<any> {
    if (filter === "promotions") {
      const promotedSessions =
        await this.sessionRepository.listPromotedSessions();

      const knowbiePointsByDays = promotedSessions.reduce(
        (acc, promoted) => {
          if (this.dateProvider.compareIfSameMonth(promoted.createdAt, date)) {
            if (promoted.days_to_expire >= 2 && promoted.days_to_expire <= 6) {
              acc["2 - 6 dias"].quantity += promoted.price;
            }

            if (promoted.days_to_expire >= 7 && promoted.days_to_expire <= 11) {
              acc["7 - 11 dias"].quantity += promoted.price;
            }

            if (
              promoted.days_to_expire >= 12 &&
              promoted.days_to_expire <= 16
            ) {
              acc["12 - 16 dias"].quantity += promoted.price;
            }

            if (
              promoted.days_to_expire >= 17 &&
              promoted.days_to_expire <= 21
            ) {
              acc["17 - 21 dias"].quantity += promoted.price;
            }

            if (
              promoted.days_to_expire >= 22 &&
              promoted.days_to_expire <= 26
            ) {
              acc["22 - 26 dias"].quantity += promoted.price;
            }

            if (
              promoted.days_to_expire >= 27 &&
              promoted.days_to_expire <= 31
            ) {
              acc["27 - 31 dias"].quantity += promoted.price;
            }
          }
          return acc;
        },
        {
          "2 - 6 dias": { quantity: 0 },
          "7 - 11 dias": { quantity: 0 },
          "12 - 16 dias": { quantity: 0 },
          "17 - 21 dias": { quantity: 0 },
          "22 - 26 dias": { quantity: 0 },
          "27 - 31 dias": { quantity: 0 },
        }
      );

      const knowbiePointsByDaysArray = Object.keys(knowbiePointsByDays).map(
        (key) => {
          return {
            label: key,
            quantity: knowbiePointsByDays[key].quantity,
          };
        }
      );

      const total = promotedSessions.reduce((acc, promoted) => {
        if (this.dateProvider.compareIfSameDay(promoted.createdAt, date)) {
          acc += promoted.price;
        }
        return acc;
      }, 0);

      return { total, list: knowbiePointsByDaysArray };
    }

    if (filter === "games") {
      const statements = await this.playerRepository.listStatements();

      const games = await this.gamesRepository.listAllActives();
      const accGames = games.reduce((acc, game) => {
        acc[game.name] = { quantity: 0 };
        return acc;
      }, {});

      if (type === "days") {
        const knowbiePointsByGame = statements.reduce((acc, statement) => {
          const { title, quantity, type, createdAt } = statement;

          if (
            title === "Venda de sessão" &&
            type === "income" &&
            this.dateProvider.compareIfSameDay(createdAt, date)
          ) {
            const game = statement.Session.Game.name;
            acc[game].quantity += quantity;
          }

          return acc;
        }, accGames);

        const knowbiePointsByGameArray = Object.keys(knowbiePointsByGame).map(
          (key) => {
            return {
              label: key,
              quantity: knowbiePointsByGame[key].quantity,
            };
          }
        );

        const total = statements.reduce((acc, statement) => {
          const { title, quantity, type, createdAt } = statement;

          if (
            title === "Venda de sessão" &&
            type === "income" &&
            this.dateProvider.compareIfSameDay(createdAt, date)
          ) {
            acc += quantity;
          }

          return acc;
        }, 0);

        return { total, list: knowbiePointsByGameArray };
      }

      if (type === "months") {
        const knowbiePointsByGame = statements.reduce((acc, statement) => {
          const { title, quantity, type, createdAt } = statement;

          if (
            title === "Venda de sessão" &&
            type === "income" &&
            this.dateProvider.compareIfSameMonth(createdAt, date)
          ) {
            const game = statement.Session.Game.name;
            acc[game].quantity += quantity;
          }

          return acc;
        }, accGames);

        const knowbiePointsByGameArray = Object.keys(knowbiePointsByGame).map(
          (key) => {
            return {
              label: key,
              quantity: knowbiePointsByGame[key].quantity,
            };
          }
        );

        const total = statements.reduce((acc, statement) => {
          const { title, quantity, type, createdAt } = statement;

          if (
            title === "Venda de sessão" &&
            type === "income" &&
            this.dateProvider.compareIfSameMonth(createdAt, date)
          ) {
            acc += quantity;
          }

          return acc;
        }, 0);

        return { total, list: knowbiePointsByGameArray };
      }
    }

    return [];
  }
}
