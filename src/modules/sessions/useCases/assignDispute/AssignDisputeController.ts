import { Request, Response } from "express";
import { container } from "tsyringe";

import { AssignDisputeUseCase } from "./AssignDisputeUseCase";

export class AssignDisputeController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { dispute_id } = request.params;

    const assignDisputeUseCase = container.resolve(AssignDisputeUseCase);

    const dispute = await assignDisputeUseCase.execute({
      dispute_id: Number(dispute_id),
      user_id: Number(id),
    });

    return response.json(dispute);
  }
}
