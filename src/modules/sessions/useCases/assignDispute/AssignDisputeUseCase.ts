import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  user_id: number;
  dispute_id: number;
}

@injectable()
export class AssignDisputeUseCase {
  constructor(
    @inject("UserRepository") private userRepository: IUserRepository,
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ dispute_id, user_id }: IRequest): Promise<any> {
    const dispute = await this.sessionRepository.findDisputeById(dispute_id);

    if (!dispute) {
      throw new AppError("Dispute not found");
    }

    if (dispute.user_id) {
      throw new AppError("Dispute already assigned");
    }
    
    const dateCreate = dispute.createdAt.getTime();
    const dateNow = new Date().getTime();
    const diffInDays = (dateNow - dateCreate) / (1000 * 60 * 60 * 24);
    if (dispute.mentor_description || diffInDays > 3){
        const assignedDispute = await this.sessionRepository.updateDispute({
        dispute_id,
        user_id,
        status: "in_progress",
      });

      const player = await this.playerRepository.findById(
        assignedDispute.player_id
      );

      const session = await this.sessionRepository.findById(
        assignedDispute.session_id
      );

      const sessionOwner = await this.playerRepository.findById(
        session.player_id
      );

      const moderator =
        assignedDispute.user_id &&
        (await this.userRepository.findById(assignedDispute.user_id));

      return {
        id: assignedDispute.id,
        status: assignedDispute.status,
        decision: assignedDispute.decision,
        player_id: assignedDispute.player_id,
        player: player.name || player.nickname,
        request_date: assignedDispute.createdAt,
        price: session.price,
        player_description: assignedDispute.player_description,
        player_attachments: assignedDispute.player_attachment,
        player_attachments_url: assignedDispute.player_attachment
          ? encodeURI(
              `${process.env.AWS_BUCKET_URL}/disputes/${assignedDispute.player_attachment}`
            )
          : null,
        mentor_id: sessionOwner.id,
        mentor: sessionOwner.name || sessionOwner.nickname,
        answer_date: assignedDispute.mentor_response_date,
        mentor_description: assignedDispute.mentor_description,
        mentor_attachments: assignedDispute.mentor_attachment,
        mentor_attachments_url: assignedDispute.mentor_attachment
          ? encodeURI(
              `${process.env.AWS_BUCKET_URL}/disputes/${assignedDispute.mentor_attachment}`
            )
          : null,
        moderator_id: moderator?.id || null,
        moderator_name: moderator?.name || null,
        moderator_description: assignedDispute.moderator_description,
        resolution_date: assignedDispute.resolution_date,
      };
    }else{
      throw new AppError("mentor did not respond and the dispute did not last 72 hours", 400 )
    }
    
  }
}
