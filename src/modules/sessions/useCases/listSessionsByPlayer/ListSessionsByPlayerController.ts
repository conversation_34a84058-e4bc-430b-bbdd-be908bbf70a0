import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListSessionsByPlayerUseCase } from "./ListSessionsByPlayerUseCase";

export class ListSessionsByPlayerController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { date, limit, page, expired, only_approved } = request.query;

    const listSessionsByPlayerUseCase = container.resolve(
      ListSessionsByPlayerUseCase
    );

    const sessions = await listSessionsByPlayerUseCase.execute({
      date: new Date(date as string),
      player_id: Number(id),
      limit: Number(limit),
      page: Number(page),
      expired_sessions: <PERSON><PERSON>an(expired),
      only_approved: <PERSON><PERSON><PERSON>(only_approved),
    });

    return response.json(sessions);
  }
}
