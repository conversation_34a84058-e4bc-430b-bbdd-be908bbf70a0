import { RRule } from "rrule";
import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

interface IRequest {
  player_id: number;
  date: Date;
  limit: number;
  page: number;
  expired_sessions: boolean;
  only_approved: boolean;
}

@injectable()
export class ListSessionsByPlayerUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({
    date,
    player_id,
    limit,
    page,
    only_approved,
  }: IRequest): Promise<any> {
    const weekdays = [
      "segunda",
      "terca",
      "quarta",
      "quinta",
      "sexta",
      "sabado",
      "domingo",
    ];

    const freq = ["ano", "mes", "semana", "dia"];

    const player = await this.playerRepository.findById(player_id);

    const recurring_sessions =
      await this.sessionRepository.listRecurringSessions();

    player.recurring_sessions = recurring_sessions;
    const appointments = player.sessions.filter((session) => {
      return only_approved
        ? session.player_id === player_id &&
            session.deletedAt == null &&
            session.canceledAt == null &&
            this.dateProvider.compareIfBefore(date, session.start_time) &&
            session.players_seats?.some((seat) => {
              return seat.Payment_info?.some((pay) => {
                return pay.status === "approved";
              });
            })
        : session.player_id === player_id &&
            session.deletedAt == null &&
            session.canceledAt == null &&
            this.dateProvider.compareIfBefore(date, session.start_time);
    });

    const appointmentsMapped = appointments.map((session) => {
      const repeat =
        player.recurring_sessions &&
        player.recurring_sessions
          .map((recurring) => {
            const rule = RRule.fromText(recurring.pattern);
            return (
              (recurring.session_id === session.id ||
                recurring.session_id === session.session_base_id) && {
                freq: freq[rule.options.freq],
                interval: rule.options.interval,
                byweekday: rule.options.byweekday
                  ? rule.options.byweekday.map((day) => weekdays[day])
                  : [],
                until: rule.options.until,
                count: rule.options.count,
              }
            );
          })
          .filter((recurring) => recurring);

      return {
        id: session.id,
        isPromoted:
          session.promoted_session[session.promoted_session.length - 1]
            ?.active ?? false,
        session_base_id: session.session_base_id,
        start_time: session.start_time,
        end_time: session.end_time,
        isActive: session.isActive,
        deletedAt: session.deletedAt,
        canceledAt: session.canceledAt,
        price: session.price,
        confirmations: session.players_seats.reduce((acc, seat) => {
          return seat.isConfirmed ? acc + 1 : acc;
        }, 0),
        seats: session.seats,
        pro_player_id: session.player_id,
        communication_link: session.communication_link,
        game_id: session.Game.id,
        game_logo:
          session.Game.logo &&
          encodeURI(
            `${process.env.AWS_BUCKET_URL}/games/logo/${session.Game.logo}`
          ),
        platform_id: session.platform_id,
        input_id: session.input_id,
        players: session.players_seats
          .map((player) => {
            return {
              player_id: player.Player.id,
              confirmed: player.isConfirmed,
              nickname: player.Player.nickname,
              Payment_info: player.Payment_info,
              avatar:
                player.Player.avatar &&
                encodeURI(
                  `${process.env.AWS_BUCKET_URL}/avatar/${player.Player.avatar}`
                ),
            };
          })
          .sort((a, b) => +b.confirmed - +a.confirmed),
        repeat: repeat ? repeat[0] : null,
      };
    });

    const all_appointments = [...appointmentsMapped].sort(
      (a, b) => +a.start_time - +b.start_time
    );

    if (page && limit) {
      return all_appointments.slice((page - 1) * limit, page * limit);
    }

    return all_appointments.slice(0, 60);
  }
}
