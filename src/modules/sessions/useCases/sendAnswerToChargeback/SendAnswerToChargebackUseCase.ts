import fs from "fs";
import { resolve } from "path";
import { inject, injectable } from "tsyringe";

import upload from "@config/upload";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { IStorageProvider } from "@shared/container/providers/StorageProvider/IStorageProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  chargeback_id: number;
  mentor_response: string;
  mentor_attachment?: string;
}

@injectable()
export class SendAnswerToChargebackUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("StorageProvider") private storageProvider: IStorageProvider,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({
    chargeback_id,
    mentor_response,
    mentor_attachment
  }: IRequest): Promise<any> {
    const chargeback = await this.sessionRepository.findChargebackInfoById(chargeback_id);

    if (!chargeback) {
      throw new AppError("Chargeback not found");
    }
    if(chargeback.mentor_requested_response != true){
      throw new AppError("Mentor response is not requested")
    }
    if (mentor_attachment) {
      const oldPath = resolve(`${upload.tmpFolder}`, mentor_attachment);
      const newDir = resolve(`${upload.tmpFolder}/resized`);
      const newPath = resolve(newDir, mentor_attachment);
      try {
        await fs.promises.access(oldPath);
      } catch (error) {
        throw new AppError("File not found");
      }
      try {
        await fs.promises.access(newDir);
      } catch (error) {
        await fs.promises.mkdir(newDir, { recursive: true });
      }
      await fs.promises.rename(oldPath, newPath);
      await this.storageProvider.save(mentor_attachment, "disputes");
    }
    const updatedChargeback = await this.sessionRepository.sendAnswerChargeback({
      chargeback_id: chargeback.id,
      mentor_response,
      mentor_attachment,
      mentor_response_date: new Date(),
    });
    return updatedChargeback
  }
}
