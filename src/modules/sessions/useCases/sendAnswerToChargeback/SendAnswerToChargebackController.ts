import { Request, Response } from "express";
import { container } from "tsyringe";
import { SendAnswerToChargebackUseCase } from "./SendAnswerToChargebackUseCase";


export class SendAnswerToChargebackController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { chargeback_id } = request.params;
    const { mentor_response } = request.body;
    const mentor_attachment = request.file?.filename;

    const sendAnswerToChargebackUseCase = container.resolve(SendAnswerToChargebackUseCase);

    const chargeback = await sendAnswerToChargebackUseCase.execute({
      chargeback_id: Number(chargeback_id),
      mentor_response,
      mentor_attachment
    });

    return response.json(chargeback);
  }
}
