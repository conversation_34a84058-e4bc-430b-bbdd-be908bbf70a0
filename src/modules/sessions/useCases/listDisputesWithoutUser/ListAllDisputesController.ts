import { Request, Response } from "express";
import { container } from "tsyringe";
import { ListAllDisputesUseCase } from "./ListAllDisputesUseCase";

export class ListAllDisputesController {
    async handle(request: Request, response: Response): Promise<Response> {
        const listAllDisputesUseCase = container.resolve(
            ListAllDisputesUseCase
        );
        const allDisputes = await listAllDisputesUseCase.execute();
    
        return response.json(allDisputes);
      };
};
