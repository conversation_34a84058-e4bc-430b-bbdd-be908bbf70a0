import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";


@injectable()
export class ListAllDisputesUseCase {
    constructor(@inject("SessionRepository") private sessionsRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,) {}
    async execute(): Promise<any> {
        const disputes = await this.sessionsRepository.listAllDisputes();
        
        const disputesWithMentorNames = await Promise.all(
            disputes.map(async (dispute) => {
                const mentor = await this.playerRepository.findById(dispute.Session.player_id);
                return {
                    ...dispute,
                    mentor_name: mentor?.name || mentor?.nickname || '<PERSON><PERSON> desconhecido',
                };
            })
        );
    
        const mappedDisputes = disputesWithMentorNames.map((dispute) => ({
            id: dispute.id,
            player_id: dispute.player_id,
            name_player: dispute.Player.name || dispute.Player.nickname,
            mentor_id: dispute.Session.player_id,
            mentor_name: dispute.mentor_name,
            createdAt: dispute.createdAt,
            status: dispute.status,
            analyst: dispute.user_id,
            // player_description: dispute.player_description,
            // decision: dispute.decision,
            // resolution_date: dispute.resolution_date,
            // player_attachment: dispute.player_attachment,
            // mentor_description: dispute.mentor_description,
            // mentor_attachment: dispute.mentor_attachment,
            // mentor_response_date: dispute.mentor_response_date,
            // moderator_description: dispute.moderator_description,
            // winner_id: dispute.winner_id,
        }));
    
        return mappedDisputes;
    };
};    