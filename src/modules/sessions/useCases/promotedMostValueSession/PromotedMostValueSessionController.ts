import { Request, Response } from "express";
import { container } from "tsyringe";
import { PromotedMostValueSessionsUseCase } from "./PromotedMostValueSessionUseCase";

export class PromotedMostValueSessionsController {
  async handle(request: Request, response: Response): Promise<Response> {
    
    const promotedMostValueSessionsUseCase = container.resolve(
        PromotedMostValueSessionsUseCase
    );
    const mostValuePromotedSessions = await promotedMostValueSessionsUseCase.execute();

    return response.json(mostValuePromotedSessions);
  }
}
