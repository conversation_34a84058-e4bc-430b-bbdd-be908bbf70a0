import "dayjs/locale/pt-br";
import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";

@injectable()
export class PromotedMostValueSessionsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(): Promise<any> {
    const promotedSessions = await this.sessionRepository.listPromotedSessions();

    if (promotedSessions.length === 0) {
      return [];
    }

    const maxPrice = Math.max(...promotedSessions.map(session => session.price_per_day));

    const mostExpensiveSessions = promotedSessions.filter(session => session.price_per_day === maxPrice);
  
    return mostExpensiveSessions;
  }
}
