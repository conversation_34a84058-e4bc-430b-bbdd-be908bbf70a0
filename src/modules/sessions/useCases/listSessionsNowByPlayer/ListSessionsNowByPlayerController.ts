import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListSessionsNowByPlayerUseCase } from "./ListSessionsNowByPlayerUseCase";

export class ListSessionsNowByPlayerController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;

    const listSessionsRecuringByPlayerUseCase = container.resolve(
      ListSessionsNowByPlayerUseCase
    );

    const sessions = await listSessionsRecuringByPlayerUseCase.execute({
      player_id: Number(id)
    });

    return response.json(sessions);
  }
}
