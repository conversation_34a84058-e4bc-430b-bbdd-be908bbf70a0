import { inject, injectable } from "tsyringe";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";

interface IRequest {
  player_id: number;
}

@injectable()
export class ListSessionsNowByPlayerUseCase {
  constructor(
    @inject("SessionRepository")
    private sessionRepository: ISessionRepository
  ) {}

  async execute({ player_id }: IRequest): Promise<any> {    
    const sessionsnow = await this.sessionRepository.listSessionsNowByPlayer(player_id);
    
    
    return sessionsnow;
    
  }
}
