import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

interface IRequest {
  player_id: number;
}

@injectable()
export class ListDisputesUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({
    player_id,
  }: IRequest): Promise<any> {
    const disputes = await this.sessionRepository.listDisputes();
  
    const mappedDisputes = disputes.map((dispute) => {
      return {
        id: dispute.id,
        session: dispute.session_id,
        title: `Disputa ${dispute.id}`,
        name: dispute.Player.name || dispute.Player.nickname,
        status: dispute.status,
        player_description: dispute.player_description,
        decision: dispute.decision,
        resolution_date: dispute.resolution_date,
        player_attachment: dispute.player_attachment,
        mentor_description: dispute.mentor_description,
        mentor_attachment: dispute.mentor_attachment,
        mentor_response_date: dispute.mentor_response_date,
        moderator_description: dispute.moderator_description,
        winner_id: dispute.winner_id,
        createdAt: dispute.createdAt,
        player_id: dispute.player_id,
      };
    });
    
    const filteredDisputes = mappedDisputes.filter((dispute) => dispute.player_id === player_id);
  
    return filteredDisputes;
  };};