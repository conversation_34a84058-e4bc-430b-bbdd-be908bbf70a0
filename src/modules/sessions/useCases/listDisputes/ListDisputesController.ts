import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListDisputesUseCase } from "./ListDisputesUseCase";

export class ListDisputesController {
  async handle(request: Request, response: Response): Promise<Response> {

    const { id } = request.user;

    const listDisputesUseCase = container.resolve(ListDisputesUseCase);

    const disputes = await listDisputesUseCase.execute({
      player_id: Number(id),
    });

    return response.json(disputes);
  }
}
