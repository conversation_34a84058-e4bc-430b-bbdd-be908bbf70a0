import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { SessionMap } from "@modules/sessions/mapper/sessionMap";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  session_id: number;
  player_id: number;
}

@injectable()
export class GetSessionPaymentStatusUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ session_id, player_id }: IRequest): Promise<any> {
    const session = await this.sessionRepository.getSessionPaymentStatus(
      session_id,
      player_id
    );

    return session.players_seats;
  }
}
