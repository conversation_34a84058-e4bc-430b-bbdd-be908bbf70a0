import { Request, Response } from "express";
import { container } from "tsyringe";

import { GetSessionPaymentStatusUseCase } from "./GetSessionPaymentStatusUseCase";

export class GetSessionPaymentStatusController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { session_id } = request.params;
    const id = request.user?.id;

    const getSessionPaymentStatusUseCase = container.resolve(
      GetSessionPaymentStatusUseCase
    );

    const session = await getSessionPaymentStatusUseCase.execute({
      session_id: Number(session_id),
      player_id: Number(id),
    });

    return response.json(session);
  }
}
