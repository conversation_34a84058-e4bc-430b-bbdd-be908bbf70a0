import { RRule } from "rrule";
import { inject, injectable } from "tsyringe";

import { frequency, weekdays } from "@config/recurrence";
import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ICreateSessionDTO } from "@modules/sessions/dtos/ICreateSessionDTO";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class CreateSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("GamesRepository") private gameRepository: IGamesRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  // TODO: a player can only create one session if he has a game account linked
  async execute({
    player_id,
    end_time,
    game_id,
    communication_link,
    input_id,
    platform_id,
    price,
    seats,
    start_time,
    repeat,
    type_session,
  }: ICreateSessionDTO): Promise<any> {
    const sessionMinDuration = 1;
    const sessionMaxDuration = 4;
    const player = await this.playerRepository.findById(player_id);
    const playerSessions = await this.sessionRepository.listSessionsByPlayer(
      player_id
    );

    if (!player) {
      throw new AppError("Player not found");
    }

    if (!player.isPro) {
      throw new AppError("Player must be a pro");
    }

    const game = await this.gameRepository.findById(game_id);

    if (!game) {
      throw new AppError("Game not found");
    }

    const playerHasMentoringGames = player.Mentoring_games.find(
      (game) => game.game_id === game_id
    );

    if (!playerHasMentoringGames) {
      throw new AppError("Player must have this game in his mentoring list");
    }

    if (!playerHasMentoringGames.active) {
      throw new AppError("Player has disabled this game");
    }

    const compareIfStartDateIsInThePast = this.dateProvider.compareIfBefore(
      start_time,
      new Date()
    );

    if (compareIfStartDateIsInThePast) {
      throw new AppError("Start time must be in the future");
    }

    const compareIfEndDateIsBeforeStartDate = this.dateProvider.compareIfBefore(
      end_time,
      start_time
    );

    if (compareIfEndDateIsBeforeStartDate) {
      throw new AppError("End time must be after start time");
    }

    let compareIfRepeatUntilIsInThePast;
    if (repeat && repeat.until) {
      compareIfRepeatUntilIsInThePast = this.dateProvider.compareIfBefore(
        repeat.until,
        start_time
      );
    }

    if (compareIfRepeatUntilIsInThePast) {
      throw new AppError("Until variable must be after start time");
    }

    const playerHasSessionAtSameTime = playerSessions.filter((session) =>
      this.dateProvider.compareIfBetween(
        session.start_time,
        new Date(start_time),
        session.end_time
      )
    ).length;

    if (playerHasSessionAtSameTime > 0) {
      throw new AppError("Player already has a session at this time");
    }

    const sessionDuration = this.dateProvider.compareInHours(
      start_time,
      end_time
    );

    if (
      sessionDuration < sessionMinDuration ||
      sessionDuration > sessionMaxDuration
    ) {
      throw new AppError("Session duration must be between 1 and 4 hours");
    }

    const session = await this.sessionRepository.create({
      player_id,
      end_time: new Date(end_time),
      game_id,
      communication_link,
      input_id,
      platform_id,
      price,
      seats,
      start_time: new Date(start_time),
      type_session,
    });

    if (repeat) {
      const byweekday = repeat.byweekday?.map((item) => {
        return RRule[weekdays[item]];
      });

      const rule = new RRule({
        freq: RRule[frequency[repeat.freq]],
        interval: repeat.interval,
        dtstart: new Date(start_time),
        byweekday,
        until: repeat.until ? new Date(repeat.until) : null,
        count: repeat.count,
      });

      const pattern = rule.toText();
      const firstSessions = rule.all((date, i) => i < 30); // gen 30 first sessions
      const nextGenDate = firstSessions[29]; // get the last session date

      console.log({ firstSessionsLEN: firstSessions.length, nextGenDate });

      const playerSessionDates = playerSessions.map(
        (session) => session.start_time
      );

      const updatedDate = firstSessions.filter((date) => {
        console.log({
          date,
          exist: !playerSessionDates.find(
            (playerDate) => +playerDate === +date
          ),
        });
        return !playerSessionDates.find((playerDate) => +playerDate === +date);
      }); // remove duplicates

      await this.sessionRepository.saveRecurrence(
        session.id,
        pattern,
        start_time,
        nextGenDate,
        repeat.until
      );

      const isSameDay = this.dateProvider.compareIfEqual(
        updatedDate[0],
        start_time
      );

      let data;

      if (isSameDay) {
        // If isSameDay is true, we need to jump to the next day
        data = updatedDate.slice(1, updatedDate.length).map((date) => {
          return {
            player_id,
            start_time: new Date(date),
            end_time: this.dateProvider.addHours(
              sessionDuration,
              new Date(date)
            ),
            game_id,
            price,
            seats,
            input_id,
            platform_id,
            type_session,
            session_base_id: session.id,
          };
        });
      } else {
        data = updatedDate.map((date) => {
          return {
            player_id,
            start_time: new Date(date),
            end_time: this.dateProvider.addHours(
              sessionDuration,
              new Date(date)
            ),
            game_id,
            price,
            seats,
            input_id,
            platform_id,
            type_session,
            session_base_id: session.id,
          };
        });
      }

      await this.sessionRepository.createManySessions(data);
    }

    return {
      ...session,
      game_logo: encodeURI(
        `${process.env.AWS_BUCKET_URL}/games/logo/${game.logo}`
      ),
    };
  }
}
