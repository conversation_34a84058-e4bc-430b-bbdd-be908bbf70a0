import { Request, Response } from "express";
import { container } from "tsyringe";

import { CreateSessionUseCase } from "./CreateSessionUseCase";

export class CreateSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const {
      end_time,
      game_id,
      price,
      seats,
      start_time,
      repeat,
      communication_link,
      input_id,
      platform_id,
      type_session,
    } = request.body;

    const createSessionUseCase = container.resolve(CreateSessionUseCase);
    const session = await createSessionUseCase.execute({
      player_id: Number(id),
      end_time,
      game_id,
      communication_link,
      input_id,
      platform_id,
      price,
      seats,
      start_time,
      repeat,
      type_session,
    });

    return response.json(session);
  }
}
