import { inject, injectable } from "tsyringe";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";

interface IRequest {
  mentor_id: number;
}

@injectable()
export class ListSessionsNowByMentorUseCase {
  constructor(
    @inject("SessionRepository")
    private sessionRepository: ISessionRepository
  ) {}

  async execute({ mentor_id }: IRequest): Promise<any> {    
    const sessionsnow = await this.sessionRepository.listSessionsNowByMentor(mentor_id);
    
    
    return sessionsnow;
    
  }
}
