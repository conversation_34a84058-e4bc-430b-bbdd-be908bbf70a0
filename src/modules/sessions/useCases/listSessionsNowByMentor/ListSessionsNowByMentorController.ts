import { Request, Response } from "express";
import { container } from "tsyringe";
import { ListSessionsNowByMentorUseCase } from "./ListSessionsNowByMentorUseCase";

export class ListSessionsNowByMentorController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;

    const listSessionsRecuringByMentorUseCase = container.resolve(
      ListSessionsNowByMentorUseCase
    );

    const sessions = await listSessionsRecuringByMentorUseCase.execute({
      mentor_id: Number(id)
    });

    return response.json(sessions);
  }
}
