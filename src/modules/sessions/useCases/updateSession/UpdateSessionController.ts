import { Request, Response } from "express";
import { container } from "tsyringe";

import { UpdateSessionUseCase } from "./UpdateSessionUseCase";

export class UpdateSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const id = request.user?.id;
    const { session_id } = request.params;

    const {
      start_time,
      end_time,
      game_id,
      communication_link,
      platform_id,
      input_id,
      price,
      seats,
      repeat,
      editOptions,
      session_base_id,
      type_session,
    } = request.body;

    const updateSessionUseCase = container.resolve(UpdateSessionUseCase);

    const session = await updateSessionUseCase.execute({
      session_id: Number(session_id),
      player_id: Number(id),
      start_time,
      end_time,
      game_id,
      communication_link,
      platform_id,
      input_id,
      price,
      seats,
      repeat,
      editOptions,
      session_base_id,
      type_session,
    });

    return response.json(session);
  }
}
