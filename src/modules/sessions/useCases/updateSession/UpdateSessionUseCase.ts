import { container, inject, injectable } from "tsyringe";

import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";
import { IUpdateSessionDTO } from "@modules/sessions/dtos/IUpdateSessionDTO";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

import { CreateSessionUseCase } from "../createSession/CreateSessionUseCase";
import { DeleteSessionUseCase } from "../deleteSession/DeleteSessionUseCase";

@injectable()
export class UpdateSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("GamesRepository") private gameRepository: IGamesRepository
  ) {}

  async execute({
    session_id,
    player_id,
    start_time,
    end_time,
    game_id,
    communication_link,
    platform_id,
    input_id,
    price,
    seats,
    session_base_id,
    repeat,
    editOptions = "only",
    type_session,
  }: IUpdateSessionDTO): Promise<any> {
    const session = await this.sessionRepository.findById(session_id);
    const deleteSessionUseCase = container.resolve(DeleteSessionUseCase);
    const createSessionUseCase = container.resolve(CreateSessionUseCase);

    if (!session) {
      throw new AppError("Session not found");
    }

    if (player_id && session.player_id !== player_id) {
      throw new AppError("Player is not the owner of this session");
    }

    if (!session.isActive) {
      throw new AppError("Session is not active");
    }

    if (game_id) {
      const game = await this.gameRepository.findById(game_id);

      if (!game) {
        throw new AppError("Game not found");
      }
    }

    if (start_time) {
      const compareIfStartDateIsInThePast = this.dateProvider.compareIfBefore(
        start_time,
        new Date()
      );

      if (compareIfStartDateIsInThePast) {
        throw new AppError("Start time must be in the future");
      }
    }

    if (end_time) {
      const compareIfEndDateIsBeforeStartDate =
        this.dateProvider.compareIfBefore(end_time, start_time);

      if (compareIfEndDateIsBeforeStartDate) {
        throw new AppError("End time must be after start time");
      }
    }

    if (editOptions === "communication_link") {
      const updatedSession = await this.sessionRepository.updateSession({
        session_id,
        communication_link,
      });

      const hasRecurrence =
        await this.sessionRepository.findRecurrenceBySessionId(session.id);

      if (!hasRecurrence && !session.session_base_id) {
        if (repeat) {
          await this.sessionRepository.deleteSession(session.id);

          await createSessionUseCase.execute({
            player_id,
            start_time: updatedSession.start_time,
            end_time: updatedSession.end_time,
            game_id: updatedSession.game_id,
            communication_link: updatedSession.communication_link,
            platform_id: updatedSession.platform_id,
            input_id: updatedSession.input_id,
            price: updatedSession.price,
            seats: updatedSession.seats,
            repeat,
            type_session,
          });
        }
      }

      return updatedSession;
    }
    if (editOptions === "only") {
      if (session.players_seats.length > 0) {
        throw new AppError("Session already scheduled", 455); // 455 = session already scheduled
      }

      const updatedSession = await this.sessionRepository.updateSession({
        start_time,
        end_time,
        session_id,
        communication_link,
        input_id,
        platform_id,
        game_id,
        price,
        seats,
        session_base_id,
        type_session,
      });

      const hasRecurrence =
        await this.sessionRepository.findRecurrenceBySessionId(session.id);

      if (!hasRecurrence && !session.session_base_id) {
        if (repeat) {
          await this.sessionRepository.deleteSession(session.id);

          await createSessionUseCase.execute({
            player_id,
            start_time: updatedSession.start_time,
            end_time: updatedSession.end_time,
            game_id: updatedSession.game_id,
            communication_link: updatedSession.communication_link,
            platform_id: updatedSession.platform_id,
            input_id: updatedSession.input_id,
            price: updatedSession.price,
            seats: updatedSession.seats,
            repeat,
            type_session,
          });
        }
      }

      return updatedSession;
    }

    if (editOptions === "all") {
      if (session.session_base_id) {
        const sessionBase = await this.sessionRepository.findById(session.session_base_id);
    
        if (repeat) {
          await deleteSessionUseCase.execute({
            player_id,
            session_id: session.session_base_id,
            options: "all",
          });
    
          await createSessionUseCase.execute({
            player_id,
            end_time: end_time || sessionBase.end_time,
            start_time: start_time || sessionBase.start_time,
            game_id: game_id || sessionBase.game_id,
            platform_id: platform_id || sessionBase.platform_id,
            input_id: input_id || sessionBase.input_id,
            price: price || sessionBase.price,
            seats: seats || sessionBase.seats,
            repeat,
            type_session,
          });
    
          return session;
        }
    
        const sessions = await this.sessionRepository.listSessionsWithRecurrence(session.session_base_id);
    
        await Promise.all(sessions.map(async (s) => {
          if (s.players_seats.length <= 0) {
            await this.sessionRepository.updateSession({
              start_time,
              end_time,
              session_id: s.id,
              communication_link,
              platform_id,
              input_id,
              game_id,
              price,
              seats,
              type_session,
            });
          }
        }));
    
        return session;
      }
    
      if (repeat) {
        await deleteSessionUseCase.execute({
          player_id,
          session_id: session.id,
          options: "all",
        });
    
        await createSessionUseCase.execute({
          player_id,
          end_time: end_time || session.end_time,
          start_time: start_time || session.start_time,
          game_id: game_id || session.game_id,
          communication_link,
          platform_id,
          input_id,
          price: price || session.price,
          seats: seats || session.seats,
          repeat,
          type_session,
        });
    
        return session;
      }
    
      await this.sessionRepository.updateSession({
        start_time,
        end_time,
        session_id: session.id,
        communication_link,
        input_id,
        platform_id,
        game_id,
        price,
        seats,
        type_session,
      });
      if (session.session_base_id) {
        if (repeat) {
          await deleteSessionUseCase.execute({
            player_id,
            session_id: session.id,
            options: "next",
          });
    
          await createSessionUseCase.execute({
            player_id,
            end_time: end_time || session.end_time,
            start_time: start_time || session.start_time,
            game_id: game_id || session.game_id,
            price: price || session.price,
            seats: seats || session.seats,
            repeat,
            type_session,
          });
    
          return session;
        }
    
        const sessions = await this.sessionRepository.listSessionsWithRecurrence(session.session_base_id);
    
        await Promise.all(sessions.map(async (s) => {
          if (!this.dateProvider.compareIfBefore(s.start_time, session.start_time)) {
            if (s.players_seats.length <= 0) {
              await this.sessionRepository.updateSession({
                start_time,
                end_time,
                session_id: s.id,
                communication_link,
                input_id,
                platform_id,
                game_id,
                price,
                seats,
                type_session,
              });
            }
          }
        }));
    
        const recurrence = await this.sessionRepository.findRecurrenceBySessionId(session.session_base_id);
    
        await this.sessionRepository.updateRecurrence({
          recurrence_id: recurrence.id,
        });
    
        return session;
      }
    
      if (repeat) {
        await deleteSessionUseCase.execute({
          player_id,
          session_id: session.id,
          options: "next",
        });
    
        await createSessionUseCase.execute({
          player_id,
          end_time: end_time || session.end_time,
          start_time: start_time || session.start_time,
          game_id: game_id || session.game_id,
          price: price || session.price,
          seats: seats || session.seats,
          repeat,
          type_session,
        });
    
        return session;
      }
    
      const sessions = await this.sessionRepository.listSessionsWithRecurrence(session.id);
    
      await Promise.all(sessions.map(async (s) => {
        if (!this.dateProvider.compareIfBefore(s.start_time, session.start_time)) {
          if (s.players_seats.length <= 0) {
            await this.sessionRepository.updateSession({
              start_time,
              end_time,
              session_id: s.id,
              communication_link,
              input_id,
              platform_id,
              game_id,
              price,
              seats,
              type_session,
            });
          }
        }
      }));
    
      const recurrence = await this.sessionRepository.findRecurrenceBySessionId(session.id);
    
      await this.sessionRepository.updateRecurrence({
        recurrence_id: recurrence.id,
      });

      return session;
    }
    
    if (editOptions === "next") {
      if (session.session_base_id) {
        if (repeat) {
          await deleteSessionUseCase.execute({
            player_id,
            session_id: session.id,
            options: "next",
          });
    
          await createSessionUseCase.execute({
            player_id,
            end_time: end_time || session.end_time,
            start_time: start_time || session.start_time,
            input_id: input_id || session.input_id,
            platform_id: platform_id || session.platform_id,
            game_id: game_id || session.game_id,
            price: price || session.price,
            seats: seats || session.seats,
            repeat,
            type_session,
          });
    
          return session;
        }
    
        const sessions = await this.sessionRepository.listSessionsWithRecurrence(session.session_base_id);
    
        await Promise.all(sessions.map(async (s) => {
          if (!this.dateProvider.compareIfBefore(s.start_time, session.start_time)) {
            if (s.players_seats.length <= 0) {
              await this.sessionRepository.updateSession({
                start_time,
                end_time,
                session_id: s.id,
                communication_link,
                input_id,
                platform_id,
                game_id,
                price,
                seats,
                type_session,
              });
            }
          }
        }));
    
        const recurrence = await this.sessionRepository.findRecurrenceBySessionId(session.session_base_id);
    
        await this.sessionRepository.updateRecurrence({
          recurrence_id: recurrence.id,
        });
    
        return session;
      }
    
      if (repeat) {
        await deleteSessionUseCase.execute({
          player_id,
          session_id: session.id,
          options: "next",
        });
    
        await createSessionUseCase.execute({
          player_id,
          end_time: end_time || session.end_time,
          start_time: start_time || session.start_time,
          input_id: input_id || session.input_id,
          platform_id: platform_id || session.platform_id,
          game_id: game_id || session.game_id,
          price: price || session.price,
          seats: seats || session.seats,
          repeat,
          type_session,
        });
    
        return session;
      }
    
      const sessions = await this.sessionRepository.listSessionsWithRecurrence(session.id);
    
      await Promise.all(sessions.map(async (s) => {
        if (!this.dateProvider.compareIfBefore(s.start_time, session.start_time)) {
          if (s.players_seats.length <= 0) {
            await this.sessionRepository.updateSession({
              start_time,
              end_time,
              session_id: s.id,
              communication_link,
              input_id,
              platform_id,
              game_id,
              price,
              seats,
              type_session,
            });
          }
        }
      }));
    
      const recurrence = await this.sessionRepository.findRecurrenceBySessionId(session.id);
    
      await this.sessionRepository.updateRecurrence({
        recurrence_id: recurrence.id,
      });
    
      return session;
    } 

    return session;
  }
}
