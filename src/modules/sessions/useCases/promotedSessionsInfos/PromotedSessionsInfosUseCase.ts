import dayjs from "dayjs";
import "dayjs/locale/pt-br";
import { inject, injectable } from "tsyringe";

import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

interface IRequest {
  filter: "games" | "period" | "status";
  type?: "days" | "months" | "active" | "paused" | "finished";
}

@injectable()
export class PromotedSessionsInfosUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("GamesRepository") private gamesRepository: IGamesRepository
  ) {}

  async execute({ filter, type }: IRequest): Promise<any> {
    const promoted_sessions =
      await this.sessionRepository.listPromotedSessions();

    if (promoted_sessions.length === 0) {
      return [];
    }

    if (filter === "games") {
      const games = await this.gamesRepository.listAllActives();
      const accGames = games.reduce((acc, game) => {
        acc[game.name] = { quantity: 0 };
        return acc;
      }, {});

      const sessionsByGame = promoted_sessions.reduce((acc, promoted) => {
        const { Game } = promoted.Session;

        acc[Game.name].quantity += 1;

        return acc;
      }, accGames);

      const sessionsByGameArray = Object.keys(sessionsByGame).map((key) => {
        return {
          label: key,
          quantity: sessionsByGame[key].quantity,
        };
      });

      return sessionsByGameArray;
    }

    if (filter === "period") {
      if (type === "days") {
        const sessionsByDays = promoted_sessions.reduce(
          (acc, promoted) => {
            if (promoted.days_to_expire >= 2 && promoted.days_to_expire <= 6) {
              acc["2 - 6 dias"].quantity += 1;
            }

            if (promoted.days_to_expire >= 7 && promoted.days_to_expire <= 11) {
              acc["7 - 11 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 12 &&
              promoted.days_to_expire <= 16
            ) {
              acc["12 - 16 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 17 &&
              promoted.days_to_expire <= 21
            ) {
              acc["17 - 21 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 22 &&
              promoted.days_to_expire <= 26
            ) {
              acc["22 - 26 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 27 &&
              promoted.days_to_expire <= 31
            ) {
              acc["27 - 31 dias"].quantity += 1;
            }

            return acc;
          },
          {
            "2 - 6 dias": { quantity: 0 },
            "7 - 11 dias": { quantity: 0 },
            "12 - 16 dias": { quantity: 0 },
            "17 - 21 dias": { quantity: 0 },
            "22 - 26 dias": { quantity: 0 },
            "27 - 31 dias": { quantity: 0 },
          }
        );

        const sessionsByDaysArray = Object.keys(sessionsByDays).map((key) => {
          return {
            label: key,
            quantity: sessionsByDays[key].quantity,
          };
        });

        return sessionsByDaysArray;
      }

      if (type === "months") {
        const sessionsByMonths = promoted_sessions.reduce(
          (acc, promoted) => {
            const { createdAt } = promoted;
            const month = createdAt.getMonth();
            if (!acc[month]) {
              acc[month] = { quantity: 0 };
            }
            acc[month].quantity += 1;

            return acc;
          },
          {
            0: { quantity: 0 },
            1: { quantity: 0 },
            2: { quantity: 0 },
            3: { quantity: 0 },
            4: { quantity: 0 },
            5: { quantity: 0 },
            6: { quantity: 0 },
            7: { quantity: 0 },
            8: { quantity: 0 },
            9: { quantity: 0 },
            10: { quantity: 0 },
            11: { quantity: 0 },
          }
        );

        const sessionsByMonthsArray = Object.keys(sessionsByMonths).map(
          (key) => {
            return {
              label: dayjs().month(Number(key)).locale("pt-br").format("MMM"),
              quantity: sessionsByMonths[key].quantity,
            };
          }
        );

        return sessionsByMonthsArray;
      }
    }

    if (filter === "status") {
      if (type === "active") {
        const activePromotedSessions = promoted_sessions.filter(
          (promoted) => promoted.active
        );

        const sessionsByDays = activePromotedSessions.reduce(
          (acc, promoted) => {
            if (promoted.days_to_expire >= 2 && promoted.days_to_expire <= 6) {
              acc["2 - 6 dias"].quantity += 1;
            }

            if (promoted.days_to_expire >= 7 && promoted.days_to_expire <= 11) {
              acc["7 - 11 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 12 &&
              promoted.days_to_expire <= 16
            ) {
              acc["12 - 16 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 17 &&
              promoted.days_to_expire <= 21
            ) {
              acc["17 - 21 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 22 &&
              promoted.days_to_expire <= 26
            ) {
              acc["22 - 26 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 27 &&
              promoted.days_to_expire <= 31
            ) {
              acc["27 - 31 dias"].quantity += 1;
            }

            return acc;
          },
          {
            "2 - 6 dias": { quantity: 0 },
            "7 - 11 dias": { quantity: 0 },
            "12 - 16 dias": { quantity: 0 },
            "17 - 21 dias": { quantity: 0 },
            "22 - 26 dias": { quantity: 0 },
            "27 - 31 dias": { quantity: 0 },
          }
        );

        const sessionsByDaysArray = Object.keys(sessionsByDays).map((key) => {
          return {
            label: key,
            quantity: sessionsByDays[key].quantity,
          };
        });

        return sessionsByDaysArray;
      }

      if (type === "paused") {
        const pausedPromotedSessions = promoted_sessions.filter(
          (promoted) =>
            !this.dateProvider.compareIfBefore(promoted.until, new Date()) &&
            !promoted.active
        );

        const sessionsByDays = pausedPromotedSessions.reduce(
          (acc, promoted) => {
            if (promoted.days_to_expire >= 2 && promoted.days_to_expire <= 6) {
              acc["2 - 6 dias"].quantity += 1;
            }

            if (promoted.days_to_expire >= 7 && promoted.days_to_expire <= 11) {
              acc["7 - 11 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 12 &&
              promoted.days_to_expire <= 16
            ) {
              acc["12 - 16 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 17 &&
              promoted.days_to_expire <= 21
            ) {
              acc["17 - 21 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 22 &&
              promoted.days_to_expire <= 26
            ) {
              acc["22 - 26 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 27 &&
              promoted.days_to_expire <= 31
            ) {
              acc["27 - 31 dias"].quantity += 1;
            }

            return acc;
          },
          {
            "2 - 6 dias": { quantity: 0 },
            "7 - 11 dias": { quantity: 0 },
            "12 - 16 dias": { quantity: 0 },
            "17 - 21 dias": { quantity: 0 },
            "22 - 26 dias": { quantity: 0 },
            "27 - 31 dias": { quantity: 0 },
          }
        );

        const sessionsByDaysArray = Object.keys(sessionsByDays).map((key) => {
          return {
            label: key,
            quantity: sessionsByDays[key].quantity,
          };
        });

        return sessionsByDaysArray;
      }

      if (type === "finished") {
        const finishedPromotedSessions = promoted_sessions.filter(
          (promoted) =>
            !promoted.active &&
            this.dateProvider.compareIfBefore(promoted.until, new Date())
        );

        const sessionsByDays = finishedPromotedSessions.reduce(
          (acc, promoted) => {
            if (promoted.days_to_expire >= 2 && promoted.days_to_expire <= 6) {
              acc["2 - 6 dias"].quantity += 1;
            }

            if (promoted.days_to_expire >= 7 && promoted.days_to_expire <= 11) {
              acc["7 - 11 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 12 &&
              promoted.days_to_expire <= 16
            ) {
              acc["12 - 16 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 17 &&
              promoted.days_to_expire <= 21
            ) {
              acc["17 - 21 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 22 &&
              promoted.days_to_expire <= 26
            ) {
              acc["22 - 26 dias"].quantity += 1;
            }

            if (
              promoted.days_to_expire >= 27 &&
              promoted.days_to_expire <= 31
            ) {
              acc["27 - 31 dias"].quantity += 1;
            }

            return acc;
          },
          {
            "2 - 6 dias": { quantity: 0 },
            "7 - 11 dias": { quantity: 0 },
            "12 - 16 dias": { quantity: 0 },
            "17 - 21 dias": { quantity: 0 },
            "22 - 26 dias": { quantity: 0 },
            "27 - 31 dias": { quantity: 0 },
          }
        );

        const sessionsByDaysArray = Object.keys(sessionsByDays).map((key) => {
          return {
            label: key,
            quantity: sessionsByDays[key].quantity,
          };
        });

        return sessionsByDaysArray;
      }
    }

    return [];
  }
}
