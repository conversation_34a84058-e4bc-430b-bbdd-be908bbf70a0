import { Request, Response } from "express";
import { container } from "tsyringe";

import { PromotedSessionsInfosUseCase } from "./PromotedSessionsInfosUseCase";

export class PromotedSessionsInfosController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { filter, type } = request.query;

    const promotedSessionsInfosUseCase = container.resolve(
      PromotedSessionsInfosUseCase
    );

    const infos = await promotedSessionsInfosUseCase.execute({
      filter: filter as "games" | "period",
      type: type as "days" | "months" | undefined,
    });

    return response.json(infos);
  }
}
