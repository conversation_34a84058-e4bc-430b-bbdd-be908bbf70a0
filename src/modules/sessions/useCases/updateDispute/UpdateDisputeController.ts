import { Request, Response } from "express";
import { container } from "tsyringe";

import { UpdateDisputeUseCase } from "./UpdateDisputeUseCase";

export class UpdateDisputeController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { dispute_id } = request.params;
    const { description } = request.body;

    const attachment = request.file?.filename;

    const updateDisputeUseCase = container.resolve(UpdateDisputeUseCase);

    const dispute = await updateDisputeUseCase.execute({
      dispute_id: Number(dispute_id),
      description,
      attachment,
    });

    return response.json(dispute);
  }
}
