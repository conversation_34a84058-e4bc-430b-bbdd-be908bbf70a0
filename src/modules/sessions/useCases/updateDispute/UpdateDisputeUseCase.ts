import fs from "fs";
import { resolve } from "path";
import { inject, injectable } from "tsyringe";

import upload from "@config/upload";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { IStorageProvider } from "@shared/container/providers/StorageProvider/IStorageProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  dispute_id: number;
  description: string;
  attachment?: string;
}

@injectable()
export class UpdateDisputeUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("StorageProvider") private storageProvider: IStorageProvider,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({
    dispute_id,
    description,
    attachment,
  }: IRequest): Promise<any> {
    const dispute = await this.sessionRepository.findDisputeById(dispute_id);

    if (!dispute) {
      throw new AppError("Dispute not found");
    }

    if (attachment) {
      await fs.promises.rename(
        resolve(`${upload.tmpFolder}`, attachment),
        resolve(`${upload.tmpFolder}/resized`, attachment)
      );
      await this.storageProvider.save(attachment, "disputes");
    }

    const updatedDispute = await this.sessionRepository.updateDispute({
      dispute_id,
      description,
      attachment,
      mentor_response_date: new Date(),
    });
    return updatedDispute
  }
}
