import { Request, Response } from "express";
import { container } from "tsyringe";

import { DeleteSessionUseCase } from "./DeleteSessionUseCase";

export class DeleteSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { session_id } = request.params;
    const { options } = request.body;

    const deleteSessionUseCase = container.resolve(DeleteSessionUseCase);

    await deleteSessionUseCase.execute({
      player_id: Number(id),
      session_id: Number(session_id),
      options,
    });

    return response.send();
  }
}
