import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  session_id: number;
  player_id: number;
  options: "all" | "next" | "only";
}

@injectable()
export class DeleteSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ player_id, session_id, options }: IRequest): Promise<void> {
    if (!options) {
      throw new AppError("Options is required");
    }

    const session = await this.sessionRepository.findById(session_id);

    if (!session) {
      throw new AppError("Session not found");
    }

    if (session.player_id !== player_id) {
      throw new AppError("Player not allowed to delete this session");
    }

    if (options === "only") {
      if (session.players_seats.length > 0) {
        throw new AppError("Session already scheduled", 455); // 455 = session already scheduled
      }

      const recurrence = await this.sessionRepository.findRecurrenceBySessionId(
        session.id
      );
      console.log(recurrence);

      if (recurrence) {
        const sessions =
          await this.sessionRepository.listSessionsWithRecurrence(session.id);

        await this.sessionRepository.updateRecurrence({
          recurrence_id: recurrence.id,
          session_id: sessions[0].id,
        });

        await this.sessionRepository.deleteSession(session.id);

        sessions.forEach(async (session, idx) => {
          if (idx > 0) {
            await this.sessionRepository.updateSession({
              session_id: session.id,
              session_base_id: sessions[0].id,
            });
          }
        });

        return;
      }

      await this.sessionRepository.deleteSession(session_id);
    }

    if (options === "all") {
      if (session.session_base_id) {
        const recurrence =
          await this.sessionRepository.findRecurrenceBySessionId(
            session.session_base_id
          );

        if (recurrence) {
          await this.sessionRepository.deleteRecurrence(recurrence.id);
        }

        const sessions =
          await this.sessionRepository.listSessionsWithRecurrence(
            session.session_base_id
          );

        sessions.forEach(async (session) => {
          if (session.players_seats.length <= 0) {
            await this.sessionRepository.deleteSession(session.id);
          }
        });

        const sessionBase = await this.sessionRepository.findById(
          session.session_base_id
        );

        if (sessionBase.players_seats.length <= 0) {
          await this.sessionRepository.deleteSession(session.session_base_id);
        }
      } else {
        const recurrence =
          await this.sessionRepository.findRecurrenceBySessionId(session.id);

        if (recurrence) {
          await this.sessionRepository.deleteRecurrence(recurrence.id);
        }

        const sessions =
          await this.sessionRepository.listSessionsWithRecurrence(session.id);

        sessions.forEach(async (session) => {
          if (session.players_seats.length <= 0) {
            await this.sessionRepository.deleteSession(session.id);
          }
        });

        if (session.players_seats.length <= 0) {
          await this.sessionRepository.deleteSession(session.id);
        }
      }
    }

    if (options === "next") {
      if (session.session_base_id) {
        const sessions =
          await this.sessionRepository.listSessionsWithRecurrence(
            session.session_base_id
          );

        sessions.map(async (s) => {
          if (
            !this.dateProvider.compareIfBefore(s.start_time, session.start_time)
          ) {
            if (s.players_seats.length <= 0) {
              await this.sessionRepository.deleteSession(s.id);
            }
          }
        });
      } else {
        const sessions =
          await this.sessionRepository.listSessionsWithRecurrence(session.id);

        await Promise.all(
          sessions.map(async (s) => {
            if (
              !this.dateProvider.compareIfBefore(
                s.start_time,
                session.start_time
              )
            ) {
              if (s.players_seats.length <= 0) {
                await this.sessionRepository.deleteSession(s.id);
              }
            }
          })
        );

        if (session.players_seats.length <= 0) {
          const recurrence =
            await this.sessionRepository.findRecurrenceBySessionId(session.id);
          await this.sessionRepository.deleteRecurrence(recurrence.id);
          await this.sessionRepository.deleteSession(session.id);
        }
      }
    }
  }
}
