import { inject, injectable } from "tsyringe";

import { IRateSessionDTO } from "@modules/sessions/dtos/IRateSessionDTO";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";

import { Rating } from ".prisma/client";

@injectable()
export class RateSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository
  ) {}

  async execute({
    description,
    player_id,
    points,
    session_id,
    tags,
    isAnonymous,
  }: IRateSessionDTO): Promise<Rating> {
    const session = await this.sessionRepository.findById(session_id);

    if (!session) {
      throw new AppError("Session not found");
    }

    let rating: Rating;

    console.log(`[tag] = ${tags} - [description] = ${description}`);
    if (tags && description) {
      rating = await this.sessionRepository.rateSession({
        description,
        player_id,
        points,
        session_id,
        tags,
        isAnonymous,
      });

      return rating;
    }

    if (tags) {
      rating = await this.sessionRepository.rateSession({
        player_id,
        points,
        session_id,
        tags,
      });

      return rating;
    }

    if (description) {
      rating = await this.sessionRepository.rateSession({
        description,
        player_id,
        points,
        session_id,
        isAnonymous,
      });

      return rating;
    }

    rating = await this.sessionRepository.rateSession({
      player_id,
      points,
      session_id,
    });

    return rating;
  }
}
