import { Request, Response } from "express";
import { container } from "tsyringe";

import { RateSessionUseCase } from "./RateSessionUseCase";

export class RateSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { description, player_id, points, session_id, tags, isAnonymous } =
      request.body;

    const rateSessionUseCase = container.resolve(RateSessionUseCase);
    const rating = await rateSessionUseCase.execute({
      description,
      player_id,
      points,
      session_id,
      tags,
      isAnonymous,
    });

    return response.json(rating);
  }
}
