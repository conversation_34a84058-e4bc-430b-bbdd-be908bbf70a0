import { Request, Response } from "express";
import { container } from "tsyringe";

import { CancelSeatUseCase } from "./CancelSeatUseCase";

export class CancelSeatController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { session_id } = request.params;
    const { cancelation_option_id, motive_field } = request.body;

    const cancelSeatUseCase = container.resolve(CancelSeatUseCase);

    const confirmCancelation = await cancelSeatUseCase.execute({
      player_id: Number(id),
      session_id: Number(session_id),
      cancelation_option_id,
      motive_field,
    });

    return response.json(confirmCancelation);
  }
}
