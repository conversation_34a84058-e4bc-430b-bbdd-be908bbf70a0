import { inject, injectable } from "tsyringe";

import { AppError } from "@shared/errors/AppError";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IJobsRepository } from "@modules/jobs/repositories/IJobsRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { IMercadoPagoProvider } from "@shared/container/providers/PaymentGatewayProvider/IMercadoPagoProvider";

interface IRequest {
  session_id: number;
  player_id: number;
  cancelation_option_id: number;
  motive_field: string;
}
// TODO: adicionar canceletion_option_id e motive_field
// TODO: verificar outros status de pagamento em que o reembolso deveria ser efetuado pelo gateway de pagamento
@injectable()
export class CancelSeatUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("JobsRepository") private jobsRepository: IJobsRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("MercadoPagoProvider")
    private mercadoPagoProvider: IMercadoPagoProvider
  ) {}

  async execute({
    player_id,
    session_id,
    cancelation_option_id,
    motive_field,
  }: IRequest): Promise<string> {
    try {
      const session = await this.sessionRepository.findById(session_id);

      const seat = await this.sessionRepository.findSeatBySessionAndPlayer(
        session_id,
        player_id
      );
      if (!seat) {
        throw new AppError("Player does not have this session", 404);
      }

      if (!session) {
        throw new AppError("Session not found", 404);
      }

      if (session.canceledAt) {
        throw new AppError("Session is canceled");
      }

      const now = new Date();
      const sessionHasOcurred = this.dateProvider.compareIfBefore(
        session.end_time,
        now
      );
      if (sessionHasOcurred) {
        throw new AppError("Session has already ended");
      }

      const twelveHoursFromNow = new Date(now.getTime() + 12 * 60 * 60 * 1000);

      if (session.start_time < twelveHoursFromNow) {
        throw new AppError(
          "You can only cancel a session 12 hours before it starts"
        );
      }

      // const statementBySession =
      //   await this.playerRepository.findLastStatementByPlayer(seat.id);

      if (!seat.Payment_info[0]?.status)
        throw new AppError("Payment Info is not on statement", 404);

      if (seat.Payment_info[0]?.status === "approved") {
        // if payment is pending we cannot refund
        if (seat.Payment_info[0]?.payment_from_wallet) {
          // if payment is from wallet, we need to refund to wallet
          await this.playerRepository.updateWallet(
            seat.player_id,
            "income",
            seat.Payment_info[0]?.payment_from_wallet
          );
        }
        if (seat.Payment_info[0]?.payment_from_external) {
          // if payment is from external payment API, we need to refund to original payment method
          await this.mercadoPagoProvider.createRefund(
            seat.Payment_info[0]?.external_payment_id
          );
        }
      }
      const amountWallet =
        seat.Payment_info[0].payment_from_wallet == null
          ? 0
          : seat.Payment_info[0].payment_from_wallet;
      const amountExternal =
        seat.Payment_info[0].payment_from_external == null
          ? 0
          : seat.Payment_info[0].payment_from_external;
      await this.playerRepository.saveStatement(
        "Compra de sessão",
        `${seat.Session.Player.nickname}`,
        amountWallet + amountExternal,
        "canceled",
        seat.player_id,
        seat.session_id,
        seat.Session.Player.id,
        seat.Payment_info[0].id
      ); // player statement

      const ownerStatement = await this.playerRepository.findStatementByPlayer(
        seat.Session.Player.id,
        "createdAt"
      );
      // TODO: testar com dois statements com status "pending"
      const [ownerStatementBySession] = ownerStatement
        .filter(
          (statement) =>
            statement.buyer_id === seat.player_id &&
            statement.session_id === seat.session_id &&
            statement.type === "pending"
        )
        .slice(-1);

      if (ownerStatementBySession) {
        await this.playerRepository.deleteStatementById(
          ownerStatementBySession.id
        );
      }
      /* await this.sessionRepository.deleteJob(seat.id) */

      await this.sessionRepository.deleteSeat(seat.id);
      return "Seat canceled";
    } catch (error) {
      throw new AppError(error.message, error.statusCode);
    }
  }
}
