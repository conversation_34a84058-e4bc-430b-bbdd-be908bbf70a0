import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";

@injectable()
export class ListCancelationOptionsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository
  ) {}

  async execute(): Promise<any> {
    const cancelation_options = await this.sessionRepository.listCancelationOptions();
    
    return cancelation_options
  }
}
