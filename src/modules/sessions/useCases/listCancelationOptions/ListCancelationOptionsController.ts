import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListCancelationOptionsUseCase } from "./ListCancelationOptionsUseCase";

export class ListCancelationOptionsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const listCancelationOptionsUseCase = container.resolve(ListCancelationOptionsUseCase);

    const cancelationOptions = await listCancelationOptionsUseCase.execute()

    return response.json(cancelationOptions);
  }
}
