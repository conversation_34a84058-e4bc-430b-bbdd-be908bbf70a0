import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  session_id: number;
  player_id: number;
}

@injectable()
export class FinishSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ session_id, player_id }: IRequest): Promise<unknown> {
    const session = await this.sessionRepository.findById(session_id);

    if (!session) {
      throw new AppError("Session not found");
    }
    if (!this.dateProvider.compareIfBefore(session.end_time, new Date())) {
      throw new AppError("Session is running");
    }
    const playerSeat = session.players_seats.find(
      (player) => player.player_id === player_id
    );

    if (!playerSeat) {
      throw new AppError("You are not on this session");
    }
    if (playerSeat.isFinished) {
      throw new AppError("You already finished this session");
    }
    if (playerSeat.hasDispute) {
      throw new AppError("Can't finish session with dispute");
    }
    if (playerSeat.hasChargeback) {
      throw new AppError("Can't finish session with Chargeback");
    }
    const playersOnSession = session.players_seats.length;
    const isSessionInGroup = playersOnSession > 1;

    if (!isSessionInGroup) {
      await this.sessionRepository.finishSession(session_id);
    }
    if (isSessionInGroup) {
      const finishedQty = session.players_seats.reduce(
        (acc, seat) => (seat.isFinished ? acc + 1 : acc),
        0
      );

      if (playersOnSession - finishedQty === 1) {
        await this.sessionRepository.finishSession(session_id);
      }
    }
    await this.sessionRepository.updateSeat({
      seat_id: playerSeat.id,
      isFinished: true,
    });

    const statementToDelete = await this.playerRepository.findPendingStatement(
      session.player_id,
      player_id,
      session_id
    );

    if (!statementToDelete) {
      throw new AppError("Statement not found");
    }

    await this.playerRepository.deleteStatementById(statementToDelete.id);

    const incomeValue = statementToDelete.quantity;

    const player = await this.playerRepository.findById(player_id);

    const promoted_session = await this.sessionRepository.findPromotedSessionBySessionId(session_id)

    await this.playerRepository.saveStatement(
      "Venda de sessão",
      `${player.nickname}`,
      incomeValue,
      "income",
      session.player_id,
      session.id,
      player_id
    );
    if(promoted_session && promoted_session.payment_type=="after_purchase"){
      await this.playerRepository.updateWallet(
        session.player_id,
        "income",
        incomeValue - promoted_session.price
      );
      return true
    }

    await this.playerRepository.updateWallet(
      session.player_id,
      "income",
      incomeValue
    );

    return true;
  }
}
