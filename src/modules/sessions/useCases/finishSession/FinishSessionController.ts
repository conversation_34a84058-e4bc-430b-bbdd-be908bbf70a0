import { Request, Response } from "express";
import { container } from "tsyringe";

import { FinishSessionUseCase } from "./FinishSessionUseCase";

export class FinishSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { session_id } = request.body;

    const finishSessionUseCase = container.resolve(FinishSessionUseCase);

    await finishSessionUseCase.execute({ session_id, player_id: Number(id) });

    return response.send();
  }
}
