import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  session_id: number;
  player_id: number;
}

@injectable()
export class ConfirmAfterSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ session_id, player_id }: IRequest): Promise<unknown> {
    const session = await this.sessionRepository.findById(session_id);

    if (!session) {
      throw new AppError("Session not found");
    }
    if (!this.dateProvider.compareIfBefore(session.end_time, new Date())) {
      throw new AppError("Session is running");
    }
    const playerSeat = session.players_seats.find(
      (player) => player.player_id === player_id
    );

    if (!playerSeat) {
      throw new AppError("You are not on this session");
    }
    if (playerSeat.isFinished) {
      throw new AppError("You already finished this session");
    }
    if (playerSeat.hasDispute) {
      throw new AppError("Can't finish session with dispute");
    }
    if (playerSeat.hasChargeback){
      throw new AppError("Can't finish session with chargeback")
    }
    if (playerSeat.isConfirmedAfterSession) {
      throw new AppError("Can't finish session with dispute");
    }
    await this.sessionRepository.updateSeat({
      seat_id: playerSeat.id,
      isConfirmedAfterSession: true,
    });

    return true;
  }
}
