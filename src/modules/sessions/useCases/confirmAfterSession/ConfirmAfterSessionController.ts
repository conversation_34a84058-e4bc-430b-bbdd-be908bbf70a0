import { Request, Response } from "express";
import { container } from "tsyringe";

import { ConfirmAfterSessionUseCase } from "./ConfirmAfterSessionUseCase";

export class ConfirmAfterSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { session_id } = request.body;

    const confirmAfterSessionUseCase = container.resolve(
      ConfirmAfterSessionUseCase
    );

    await confirmAfterSessionUseCase.execute({
      session_id,
      player_id: Number(id),
    });

    return response.send();
  }
}
