import { Request, Response } from "express";
import { container } from "tsyringe";

import { SaveMetricsUseCase } from "./SaveMetricsUseCase";

export class SaveMetricsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { session_id } = request.params;
    const { clicks, reach, views, checkouts, date } = request.body;

    const saveMetricsUseCase = container.resolve(SaveMetricsUseCase);

    const metrics = await saveMetricsUseCase.execute({
      date,
      session_id: Number(session_id),
      player_id: Number(id),
      clicks,
      reach,
      views,
      checkouts,
    });

    return response.json(metrics);
  }
}
