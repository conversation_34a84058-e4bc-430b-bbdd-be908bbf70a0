import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { Session_metrics } from "@prisma/client";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  session_id: number;
  player_id: number;
  clicks?: number;
  reach?: number;
  views?: number;
  checkouts?: number;
  date: Date;
}

@injectable()
export class SaveMetricsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({
    session_id,
    player_id,
    checkouts,
    clicks,
    reach,
    views,
    date,
  }: IRequest): Promise<Session_metrics> {
    const session = await this.sessionRepository.findById(session_id);

    if (!session) {
      throw new AppError("Session not found");
    }

    const metrics = await this.sessionRepository.listMetricsBySession(
      session_id
    );

    const player_metrics = metrics.filter(
      (metric) =>
        metric.player_id === player_id &&
        metric.session_id === session_id &&
        this.dateProvider.compareIfSameDay(metric.date, date)
    )[0];

    if (player_metrics) {
      const updated_metrics = await this.sessionRepository.updateMetrics({
        session_metrics_id: player_metrics.id,
        clicks,
        views,
        checkouts,
      });
      return updated_metrics;
    }

    const saved_metrics = await this.sessionRepository.createMetrics({
      session_id,
      player_id,
      clicks,
      reach,
      views,
      checkouts,
      date,
    });

    return saved_metrics;
  }
}
