import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { SessionMap } from "@modules/sessions/mapper/sessionMap";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  session_id: number;
  user_id: number;
  buyer_id: number;
}

@injectable()
export class GetSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ session_id, buyer_id, user_id }: IRequest): Promise<any> {
    const session = await this.sessionRepository.findById(session_id);

    if (!session) {
      throw new AppError("Session not found");
    }

    if (session.player_id !== user_id) {
      return SessionMap.toDTO(session);
    }

    if (buyer_id) {
      const player = await this.playerRepository.findById(buyer_id);

      if (!player) {
        throw new AppError("Player not found");
      }

      const playerHasSeat = session.players_seats.find(
        (seat) => seat.player_id === buyer_id
      );

      const infos = {
        id: player.id,
        nickname: player.nickname,
        player_avatar: player.avatar
          ? encodeURI(`${process.env.AWS_BUCKET_URL}/avatar/${player.avatar}`)
          : null,
      };

      console.log(playerHasSeat);

      return {
        ...SessionMap.toDTO(session),
        player: playerHasSeat && infos,
      };
    }

    return SessionMap.toDTO(session);
  }
}
