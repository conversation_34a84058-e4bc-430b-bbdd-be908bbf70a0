import { Request, Response } from "express";
import { container } from "tsyringe";

import { GetSessionUseCase } from "./GetSessionUseCase";

export class GetSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { session_id } = request.params;
    const id = request.user?.id;
    const { buyer_id } = request.query;

    const getSessionUseCase = container.resolve(GetSessionUseCase);

    const session = await getSessionUseCase.execute({
      session_id: Number(session_id),
      user_id: Number(id),
      buyer_id: Number(buyer_id),
    });

    return response.json(session);
  }
}
