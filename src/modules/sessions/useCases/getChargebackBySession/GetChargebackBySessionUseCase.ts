import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
    session_id: number;
}

@injectable()
export class GetChargebackBySessionUseCase {
    constructor(
        @inject("SessionRepository") private sessionRepository: ISessionRepository,
        @inject("DateProvider") private dateProvider: IDateProvider,
        @inject("PlayerRepository") private playerRepository: IPlayerRepository,
        @inject("UserRepository") private userRepository: IUserRepository
      ) {}
    
      async execute({ session_id }: IRequest): Promise<any[]> {
        const chargebacks = await this.sessionRepository.listChargeback();
        if (!chargebacks || chargebacks.length === 0) {
            throw new AppError("No chargebacks found");
        }
    
        const chargebackDetails = await Promise.all(chargebacks.map(async (chargeback) => {
          const chargeback_dispute = await this.sessionRepository.findChargebackInfoById(chargeback.id);
          const paymentInfo = await this.sessionRepository.findPaymentInfoByPaymentId(chargeback.external_payment_id);
          if (!paymentInfo) {
            return null; 
          }
          
          const seat = await this.sessionRepository.findSeatById(paymentInfo.seat_id);
          if (!seat) {
            return null; 
          }
          
          const session = await this.sessionRepository.findById(seat.session_id);
          if (!session) {
            return null;
          }
          
          const player = await this.playerRepository.findById(paymentInfo.player_id);
          if (!player) {
            return null;
          }
          
          const mentor = await this.playerRepository.findById(session.player_id);
          if (!mentor) {
            return null; 
          }
    
          return {
              id: chargeback.id,
              chargeback_date: chargeback.date_created,
              player_id: player.id,
              player: player.name,
              session: session.id,
              session_date: session.createdAt,
              mentor_id: mentor.id,
              mentor: mentor.name,
              ...(chargeback_dispute && chargeback_dispute.mentor_requested_response && { mentor_requested_response: chargeback_dispute.mentor_requested_response }),
              session_price: session.price
          };
        }));
    
        const validChargebackDetails = chargebackDetails.filter(detail => detail !== null);
    
        const filteredChargebacks = validChargebackDetails.filter(chargeback => chargeback.session === session_id);
    
        return filteredChargebacks;
      }
}
