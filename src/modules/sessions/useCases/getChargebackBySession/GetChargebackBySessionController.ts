import { Request, Response } from "express";
import { container } from "tsyringe";
import { GetChargebackBySessionUseCase } from "./GetChargebackBySessionUseCase";


export class GetChargebackBySessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { session_id } = request.params;

    const getChargebackBySessionUseCase = container.resolve(GetChargebackBySessionUseCase);

    
    const chargeback = await getChargebackBySessionUseCase.execute({
      session_id: Number(session_id),
    });

    return response.json(chargeback);
  };
};
