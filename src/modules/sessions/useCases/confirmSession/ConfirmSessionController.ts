import { Request, Response } from "express";
import { container } from "tsyringe";

import { ConfirmSessionUseCase } from "./ConfirmSessionUseCase";

export class ConfirmSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { player_id, session_id, confirmed } = request.body;
    const confirmSessionUseCase = container.resolve(ConfirmSessionUseCase);
    
    const message = await confirmSessionUseCase.execute({
      player_id,
      session_id,
      confirmed,
      owner_id: Number(id),
    });

    return response.json({ message });
  }
}
