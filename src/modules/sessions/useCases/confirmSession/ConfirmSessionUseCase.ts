import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { INotificationsProvider } from "@shared/container/providers/NotificationsProvider/INotificationsProvider";
import { AppError } from "@shared/errors/AppError";
import { PrismaClient } from "@prisma/client";

interface IRequest {
  session_id: number;
  player_id: number;
  confirmed: boolean;
  owner_id: number;
}

@injectable()
export class ConfirmSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("PrismaClient") private prisma: PrismaClient,
    @inject("NotificationsProvider")
    private notificationsProvider: INotificationsProvider
  ) {}
  async execute({
    player_id,
    confirmed,
    session_id,
    owner_id,
  }: IRequest): Promise<string> {
    const session = await this.sessionRepository.findById(session_id);
    const seat = await this.sessionRepository.findSeat(player_id, session_id);
    const player = await this.playerRepository.findById(player_id);
    const sessionOwner = await this.playerRepository.findById(owner_id);

    if (!seat) {
      throw new AppError("Player is not in session");
    }

    if (!session) {
      throw new AppError("Session not found");
    }

    if (session.player_id !== owner_id) {
      throw new AppError("You are not the owner of this session");
    }
    if (confirmed) {
      await this.sessionRepository.updateSeat({
        seat_id: seat.id,
        isConfirmed: confirmed,
      });


        if (player.push_token) {
          await this.notificationsProvider.sendNotification(
            player.push_token,
            "Confirmação de reserva",
            `Um sessão reservada com @${sessionOwner.nickname} foi confirmada. Fique atento para ao horário de início da sessão.`,
            JSON.stringify({
              avatar: sessionOwner.avatar
                ? `${process.env.AWS_BUCKET_URL}/avatar/${sessionOwner.avatar}`
                : "",
            })
          );
        } else {
          console.log("Token de push nulo ou inválido");
        };

      await this.playerRepository.saveNotification({
        player_id: player.id,
        body: `Um sessão reservada com @${sessionOwner.nickname} foi confirmada. Fique atento para ao horário de início da sessão.`,
        title: "Confirmação de reserva",
        image: sessionOwner.avatar
          ? `${process.env.AWS_BUCKET_URL}/avatar/${sessionOwner.avatar}`
          : "",
      });

      return "Session confirmed";
    }

    if (!confirmed) {
      const statementBySession =
        await this.playerRepository.findLastStatementByPlayer(player_id);

      const ownerStatementBySession =
        await this.playerRepository.findPendingStatement(
          owner_id,
          player_id,
          session.id
        );

      try {
        await this.sessionRepository.notConfirmSession(
          seat,
          sessionOwner,
          statementBySession,
          Number(ownerStatementBySession.id)
        );
        // await this.prisma.$transaction(async (tx) => {
        //   await tx.playerRepository.saveStatement(
        //     "Compra de sessão",
        //     `${sessionOwner.nickname}`,
        //     statementBySession.quantity,
        //     "canceled",
        //     player_id,
        //     statementBySession.session_id
        //   );
        //   await tx.playerRepository.updateWallet(
        //     player_id,
        //     "income",
        //     statementBySession.quantity
        //   );
        //   await tx.playerRepository.deleteStatementById(
        //     ownerStatementBySession.id
        //   );
        //   await tx.sessionRepository.deleteSeat(seat.id);
        // });
      } catch (error) {
        console.log(error);
      }

      // const isPromotedSession =
      //   await this.sessionRepository.findPromotedSessionBySessionId(session_id);

      // if (isPromotedSession) {
      //   const until = this.dateProvider.addDays(
      //     isPromotedSession.days_to_expire
      //   );

      //   await this.sessionRepository.updatePromotedSession({
      //     promoted_session_id: isPromotedSession.id,
      //     active: true,
      //     until,
      //   });
      // }

      await this.notificationsProvider.sendNotification(
        player.push_token,
        "Confirmação de reserva",
        `Um sessão reservada com @${sessionOwner.nickname} foi negada. Acesse o app para agendar uma nova sessão`,
        JSON.stringify({
          avatar: sessionOwner.avatar
            ? `${process.env.AWS_BUCKET_URL}/avatar/${sessionOwner.avatar}`
            : "",
        })
      );

      // await this.playerRepository.saveNotification({
      //   player_id: player.id,
      //   body: `Um sessão reservada com @${sessionOwner.nickname} foi negada. Acesse o app para agendar uma nova sessão`,
      //   title: "Confirmação de reserva",
      //   image: sessionOwner.avatar
      //     ? `${process.env.AWS_BUCKET_URL}/avatar/${sessionOwner.avatar}`
      //     : "",
      // });

      return "Session canceled";
    }

    return "";
  }
}
