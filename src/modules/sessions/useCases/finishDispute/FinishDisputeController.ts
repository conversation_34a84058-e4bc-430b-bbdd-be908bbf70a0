import { Request, Response } from "express";
import { container } from "tsyringe";

import { FinishDisputeUseCase } from "./FinishDisputeUseCase";

export class FinishDisputeController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { winner_id, description } = request.body;
    const { dispute_id } = request.params;
    const { id } = request.user;

    const finishDisputeUseCase = container.resolve(FinishDisputeUseCase);

    const dispute = await finishDisputeUseCase.execute({
      user_id: Number(id),
      dispute_id: Number(dispute_id),
      winner_id: Number(winner_id),
      description,
    });

    return response.json(dispute);
  }
}
