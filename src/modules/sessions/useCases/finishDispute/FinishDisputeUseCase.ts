import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  user_id: number;
  dispute_id: number;
  winner_id: number;
  description: string;
}

@injectable()
export class FinishDisputeUseCase {
  constructor(
    @inject("UserRepository") private userRepository: IUserRepository,
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({
    dispute_id,
    user_id,
    winner_id,
    description,
  }: IRequest): Promise<any> {
    const dispute = await this.sessionRepository.findDisputeById(dispute_id);
    const session = await this.sessionRepository.findById(dispute.session_id);
    const sessionOwner = await this.playerRepository.findById(
      session.player_id
    );
    const player = await this.playerRepository.findById(dispute.player_id);

    if (!dispute) {
      throw new AppError("Dispute not found");
    }

    if (dispute.user_id !== user_id) {
      throw new AppError("You are not assigned to this dispute");
    }

    if (dispute.player_id !== winner_id && session.player_id !== winner_id) {
      throw new AppError("winner_id is not valid");
    }

    const ownerStatement = await this.playerRepository.findStatementByPlayer(
      sessionOwner.id,
      "createdAt"
    );

    const [ownerStatementBySession] = ownerStatement
      .filter(
        (statement) =>
          statement.buyer_id === dispute.player_id &&
          statement.session_id === session.id &&
          statement.type === "pending"
      )
      .slice(-1);

    await this.playerRepository.deleteStatementById(ownerStatementBySession.id);

    let decision = "";

    if (dispute.player_id === winner_id) {
      decision = "Player";

      await this.playerRepository.saveStatement(
        "Ganhador da disputa",
        `${session.Player.nickname}`,
        session.price,
        "canceled",
        dispute.player_id,
        dispute.session_id
      );

      await this.playerRepository.updateWallet(
        dispute.player_id,
        "income",
        session.price
      );
    }

    if (session.player_id === winner_id) {
      decision = "Pro Player";

      await this.playerRepository.saveStatement(
        "Ganhador da disputa",
        `${player.nickname}`,
        session.price,
        "income",
        session.player_id,
        session.id,
        dispute.player_id
      );

      await this.playerRepository.updateWallet(
        session.player_id,
        "income",
        session.price
      );
    }

    const finishedDispute = await this.sessionRepository.updateDispute({
      dispute_id,
      decision,
      winner_id,
      moderator_description: description,
      status: "finished",
      resolution_date: new Date(),
    });

    const moderator =
      finishedDispute.user_id &&
      (await this.userRepository.findById(finishedDispute.user_id));

    return {
      id: finishedDispute.id,
      status: finishedDispute.status,
      decision: finishedDispute.decision,
      player_id: finishedDispute.player_id,
      player: player.name || player.nickname,
      request_date: finishedDispute.createdAt,
      price: session.price,
      player_description: finishedDispute.player_description,
      player_attachments: finishedDispute.player_attachment,
      player_attachments_url: finishedDispute.player_attachment
        ? encodeURI(
            `${process.env.AWS_BUCKET_URL}/disputes/${finishedDispute.player_attachment}`
          )
        : null,
      mentor_id: sessionOwner.id,
      mentor: sessionOwner.name || sessionOwner.nickname,
      answer_date: finishedDispute.mentor_response_date,
      mentor_description: finishedDispute.mentor_description,
      mentor_attachments: finishedDispute.mentor_attachment,
      mentor_attachments_url: finishedDispute.mentor_attachment
        ? encodeURI(
            `${process.env.AWS_BUCKET_URL}/disputes/${finishedDispute.mentor_attachment}`
          )
        : null,
      moderator_id: moderator?.id || null,
      moderator_name: moderator?.name || null,
      moderator_description: finishedDispute.moderator_description,
      resolution_date: finishedDispute.resolution_date,
    };
  }
}
