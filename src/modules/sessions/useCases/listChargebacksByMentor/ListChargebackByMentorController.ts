import { Request, Response } from "express";
import { container } from "tsyringe";
import { ListChargebackByMentorUseCase } from "./ListChargebackByMentorUseCase";


export class ListChargebackByMentorController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { player_id} = request.params;

    const listChargebackByMentorUseCase = container.resolve(ListChargebackByMentorUseCase);

    
    const chargeback = await listChargebackByMentorUseCase.execute({
      id: Number(player_id),
    });

    return response.json(chargeback);
  };
};
