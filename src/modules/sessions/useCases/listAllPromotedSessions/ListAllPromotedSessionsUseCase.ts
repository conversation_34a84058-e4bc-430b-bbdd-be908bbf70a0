import "dayjs/locale/pt-br";
import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";

@injectable()
export class ListAllPromotedSessionsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(): Promise<any> {
    const promotedSessions = await this.sessionRepository.listPromotedSessions();

    const sessionsWithPlayerDetails = await Promise.all(
      promotedSessions.map(async (session) => {
        const player = await this.playerRepository.findById(session.Session.player_id);

        return {
          ...session,
          Session: {
            ...session.Session,
            Player: {
              id: player.id,
              name: player.name,
              avatar: player.avatar,
              nickname: player.nickname,
            },
          },
        };
      })
    );

    return sessionsWithPlayerDetails;
  }
}
