import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListAllPromotedSessionsUseCase } from "./ListAllPromotedSessionsUseCase";

export class ListAllPromotedSessionsController {
  async handle(request: Request, response: Response): Promise<Response> {
    
    const listAllPromotedSessionsUseCase = container.resolve(
        ListAllPromotedSessionsUseCase
    );
    const allPromotedSessions = await listAllPromotedSessionsUseCase.execute();

    return response.json(allPromotedSessions);
  }
}
