import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

interface IRequest {
  player_id: number;
}

@injectable()
export class ListPromotedSessionsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ player_id }: IRequest): Promise<any> {
    const promoted_session =
      await this.sessionRepository.listPromotedSessionsByPlayer(player_id);

    const promotedSessionsMapped = Promise.all(
      promoted_session.map(async (promoted) => {
        const session_metrics =
          await this.sessionRepository.listMetricsBySession(
            promoted.session_id
          );

        const past_days = this.dateProvider.compareInDays(
          promoted.createdAt,
          new Date()
        );

        const reach = session_metrics.reduce((acc, metric) => {
          return acc + metric.reach;
        }, 0);

        let status: string;

        if (promoted.active) {
          status = "active";
        } else if (
          !this.dateProvider.compareIfBefore(promoted.until, new Date()) &&
          !promoted.active
        ) {
          // TODO: verify is session has already started
          status = "paused";
        } else {
          status = "finished";
        }

        return {
          id: promoted.id,
          status,
          until: promoted.until,
          days_to_expire: promoted.days_to_expire,
          past_days,
          price: promoted.price,
          reach,
          expected_reach_min: promoted.expected_reach_min,
          expected_reach_max: promoted.expected_reach_max,
          createdAt: promoted.createdAt,
          session_id: promoted.session_id,
          session: {
            id: promoted.Session.id,
            start_time: promoted.Session.start_time,
            end_time: promoted.Session.end_time,
            price: promoted.Session.price,
            seats: promoted.Session.seats,
            isActive: promoted.Session.isActive,
            createdAt: promoted.Session.createdAt,
            player_id: promoted.Session.player_id,
            game_id: promoted.Session.game_id,
            session_base_id: promoted.Session.session_base_id,
            Game: {
              id: promoted.Session.Game.id,
              name: promoted.Session.Game.name,
              isActive: promoted.Session.Game.isActive,
              banner: encodeURI(
                `${process.env.AWS_BUCKET_URL}/games/banner/${promoted.Session.Game.banner}`
              ),
              logo: encodeURI(
                `${process.env.AWS_BUCKET_URL}/games/logo/${promoted.Session.Game.logo}`
              ),
              createdAt: promoted.Session.Game.createdAt,
              updatedAt: promoted.Session.Game.updatedAt,
            },
          },
        };
      })
    );

    return promotedSessionsMapped;
  }
}
