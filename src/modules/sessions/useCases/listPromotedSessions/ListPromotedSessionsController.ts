import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListPromotedSessionsUseCase } from "./ListPromotedSessionsUseCase";

export class ListPromotedSessionsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;

    const listPromotedSessionsUseCase = container.resolve(
      ListPromotedSessionsUseCase
    );

    const promoted_session = await listPromotedSessionsUseCase.execute({
      player_id: Number(id),
    });

    return response.json(promoted_session);
  }
}
