import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  user_id: number;
  chargeback_id: number;
}

@injectable()
export class AssignChargebackUseCase {
  constructor(
    @inject("UserRepository") private userRepository: IUserRepository,
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ chargeback_id, user_id }: IRequest): Promise<any> {
    const chargeback = await this.sessionRepository.findChargebackInfoById(chargeback_id);

    if (!chargeback) {
      throw new AppError("Dispute not found");
    }

    if (chargeback.user_id) {
      throw new AppError("Dispute already assigned");
    }
    

    const assignedchargeback = await this.sessionRepository.assignChargeback({
        chargeback_id: chargeback.id,
        user_id,
    })

    return assignedchargeback
}
}
