import { Request, Response } from "express";
import { container } from "tsyringe";
import { AssignChargebackUseCase } from "./AssignChargebackUseCase";



export class AssignChargebackController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { chargeback_id } = request.params;

    const assignChargebackUseCase = container.resolve(AssignChargebackUseCase);

    const chargeback = await assignChargebackUseCase.execute({
      chargeback_id: Number(chargeback_id),
      user_id: Number(id),
    });

    return response.json(chargeback);
  }
}
