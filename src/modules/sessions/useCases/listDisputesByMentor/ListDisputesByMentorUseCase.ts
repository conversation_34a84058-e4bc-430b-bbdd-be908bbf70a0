import { inject, injectable } from "tsyringe";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";

interface IRequest {
  id: string;
}

@injectable()
export class ListDisputesByMentorUseCase {
  constructor(
    @inject("SessionRepository")
    private sessionRepository: ISessionRepository
  ) {}

  async execute({ id }: IRequest): Promise<any> {    
    const mentorIdNumber = parseInt(id, 10);
    const disputes = await this.sessionRepository.listDisputesByMentorId();
    
    const mappedDisputes = disputes.map((dispute) => ({
      id: dispute.id,
      mentor: dispute.Session.player_id,
      session: dispute.session_id,
      title: `Disputa ${dispute.id}`,
      name_player: dispute.Player.name || dispute.Player.nickname,
      status: dispute.status,
      player_description: dispute.player_description,
      decision: dispute.decision,
      resolution_date: dispute.resolution_date,
      player_attachment: dispute.player_attachment,
      mentor_description: dispute.mentor_description,
      mentor_attachment: dispute.mentor_attachment,
      mentor_response_date: dispute.mentor_response_date,
      moderator_description: dispute.moderator_description,
      winner_id: dispute.winner_id,
      createdAt: dispute.createdAt,
      player_id: dispute.player_id,
    }));
    const filteredDisputes = mappedDisputes.filter((mappedDisputes) => mappedDisputes.mentor === mentorIdNumber);
    return filteredDisputes;
  }
}
