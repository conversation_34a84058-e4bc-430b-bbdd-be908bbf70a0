import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListDisputesByMentorUseCase } from "./ListDisputesByMentorUseCase";

export class ListDisputesByMentorController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.params; 
    const listDisputesByMentorUseCase = container.resolve(ListDisputesByMentorUseCase);

    const disputes = await listDisputesByMentorUseCase.execute({ id });

    return response.json(disputes);
  }
}
