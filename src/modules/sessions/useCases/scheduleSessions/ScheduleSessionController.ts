import { Request, Response } from "express";
import { container } from "tsyringe";

import { ScheduleSessionUseCase } from "./ScheduleSessionUseCase";

export class ScheduleSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { session_id, payment_from_wallet } = request.body;
    const { id } = request.user;
    const scheduleSessionUseCase = container.resolve(ScheduleSessionUseCase);

    const result = await scheduleSessionUseCase.execute({
      player_id: Number(id),
      session_id: Number(session_id),
      payment_from_wallet: Number(payment_from_wallet),
    });

    return response.json(result);
  }
}
