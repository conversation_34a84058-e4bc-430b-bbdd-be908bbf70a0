import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IScheduleSessionDTO } from "@modules/sessions/dtos/IScheduleSessionDTO";
import { SessionMap } from "@modules/sessions/mapper/sessionMap";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { ITributeRepository } from "@modules/tributes/repositories/ITributeRepository";
import { IWebhookRepository } from "@modules/webhook/repositories/IWebhookRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { INotificationsProvider } from "@shared/container/providers/NotificationsProvider/INotificationsProvider";
import { IMercadoPagoProvider } from "@shared/container/providers/PaymentGatewayProvider/IMercadoPagoProvider";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class ScheduleSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("WebhookRepository") private webhookRepository: IWebhookRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("MercadoPagoProvider")
    private mercadoPagoProvider: IMercadoPagoProvider,
    @inject("NotificationsProvider")
    private notificationsProvider: INotificationsProvider,
    @inject("TributeRepository") private tributeRepository: ITributeRepository
  ) {}
  // TODO: Cancelar o seat caso o sistema dê um erro
  async execute({
    player_id,
    session_id,
    payment_from_wallet,
  }: IScheduleSessionDTO): Promise<any> {
    try {
      const session = await this.sessionRepository.findById(session_id);
      const sessionOwner = await this.playerRepository.findById(
        session.player_id
      );

      if (!session) {
        throw new AppError("Session not found");
      }
      
      const PlayerMentorNow = await this.sessionRepository.listSessionsNowByMentor(player_id)      
      if(PlayerMentorNow && PlayerMentorNow.length > 0){
        throw new AppError("Player is Owner in other Session Now");
      }

      if (payment_from_wallet === undefined || payment_from_wallet === null) {
        throw new AppError("payment_from_wallet param is missing");
      }

      const hasSeat = session.players_seats.filter(
        (seat) => seat.player_id === player_id
      );

      if (hasSeat.length > 0) {
        if (
          hasSeat[0]?.Payment_info[0]?.status === "created" &&
          hasSeat[0]?.Payment_info[0]?.payment_url != null
        ) {
          throw new AppError(hasSeat[0].Payment_info[0].payment_url, 456); // 456 = player has this session
        }
        throw new AppError("Player has this session", 452); // 452 = player has this session
      }

      const startDateDiffInMinutes = this.dateProvider.compareInMinutes(
        new Date(),
        session.start_time
      );

      const minutesBeforeStart = 10;

      if (startDateDiffInMinutes <= minutesBeforeStart) {
        throw new AppError("Session is about to start", 453); // 453 = session is about to start
      }

      const totalPlayers = session.players_seats.length;

      if (totalPlayers >= session.seats) {
        throw new AppError("Session is full", 454); // 454 = session is full
      }

      if (player_id === sessionOwner.id) {
        throw new AppError("Player is the session owner");
      }

      const player = await this.playerRepository.findById(player_id);

      if (!player) {
        throw new AppError("Session buyer not found");
      }

      if (
        payment_from_wallet > 0 &&
        player.wallet.total < payment_from_wallet
      ) {
        throw new AppError("Insufficient funds", 455); // 455 = insufficient funds
      }

      const date_of_expiration = new Date();
      date_of_expiration.setMinutes(date_of_expiration.getMinutes() + 20); // add 20min a hora atual

      const preferenceData = {
        items: [
          {
            title: `Sessão de ${sessionOwner.nickname}`,
            description: `Compra de sessão de ${sessionOwner.nickname}`,
            picture_url: sessionOwner.avatar || null,
            category_id: "Services",
            quantity: 1,
            currency_id: "BRL",
            unit_price: session.price,
          },
        ],
        payer: {
          identification: {
            type: player.cpf ? "CPF" : null,
            number: player.cpf || null,
          },
        },
        payment_methods: {
          excluded_payment_types: [
            {
              id: "ticket,atm,debit_card",
            },
          ],
        },
        back_urls: {
          success: "knowbie://success",
          failure: "knowbie://failure",
          pending: "knowbie://pending",
        },
        auto_return: "approved",
        external_reference: "",
        date_of_expiration,
        // notification_url:
        //   "https://d8d1-45-7-1-242.ngrok-free.app/webhook/mercadopago",
        notification_url: `${process.env.APP_URL_BASE}/webhook/mercadopago`,
      };

      let mercadoPagoResponse;
      let aditionalCharge;
      let payment_info;

      const updatedSession = await this.sessionRepository.findById(session_id);

      try {
        let liquid_ammount = session.price;
        if (liquid_ammount > 0) {
          const fees = await this.tributeRepository.listAllDistinctTribute();
          let payment_gateway_fee = "0";
          let comission_fee = "0";
          let license_fee = 0;
          fees.forEach((fee) => {
            if (fee.description === "payment player fee") {
              payment_gateway_fee = Number(
                liquid_ammount * (fee.value / 100)
              ).toFixed(2);
            } else if (fee.description === "comission fee") {
              comission_fee = Number(
                liquid_ammount * (fee.value / 100)
              ).toFixed(2);
            } else if (fee.description === "license fee") {
              license_fee = fee.value;
            }
          });
          liquid_ammount -=
            Number(payment_gateway_fee) + Number(comission_fee) + license_fee;
        }

        if (
          payment_from_wallet === session.price &&
          player.wallet.total >= payment_from_wallet &&
          session.price > 0
        ) {
          console.log("1");
          // Se o pagamento for feito inteiramente com saldo da carteira
          payment_info = await this.webhookRepository.create({
            player_id,
            payment_from_wallet: session.price,
            status: "approved",
          });

          var wallet = await this.playerRepository.updateWallet(
            player_id,
            "outcome",
            session.price
          );

          await this.playerRepository.saveStatement(
            "Compra de sessão",
            `${sessionOwner.nickname}`,
            session.price,
            "outcome",
            player_id,
            session_id,
            undefined,
            payment_info.id
          );

          await this.playerRepository.saveStatement(
            "Venda de sessão",
            `${player.nickname}`,
            Number(liquid_ammount),
            "pending",
            sessionOwner.id,
            session_id,
            player.id
          );
        } else if (
          payment_from_wallet > 0 &&
          player.wallet.total >= payment_from_wallet &&
          session.price > 0
        ) {
          console.log("2");
          // Se o pagamento for feito com saldo da carteira e o saldo for insuficiente, o restante é cobrado no cartão
          aditionalCharge = session.price - payment_from_wallet;

          payment_info = await this.webhookRepository.create({
            player_id,
            payment_from_wallet,
            payment_from_external: aditionalCharge,
          });
          preferenceData.external_reference = String(payment_info.id);

          preferenceData.items[0].unit_price = aditionalCharge;
          mercadoPagoResponse = await this.mercadoPagoProvider.createPreference(
            preferenceData
          );

          await this.webhookRepository.update(payment_info.id, {
            payment_url: mercadoPagoResponse?.data?.init_point,
          });

          var wallet = await this.playerRepository.updateWallet(
            player_id,
            "outcome",
            payment_from_wallet
          );

          await this.playerRepository.saveStatement(
            "Compra de sessão",
            `${sessionOwner.nickname}`,
            session.price,
            "outcome",
            player_id,
            session_id,
            undefined,
            payment_info.id
          );

          await this.playerRepository.saveStatement(
            "Venda de sessão",
            `${player.nickname}`,
            Number(liquid_ammount),
            "pending",
            sessionOwner.id,
            session_id,
            player.id
          );
        } else if (session.price === 0) {
          console.log("3");
          // Defina o status do pagamento como "approved"
          payment_info = await this.webhookRepository.create({
            player_id,
            payment_from_wallet: 0,
            payment_from_external: 0,
            status: "approved",
          });
          await this.playerRepository.saveStatement(
            "Compra de sessão",
            `${sessionOwner.nickname}`,
            session.price,
            "outcome",
            player_id,
            session_id,
            undefined,
            payment_info.id
          );

          await this.playerRepository.saveStatement(
            "Venda de sessão",
            `${player.nickname}`,
            Number(liquid_ammount),
            "pending",
            sessionOwner.id,
            session_id,
            player.id
          );
        } else {
          console.log("4");
          // Se o pagamento for feito inteiramente com o meio de pagamento externo
          payment_info = await this.webhookRepository.create({
            player_id,
            payment_from_external: session.price,
          });
          preferenceData.external_reference = String(payment_info.id);

          mercadoPagoResponse = await this.mercadoPagoProvider.createPreference(
            preferenceData
          );

          await this.webhookRepository.update(payment_info.id, {
            payment_url: mercadoPagoResponse?.data?.init_point,
          });

          await this.playerRepository.saveStatement(
            "Compra de sessão",
            `${sessionOwner.nickname}`,
            session.price,
            "outcome",
            player_id,
            session_id,
            undefined,
            payment_info.id
          );

          await this.playerRepository.saveStatement(
            "Venda de sessão",
            `${player.nickname}`,
            Number(liquid_ammount),
            "pending",
            sessionOwner.id,
            session_id,
            player.id
          );
        }
      } catch (err) {
        console.error(err);
        throw new AppError(
          `A payment error has occurred: ${err.message}`,
          err.statusCode
        );
      }

      const scheduledSession = await this.sessionRepository.scheduleSession({
        session_id,
        player_id,
        payment_from_wallet,
      });

      const startDateDiffInHours = this.dateProvider.compareInHours(
        new Date(),
        session.start_time
      );

      let expireDate = this.dateProvider.addHours(24);

      const hours = 24;
      if (startDateDiffInHours <= hours) {
        expireDate = session.start_time;
      }

      await this.sessionRepository.scheduleJob(expireDate, scheduledSession.id);

      const seat_id = scheduledSession.id;

      await this.webhookRepository.update(payment_info.id, { seat_id });

      /*     await this.notificationsProvider.sendNotification(
      player.push_token,
      "Sessão reservada",
      `Você reservou uma sessão com @${sessionOwner.nickname}. Você será notificado assim que o Pró player confirmar a sessão.`,
      JSON.stringify({
        avatar: sessionOwner.avatar
          ? `${process.env.AWS_BUCKET_URL}/avatar/${sessionOwner.avatar}`
          : "",
      })
    );

    await this.playerRepository.saveNotification({
      player_id: player.id,
      body: `Você reservou uma sessão com @${sessionOwner.nickname}. Você será notificado assim que o Pró player confirmar a sessão.`,
      title: "Sessão reservada",
      image: sessionOwner.avatar
        ? `${process.env.AWS_BUCKET_URL}/avatar/${sessionOwner.avatar}`
        : "",
    }); */

      const newTotalPlayers = updatedSession.players_seats.length;

      if (newTotalPlayers === updatedSession.seats) {
        const isPromotedSession =
          await this.sessionRepository.findPromotedSessionBySessionId(
            session_id
          );

        if (isPromotedSession) {
          const days_to_expire = this.dateProvider.compareInDays(
            new Date(),
            isPromotedSession.until
          );
          await this.sessionRepository.updatePromotedSession({
            promoted_session_id: isPromotedSession.id,
            active: false,
            days_to_expire,
          });
        }
      }

      return {
        session: {
          ...SessionMap.toDTO(session),
          confirmed: updatedSession.players_seats.filter(
            (seat) => seat.player_id === player_id
          )[0]?.isConfirmed,
        },
        Saldo:
          wallet?.total !== null && wallet?.total !== undefined
            ? wallet?.total
            : player.wallet.total, // return new total from wallet or old total if no changes were made
        mercado_pago_init_point: mercadoPagoResponse?.data?.init_point || null,
      };
    } catch (err) {
      throw new AppError(err.message, err.statusCode);
    }
  }
}
