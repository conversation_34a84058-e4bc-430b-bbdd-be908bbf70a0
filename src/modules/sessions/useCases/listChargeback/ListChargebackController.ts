import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListChargebackUseCase } from "./ListChargebackUseCase";

export class ListChargebackController {
  async handle(request: Request, response: Response): Promise<Response> {
    
    const listChargebackUseCase = container.resolve(
      ListChargebackUseCase
    );
    const allChargeback = await listChargebackUseCase.execute();

    return response.json(allChargeback);
  }
}
