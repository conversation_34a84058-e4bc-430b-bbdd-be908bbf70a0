import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";

@injectable()
export class ListChargebackUseCase {
  constructor(@inject("SessionRepository") private sessionRepository: ISessionRepository,) {}
  async execute(): Promise<any> {
      const allChargeback = await this.sessionRepository.listChargeback();
      return allChargeback;
  }
}
