import { Request, Response } from "express";
import { container } from "tsyringe";

import { GetLastSessionWithoutRatingUseCase } from "./GetLastSessionWithoutRatingUseCase";

export class GetLastSessionWithoutRatingController {
  async handle(request: Request, response: Response): Promise<Response> {
    const id = request.params.player_id;

    const getLastSessionWithoutRatingUseCase = container.resolve(
      GetLastSessionWithoutRatingUseCase
    );

    const session = await getLastSessionWithoutRatingUseCase.execute(
      Number(id)
    );

    return response.json(session);
  }
}
