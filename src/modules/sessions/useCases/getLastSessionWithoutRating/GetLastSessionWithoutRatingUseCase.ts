import { inject, injectable } from "tsyringe";

import { SessionMap } from "@modules/sessions/mapper/sessionMap";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";
import { Session } from "@prisma/client";
import { ILastSessionDTO } from "@modules/sessions/dtos/ISessionDTO";

interface IRequest {
  session_id: number;
  user_id: number;
  buyer_id: number;
}

@injectable()
export class GetLastSessionWithoutRatingUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository
  ) {}

  async execute(user_id: number): Promise<ILastSessionDTO[]> {
    const session = await this.sessionRepository.findLastSessionWithoutRating(
      user_id
    );

    if (session.length < 1) return session;

    return [
      {
        id: session[0].session_id,
        price: session[0].price,
        start_time: session[0].start_time,
        end_time: session[0].end_time,
        isFinished: session[0].isFinished,
        isConfirmedAfterSession: session[0].isConfirmedAfterSession,
        seats: session[0].seats,
        points: session[0].points,
        completedDate: session[0].end_time,
        // completedDate:
        //   session[0].type == "income"
        //     ? session[0].createdAt
        //     : session[0].end_time,
        Player: {
          avatar: session[0].avatar,
          id: session[0].player_id,
          nickname: session[0].nickname,
        },
        Game: {
          name: session[0].game_name,
          id: session[0].game_id,
          logo: session[0].logo,
        },
      },
    ];
  }
}
