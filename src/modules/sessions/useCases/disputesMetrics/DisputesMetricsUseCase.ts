import dayjs from "dayjs";
import "dayjs/locale/pt-br";

import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

@injectable()
export class DisputesMetricsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute(): Promise<any> {
    const disputes = await this.sessionRepository.listDisputes();

    const disputeByMonths = disputes.reduce(
      (acc, dispute) => {
        const { createdAt } = dispute;
        const month = createdAt.getMonth();

        if (!acc[month]) {
          acc[month] = { quantity: 0 };
        }
        acc[month].quantity += 1;

        return acc;
      },
      {
        0: { quantity: 0 },
        1: { quantity: 0 },
        2: { quantity: 0 },
        3: { quantity: 0 },
        4: { quantity: 0 },
        5: { quantity: 0 },
        6: { quantity: 0 },
        7: { quantity: 0 },
        8: { quantity: 0 },
        9: { quantity: 0 },
        10: { quantity: 0 },
        11: { quantity: 0 },
      }
    );

    const disputeByMonthsArray = Object.keys(disputeByMonths).map((key) => {
      return {
        label: dayjs().month(Number(key)).locale("pt-br").format("MMMM"),
        quantity: disputeByMonths[key].quantity,
      };
    });

    return disputeByMonthsArray;
  }
}
