import { Request, Response } from "express";
import { container } from "tsyringe";

import { DisputesMetricsUseCase } from "./DisputesMetricsUseCase";

export class DisputesMetricsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const disputesMetricsUseCase = container.resolve(DisputesMetricsUseCase);

    const dispute = await disputesMetricsUseCase.execute();

    return response.json(dispute);
  }
}
