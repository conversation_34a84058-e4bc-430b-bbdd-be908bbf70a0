import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { pagination } from "@modules/sessions/dtos/ISessionDTO";

@injectable()
export class GetHistoricChatUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository
  ) {}

  async execute(
    session_id: number,
    limit: number,
    page: number
  ): Promise<pagination> {
    let historic = {
      info: {
        count: 0,
        pages: 0,
        next: 0,
        prev: 0,
      },
      result: [],
    };
    historic.result = await this.sessionRepository.historicChat(
      session_id,
      limit,
      page
    );

    const countMessages = await this.sessionRepository.countMessagesChatById(
      session_id
    );

    historic.info = {
      count: countMessages,
      pages: page,
      next: page * limit < countMessages ? page + 1 : null,
      prev: page == 1 ? null : page - 1,
    };
    return historic;
  }
}
