import { Request, Response } from "express";
import { container } from "tsyringe";

import { GetHistoricChatUseCase } from "./GetHistoricChatUseCase";

export class GetHistoricChatController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { session_id } = request.params;
    const { limit, page } = request.query;

    const historicChatUseCase = container.resolve(GetHistoricChatUseCase);

    const historic = await historicChatUseCase.execute(
      Number(session_id),
      Number(limit),
      Number(page)
    );

    return response.json(historic);
  }
}
