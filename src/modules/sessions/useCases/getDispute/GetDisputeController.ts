import { Request, Response } from "express";
import { container } from "tsyringe";

import { GetDisputeUseCase } from "./GetDisputeUseCase";

export class GetDisputeController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { dispute_id } = request.params;

    const getDisputeUseCase = container.resolve(GetDisputeUseCase);

    const dispute = await getDisputeUseCase.execute({
      dispute_id: Number(dispute_id),
    });

    return response.json(dispute);
  };
};
