import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  dispute_id: number;
}

@injectable()
export class GetDisputeUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("UserRepository") private userRepository: IUserRepository
  ) {}

  async execute({ dispute_id }: IRequest): Promise<any> {
    const dispute = await this.sessionRepository.findDisputeById(dispute_id);

    if (!dispute) {
      throw new AppError("Dispute not found");
    }

    const player = await this.playerRepository.findById(dispute.player_id);

    const session = await this.sessionRepository.findById(dispute.session_id);

    const sessionOwner = await this.playerRepository.findById(
      session.player_id
    );

    const moderator =
      dispute.user_id && (await this.userRepository.findById(dispute.user_id));

    return {
      id: dispute.id,
      player_id: dispute.player_id,
      player: player.name || player.nickname,
      mentor_id: sessionOwner.id,
      mentor: sessionOwner.name || sessionOwner.nickname,
      request_date: dispute.createdAt,
      price: session.price,
      player_description: dispute.player_description,
      player_attachments: dispute.player_attachment,
      player_attachments_url: dispute.player_attachment
        ? encodeURI(
            `${process.env.AWS_BUCKET_URL}/disputes/${dispute.player_attachment}`
          )
        : null,
      mentor_description: dispute.mentor_description,
      mentor_attachments: dispute.mentor_attachment,
      mentor_attachments_url: dispute.mentor_attachment
        ? encodeURI(
            `${process.env.AWS_BUCKET_URL}/disputes/${dispute.mentor_attachment}`
          )
        : null,
      mentor_response_date: dispute.mentor_response_date,
      status: dispute.status,
      analyst_id: moderator?.id || null,
      analyst_name: moderator?.name || null,
      decision: dispute.decision,
      // answer_date: dispute.mentor_response_date,
      // moderator_description: dispute.moderator_description,
      // resolution_date: dispute.resolution_date,
    };
  }
}
