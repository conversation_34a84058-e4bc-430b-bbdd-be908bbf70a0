import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListSessionsUseCase } from "./ListSessionsUseCase";

export class ListSessionsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const {
      game_id,
      date,
      price_min,
      price_max,
      time,
      duration,
      seats,
      avaliation_min,
      avaliation_max,
      page,
      limit,
      order,
      available,
      player,
      player_id,
      start_date,
      end_date,
      tags
    } = request.query;

    const listSessionsUseCase = container.resolve(ListSessionsUseCase);

    const sessions = await listSessionsUseCase.execute({
      game_id: Number(game_id),
      seats:
        seats &&
        String(seats)
          .split(",")
          .map((seat) => Number(seat)),
      // date:
      //   date &&
      //   String(date)
      //     .split(",")
      //     .map((date) => String(date).trim()),
      date: date && String(date),
      time: time && String(time),
      duration: Number(duration),
      page: Number(page),
      limit: Number(limit),
      order: String(order),
      player: player && String(player),
      available: String(available) === "true",
      price: {
        max: Number(price_max),
        min: Number(price_min),
      },
      avaliation: {
        max: Number(avaliation_max),
        min: Number(avaliation_min),
      },
      player_id: Number(player_id),
      start_date: start_date && String(start_date),
      end_date: end_date && String(end_date),
      tags: tags ? String(tags).split(",").map(tag => tag.trim()) : undefined
    });

    return response.json(sessions);
  }
}
