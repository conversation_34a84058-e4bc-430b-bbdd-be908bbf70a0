import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IListSessionsDTO } from "@modules/sessions/dtos/IListSessionsDTO";
import {
  ISessionDTO,
  listSessions,
  pagination,
} from "@modules/sessions/dtos/ISessionDTO";
import { SessionMap } from "@modules/sessions/mapper/sessionMap";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { Session } from "@prisma/client";

@injectable()
export class ListSessionsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async addPlayerAvaliationToSession(session: listSessions) {
    const avaliations = await this.playerRepository.findAvaliations(session.player_id);
    
    let average = 5;
  
    if (avaliations.length !== 0) {
      average =
        avaliations.reduce((acc, curr) => acc + curr.points, 5) /
        (avaliations.length + 1);
    }
  
    const Tags = await this.playerRepository.findTags(session.player_id);
    
    // Necessária conversão, pois estava vindo bigint e bugando tipagem do JS
    const formattedTags = Tags.map((tag) => ({
      name: tag.name,
      quantity: Number(tag.quantity),
    }));
  
    session.avaliations = {
      average: Number(average.toFixed(1)),
      tags: formattedTags,
      comments: avaliations,
    };
  
    return session;
  }
  
  // TODO: set Available default = true
  // Why available = true is not working I don't know :(
  async execute({
    game_id,
    price,
    seats,
    date,
    time,
    duration,
    avaliation,
    page,
    limit,
    order,
    available,
    player,
    player_id,
    start_date,
    end_date,
    tags
  }: // }: IListSessionsDTO): Promise<ISessionDTO[]> {
  IListSessionsDTO): Promise<pagination> {
    const sessions = {
      info: {
        count: 0,
        pages: 0,
        next: 0,
        prev: 0,
      },
      result: [],
    };
    sessions.result = await this.sessionRepository.listActiveSessions(
      limit,
      page,
      game_id,
      order,
      price,
      time,
      date,
      player,
      player_id
    );

    const total = sessions.result.length;
    // console.log(sessions.result.length);

    sessions.result = await Promise.all(
      sessions.result.map((session) =>
        this.addPlayerAvaliationToSession(session)
      )
    );

    // await Promise.all(
    //   sessions.result.map(async (session) => {
    //     session.Player.player_stats =
    //       await this.playerRepository.findPlayerStatsByGame(
    //         session.player_id,
    //         session.game_id
    //       );
    //   })
    // );

    if (available) {
      sessions.result = sessions.result.filter(
        (session) => session.players_seats.length < session.seats
      );
    }

    // if (player) {
    //   sessions.result = sessions.result.filter((session) =>
    //     session.Player.nickname.toLowerCase().includes(player.toLowerCase())
    //   );
    // }

    if (seats) {
      sessions.result = sessions.result.filter((session) =>
        seats.includes(session.seats)
      );
    }

    if (time && !date) {
      sessions.result = sessions.result.filter((session) =>
        this.dateProvider.compareHours(session.start_time, time)
      );
    }

    if (duration) {
      sessions.result = sessions.result.filter(
        (session) =>
          this.dateProvider.compareInHours(
            session.start_time,
            session.end_time
          ) === duration
      );
    }

    if (avaliation) {
      const { min, max } = avaliation;

      sessions.result = sessions.result.filter(
        (session) =>
          (!min || session.avaliation >= min) &&
          (!max || session.avaliation <= max)
      );
    }

    // switch (order) {
    //   case "avaliation":
    //     sessions.result.sort((a, b) => b.avaliation - a.avaliation);
    //     break;
    // }

    if (order === "avaliation")
      sessions.result.sort((a, b) => b.avaliation - a.avaliation);

    if (start_date && end_date) {
      const startDate = new Date(start_date);
      const endDate = new Date(end_date);
      endDate.setDate(endDate.getDate() + 1);
      sessions.result = sessions.result.filter((session) => {
        const sessionDate = new Date(session.start_time);
        return sessionDate >= startDate && sessionDate <= endDate;
      });
    }
    
    if (tags && tags.length > 0) {
      sessions.result = sessions.result.filter(session => {
        const sessionTagNames = session.avaliations.tags.map(tag => tag.name);
        return tags.every(tag => sessionTagNames.includes(tag));
      });
    }

    // Filter to get only future sessions.result
    // sessions.result = sessions.result.filter((session) =>
    //   this.dateProvider.compareIfBefore(new Date(), session.start_time)
    // );

    // pagination badaras
    if (page && limit) {
      sessions.result = sessions.result.slice((page - 1) * limit, page * limit);
    }

    sessions.info = {
      count: total,
      pages: Math.ceil(total / limit),
      next: page * limit < total ? page + 1 : null,
      prev: page === 1 ? null : page - 1,
    };

    // console.log(sessions);

    // const SessionsDTO = sessions.map((session) => SessionMap.toDTO(session));

    return sessions;
  }
}
