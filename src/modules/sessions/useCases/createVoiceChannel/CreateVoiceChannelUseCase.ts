import { container, inject, injectable } from "tsyringe";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";
import axios from "axios";
import { UpdateSessionUseCase } from "../updateSession/UpdateSessionUseCase";

@injectable()
export class CreateVoiceChannelUseCase {
  private readonly DISCORD_BOT_TOKEN = process.env.DISCORD_BOT_TOKEN;
  private readonly GUILD_ID = process.env.GUILD_ID;

  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
  ) {
    if (!this.DISCORD_BOT_TOKEN || !this.GUILD_ID) {
      throw new AppError('Configurações do Discord não definidas.');
    }
  }

  async execute(session_id: number): Promise<string> {
    const session = await this.sessionRepository.findById(session_id);
    if (!session) {
      throw new AppError("Sessão não encontrada.");
    }
    const updateSessionUseCase= container.resolve(UpdateSessionUseCase)
    try {
      const channelData = {
        name:`Mentoria Knowbie Sessão ${session_id}`,
        type: 2,
      };

      const response = await axios.post(
        `https://discord.com/api/v10/guilds/${this.GUILD_ID}/channels`,
        channelData,
        {
          headers: {
            'Authorization': `Bot ${this.DISCORD_BOT_TOKEN}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const channelId = response.data.id;      
      const invite = await this.sessionRepository.createLinkDiscord(channelId);
      const inviteURL = `https://discord.gg/${invite.code}`
      try{
        await this.sessionRepository.createInviteDiscord(session_id, inviteURL, channelId);
      }catch(error) {
        console.log(error);
      }
      await updateSessionUseCase.execute({
        session_id: Number(session_id),
        editOptions: "communication_link",
        communication_link: inviteURL,
      });
      return inviteURL;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const message = error.response?.data?.message || error.message;
        throw new AppError(`Erro ao criar o canal de voz: ${message}`);
      } else {
        throw new AppError('Erro ao criar o canal de voz.', error);
      }
    }
  }
}
