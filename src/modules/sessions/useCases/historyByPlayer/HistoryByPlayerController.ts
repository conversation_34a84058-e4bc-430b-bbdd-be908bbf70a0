import { Request, Response } from "express";
import { container } from "tsyringe";

import { HistoryByPlayerUseCase } from "./HistoryByPlayerUseCase";

export class HistoryByPlayerController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { player_id } = request.params;
    const { page, limit, filter } = request.query;

    const historyByPlayerUseCase = container.resolve(HistoryByPlayerUseCase);

    const history = await historyByPlayerUseCase.execute({
      player_id: Number(player_id),
      page: Number(page),
      limit: Number(limit),
      filter: String(filter),
    });

    return response.json(history);
  }
}
