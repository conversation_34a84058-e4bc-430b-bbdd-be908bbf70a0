import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  player_id: number;
  page: number;
  limit: number;
  filter: string;
}

@injectable()
export class HistoryByPlayerUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ player_id, page, limit, filter }: IRequest): Promise<any> {
    const player = await this.playerRepository.findById(player_id);
    const sessions = await this.sessionRepository.listAll();
    const ratings = await this.sessionRepository.findAllRatings();
    let history = [],
      historyWithPagination = [];

    const filters = filter.split(",");

    if (!player) {
      throw new AppError("Player not found");
    }

    if (!page || !limit) {
      throw new AppError("Page and limit are required");
    }

    if (!filter || filter.includes("finished")) {
      const finishedSessions = sessions.filter((session) =>
        session.players_seats.some(
          (seat) => seat.player_id === player_id && seat.isFinished === true
        )
      );

      var finishedSessionWithRatings = await Promise.all(
        finishedSessions.map(async (session) => {
          const statementBySession =
            await this.sessionRepository.findStatementsBySession(session.id);

          const completedDate = statementBySession
            .map((statement) => {
              return (
                statement.type === "income" &&
                statement.Session !== null &&
                statement.session_id === session.id &&
                statement
              );
            })
            .filter((statement) => statement)[0]?.createdAt;

          return {
            id: session.id,
            status: "completed",
            completedDate,
            start_time: session.start_time,
            end_time: session.end_time,
            price: session.price,
            seats: session.seats,
            rating:
              ratings.find(
                (rating) =>
                  rating.session_id === session.id &&
                  rating.player_id === player_id
              )?.points ?? 0,
            Player: {
              id: session.player_id,
              nickname: session.Player.nickname,
              avatar: session.Player.avatar
                ? encodeURI(
                    `${process.env.AWS_BUCKET_URL}/avatar/${session.Player.avatar}`
                  )
                : null,
            },
            Game: {
              id: session.Game.id,
              logo: encodeURI(
                `${process.env.AWS_BUCKET_URL}/games/logo/${session.Game.logo}`
              ),
            },
          };
        })
      );

      history = [...history, ...finishedSessionWithRatings];
    }

    if (!filter || filter.includes("confirmed")) {
      const confirmedSessions = sessions.filter((session) =>
        session.players_seats.some(
          (seat) =>
            seat.player_id === player_id &&
            seat.isConfirmed === true &&
            seat.isFinished === false &&
            this.dateProvider.compareIfBefore(new Date(), session.start_time)
        )
      );

      var confirmedSessionsMapped = confirmedSessions.map((session) => {
        return {
          id: session.id,
          status: "confirmed",
          start_time: session.start_time,
          end_time: session.end_time,
          price: session.price,
          seats: session.seats,
          Player: {
            id: session.player_id,
            nickname: session.Player.nickname,
            avatar: session.Player.avatar
              ? encodeURI(
                  `${process.env.AWS_BUCKET_URL}/avatar/${session.Player.avatar}`
                )
              : null,
          },
          Game: {
            id: session.Game.id,
            logo: encodeURI(
              `${process.env.AWS_BUCKET_URL}/games/logo/${session.Game.logo}`
            ),
          },
        };
      });
      history = [...history, ...confirmedSessionsMapped];
    }

    if (!filter || filter.includes("awaiting_finish")) {
      const awaitingToFinishSessions = sessions.filter((session) =>
        session.players_seats.some(
          (seat) =>
            seat.player_id === player_id &&
            seat.isFinished === false &&
            this.dateProvider.compareIfBefore(session.end_time, new Date())
        )
      );

      var awaitingToFinishSessionWithRatings = await Promise.all(
        awaitingToFinishSessions.map(async (session) => {
          return {
            id: session.id,
            status: "awaiting_finish",
            completedDate: session.end_time,
            start_time: session.start_time,
            end_time: session.end_time,
            price: session.price,
            seats: session.seats,
            rating:
              ratings.find(
                (rating) =>
                  rating.session_id === session.id &&
                  rating.player_id === player_id
              )?.points ?? 0,
            Player: {
              id: session.player_id,
              nickname: session.Player.nickname,
              avatar: session.Player.avatar
                ? encodeURI(
                    `${process.env.AWS_BUCKET_URL}/avatar/${session.Player.avatar}`
                  )
                : null,
            },
            Game: {
              id: session.Game.id,
              logo: encodeURI(
                `${process.env.AWS_BUCKET_URL}/games/logo/${session.Game.logo}`
              ),
            },
          };
        })
      );
      history = [...history, ...awaitingToFinishSessionWithRatings];
    }

    if (!filter || filter.includes("awaiting_confirm")) {
      var awaitingConfirmationSessions = sessions.filter((session) =>
        session.players_seats.some(
          (seat) =>
            seat.player_id === player_id &&
            session.isActive === true &&
            seat.isConfirmed === false &&
            this.dateProvider.compareIfBefore(new Date(), session.start_time)
        )
      );

      var awaitingConfirmationSessionsMapped = awaitingConfirmationSessions.map(
        (session) => {
          return {
            id: session.id,
            status: "awaiting_confirm",
            start_time: session.start_time,
            end_time: session.end_time,
            price: session.price,
            seats: session.seats,
            Player: {
              id: session.player_id,
              nickname: session.Player.nickname,
              avatar: session.Player.avatar
                ? encodeURI(
                    `${process.env.AWS_BUCKET_URL}/avatar/${session.Player.avatar}`
                  )
                : null,
            },
            Game: {
              id: session.Game.id,
              logo: encodeURI(
                `${process.env.AWS_BUCKET_URL}/games/logo/${session.Game.logo}`
              ),
            },
          };
        }
      );
      history = [...history, ...awaitingConfirmationSessionsMapped];
    }

    if (!filter || filter.includes("canceled")) {
      const statements = await this.playerRepository.findStatementByPlayer(
        player_id,
        "createdAt"
      );

      const canceledSessionIds = statements
        .map((statement) => {
          return {
            session_id:
              statement.type === "canceled" &&
              statement.Session !== null &&
              statement.session_id,
            statement_id: statement.id,
          };
        })
        .filter((statement) => statement.session_id); // remove boolean false

      var canceledSessions = [];

      await Promise.all(
        canceledSessionIds.map(async (statement) => {
          const session = await this.sessionRepository.findById(
            statement.session_id
          );

          const completedDate = statements.find(
            (s) =>
              s.session_id === statement.session_id &&
              s.id === statement.statement_id
          )?.createdAt;

          canceledSessions.push({
            id: session.id,
            status: "canceled",
            completedDate,
            start_time: session.start_time,
            end_time: session.end_time,
            price: session.price,
            seats: session.seats,
            Player: {
              id: session.player_id,
              nickname: session.Player.nickname,
              avatar: session.Player.avatar
                ? encodeURI(
                    `${process.env.AWS_BUCKET_URL}/avatar/${session.Player.avatar}`
                  )
                : null,
            },
            Game: {
              id: session.Game.id,
              logo: encodeURI(
                `${process.env.AWS_BUCKET_URL}/games/logo/${session.Game.logo}`
              ),
            },
          });
        })
      );
      history = [...history, ...canceledSessionIds];
    }

    historyWithPagination = history
      .sort(
        (a, b) =>
          new Date(b.start_time).getTime() - new Date(a.start_time).getTime()
      ) //sort by start_time recent to older
      .slice((page - 1) * limit, page * limit);

    return historyWithPagination;
  }
}
