import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListPastSessionsByPlayerUseCase } from "./ListPastSessionsByPlayerUseCase";

export class ListPastSessionsByPlayerController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { date, limit, page } = request.query;

    const listPastSessionsByPlayerUseCase = container.resolve(
      ListPastSessionsByPlayerUseCase
    );

    const sessions = await listPastSessionsByPlayerUseCase.execute({
      date: new Date(date as string),
      player_id: Number(id),
      limit: Number(limit),
      page: Number(page)
    });

    return response.json(sessions);
  }
}
