import { RRule } from "rrule";
import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

interface IRequest {
  player_id: number;
  date: Date;
  limit: number;
  page: number;
}

@injectable()
export class ListPastSessionsByPlayerUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ date, player_id, limit, page }: IRequest): Promise<any> {
    if (date === undefined) {
      date = new Date();
    }

    if (!page || !limit) {
      throw new Error("Page and limit are required");
    }

    const player = await this.playerRepository.findById(player_id);

    const recurring_sessions =
      await this.sessionRepository.listRecurringSessions();

    player.recurring_sessions = recurring_sessions;

    const past_sessions =
      await this.sessionRepository.findFullPastSessionsByPlayerId(
        player.id,
        page,
        limit
      );

    let rating, ratings;
    for (const session of past_sessions) {
      let ratings_quantity = 0;
      let ratings_sum = 0;
      ratings = await this.sessionRepository.findRatingsBySessionId(session.id);

      if (ratings.length > 0) {
        ratings.forEach((rating) => {
          ratings_quantity += 1;
          ratings_sum += rating.points;
        });
        rating = (ratings_sum / ratings_quantity).toFixed(1);
      } else {
        rating = null;
      }
      session.avaliation = rating;
    }

    return past_sessions.slice(0, 60);
  }
}
