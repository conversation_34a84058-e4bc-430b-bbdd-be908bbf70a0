import { Request, Response } from "express";
import { container } from "tsyringe";

import { HighlightsSessionsUseCase } from "./HighlightsSessionsUseCase";

export class HighlightsSessionsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const highlightsSessionsUseCase = container.resolve(
      HighlightsSessionsUseCase
    );

    const highlightsSessions = await highlightsSessionsUseCase.execute();

    return response.json(highlightsSessions);
  }
}
