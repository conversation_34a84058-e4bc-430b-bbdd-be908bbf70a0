import { shuffle } from "lodash";
import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionDTO } from "@modules/sessions/dtos/ISessionDTO";
import { SessionMap } from "@modules/sessions/mapper/sessionMap";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";

import { PrismaClient } from ".prisma/client";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

@injectable()
export class HighlightsSessionsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("PrismaClient") private prisma: PrismaClient,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async addPlayerEvaluationByGame(
    player_id: number,
    game_id: number
  ): Promise<number> {
    const avaliations = await this.playerRepository.findAvaliationsByGame(
      player_id,
      game_id
    );

    let average = 5;

    if (avaliations.length > 0) {
      average =
        avaliations.reduce((acc, curr) => {
          return acc + curr.points;
        }, 5) /
        (avaliations.length + 1);
    }

    return average;
  }

  // 9 no total:
  // Primeiro - 3 Sessões Promovidas (Randomico)
  // Segundo - 3 Cards do Dash (Randomico)
  // Terceiro - 3 Sessões comuns (Randomico e mais proximas de acontecer)

  // Se essa seleção não bater 9, busca mais sessões promovidas.
  // Se ainda não bater 9, busca mais Cards do Dash.
  // Se ainda não bater 9, busca mais sessões comuns.

  async execute(): Promise<ISessionDTO[] | any> {
    const totalSessions = 9;
    const totalPerSession = 3;
    const highlightsSessions =
      await this.sessionRepository.listHighlightsSessions();
    const commonSessions = await this.sessionRepository.listActiveSessions();
    const highlightsDash = await this.prisma.highlights.findMany();
    const commonSessionWithoutHighlights = commonSessions.filter(
      (session) =>
        !highlightsSessions.find((highlight) => {
          return highlight.session_id === session.id;
        })
    );

    console.log({
      common: commonSessionWithoutHighlights.length,
      highlights: highlightsSessions.length,
      dash: highlightsDash.length,
    });

    const hasMoreCommonSessions =
      commonSessionWithoutHighlights.length > totalPerSession;
    const hasMoreHighlightsSessions =
      highlightsSessions.length > totalPerSession;
    const hasMoreHighlightsDash = highlightsDash.length > totalPerSession;

    const highlightsSessionsMapped = await Promise.all(
      shuffle(
        highlightsSessions.map((highlight) =>
          SessionMap.toDTO(highlight.Session)
        )
      ).map(async (session) => {
        const evaluation = await this.addPlayerEvaluationByGame(
          session.pro_player_id,
          session.game_id
        );
        return { ...session, avaliation: evaluation.toFixed(1) };
      })
    );

    const highlightsDashMapped = shuffle(
      highlightsDash.map((highlight) => {
        return {
          id: highlight.id,
          banner: encodeURI(
            `${process.env.AWS_BUCKET_URL}/highlights/${highlight.image}`
          ),
        };
      })
    );

    const commonSessionsMapped = await Promise.all(
      shuffle(
        commonSessionWithoutHighlights
          .filter((session) => session.players_seats.length === 0)
          .map((session) => SessionMap.toDTO(session))
      ).map(async (session) => {
        const evaluation = await this.addPlayerEvaluationByGame(
          session.pro_player_id,
          session.game_id
        );
        return { ...session, avaliation: evaluation.toFixed(1) };
      })
    );

    const shuffled = [
      ...highlightsSessionsMapped.slice(0, 3), // Randomize and slice to get only 3
      ...highlightsDashMapped.slice(0, 3), // Randomize and slice to get only 3,
      ...commonSessionsMapped.slice(0, 3), // Randomize, filter to get only free session, and slice to get only 3,
    ];

    let highlights = shuffled.filter(
      (highlight, index, self) =>
        index === self.findIndex((item) => item.id === highlight.id)
    );

    console.log({ init_len: highlights.length });

    if (highlights.length < totalSessions) {
      let moreHighlightsSessions = [];

      if (hasMoreHighlightsSessions) {
        moreHighlightsSessions = highlightsSessionsMapped.slice(3);
      }

      if (hasMoreHighlightsDash) {
        moreHighlightsSessions = highlightsDashMapped.slice(3);
      }

      if (hasMoreCommonSessions) {
        moreHighlightsSessions = commonSessionsMapped.slice(3);
      }

      highlights.push(...moreHighlightsSessions);
    }

    console.log({ len: highlights.length });

    highlights = highlights.filter((highlight) =>
      this.dateProvider.compareIfBefore(new Date(), highlight.start_time)
    );

    return highlights.slice(0, totalSessions);
  }
}
