import dayjs from "dayjs";
import "dayjs/locale/pt-br";
import { inject, injectable } from "tsyringe";

import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";

interface IRequest {
  filter: "games" | "seats" | "hours" | "weekdays";
}

@injectable()
export class SessionsInfosUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("GamesRepository") private gamesRepository: IGamesRepository
  ) {}

  async execute({ filter }: IRequest): Promise<any> {
    const sessions = await this.sessionRepository.listAll();

    if (filter === "games") {
      const games = await this.gamesRepository.listAllActives();
      const accGames = games.reduce((acc, game) => {
        acc[game.name] = { quantity: 0 };
        return acc;
      }, {});

      const sessionsByGame = sessions.reduce((acc, session) => {
        const { Game } = session;

        acc[Game.name].quantity += 1;

        return acc;
      }, accGames);

      const sessionsByGameArray = Object.keys(sessionsByGame).map((key) => {
        return {
          label: key,
          quantity: sessionsByGame[key].quantity,
        };
      });

      return sessionsByGameArray;
    }

    if (filter === "seats") {
      const sessionsBySeats = sessions.reduce(
        (acc, session) => {
          const { seats } = session;
          if (!acc[seats]) {
            acc[seats] = { quantity: 0 };
          }
          acc[seats].quantity += 1;

          return acc;
        },
        {
          1: { quantity: 0 },
          2: { quantity: 0 },
          3: { quantity: 0 },
          4: { quantity: 0 },
        }
      );

      const sessionsBySeatsArray = Object.keys(sessionsBySeats).map((key) => {
        return {
          label: key === "1" ? "Individual" : `${key} Pessoas`,
          quantity: sessionsBySeats[key].quantity,
        };
      });

      return sessionsBySeatsArray;
    }

    if (filter === "hours") {
      const sessionsByHours = sessions.reduce((acc, session) => {
        const { start_time } = session;
        const hour = start_time.getHours();
        if (!acc[hour]) {
          acc[hour] = { quantity: 0 };
        }
        acc[hour].quantity += 1;

        return acc;
      }, {});

      const sessionsByHoursArray = Object.keys(sessionsByHours).map((key) => {
        return {
          label: key === "0" ? "00:00" : `${key}:00`,
          quantity: sessionsByHours[key].quantity,
        };
      });

      return sessionsByHoursArray;
    }

    if (filter === "weekdays") {
      const sessionsByWeekdays = sessions.reduce(
        (acc, session) => {
          const { start_time } = session;
          const day = start_time.getDay();
          const key = dayjs()
            .day(Number(day))
            .locale("pt-br")
            .format("ddd")
            .toLowerCase();

          if (!acc[key]) {
            acc[key] = { quantity: 0 };
          }
          acc[key].quantity += 1;

          return acc;
        },
        {
          dom: { quantity: 0 },
          seg: { quantity: 0 },
          ter: { quantity: 0 },
          qua: { quantity: 0 },
          qui: { quantity: 0 },
          sex: { quantity: 0 },
          sáb: { quantity: 0 },
        }
      );

      const sessionsByWeekdaysArray = Object.keys(sessionsByWeekdays).map(
        (key) => {
          return {
            label: key,
            quantity: sessionsByWeekdays[key].quantity,
          };
        }
      );

      return sessionsByWeekdaysArray;
    }

    return [];
  }
}
