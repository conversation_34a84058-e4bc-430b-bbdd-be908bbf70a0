import { Request, Response } from "express";
import { container } from "tsyringe";

import { SessionsInfosUseCase } from "./SessionsInfosUseCase";

export class SessionsInfosController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { filter } = request.query;

    const sessionsInfosUseCase = container.resolve(SessionsInfosUseCase);

    const infos = await sessionsInfosUseCase.execute({
      filter: filter as "games" | "seats" | "hours" | "weekdays",
    });

    return response.json(infos);
  }
}
