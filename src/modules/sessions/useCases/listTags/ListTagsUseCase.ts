import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { Tag } from "@prisma/client";

@injectable()
export class ListTagsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository
  ) {}

  async execute(): Promise<Tag[]> {
    const tags = await this.sessionRepository.listTags();

    return tags;
  }
}
