import { ISessionDTO, listSessions } from "../dtos/ISessionDTO";

export class SessionMap {
  public static toDTO(session: listSessions): ISessionDTO {
    return {
      id: session.id,
      start_time: session.start_time,
      end_time: session.end_time,
      price: session.price,
      seats: session.seats,
      available_seats: session.seats - session.players_seats.length,
      avaliation: session.avaliation && Number(session.avaliation.toFixed(1)),
      pro_player_id: session.player_id,
      pro_player_avatar:
        session.Player.avatar &&
        encodeURI(
          `${process.env.AWS_BUCKET_URL}/avatar/${session.Player.avatar}`
        ),
      pro_player_nickname: session.Player.nickname,
      communication_link: session.communication_link,
      input_id: session.Input?.id,
      input_description: session.Input?.description,
      platform_id: session.Platform?.id,
      platform_description: session.Platform?.description,
      game_name: session.Game.name,
      game_id: session.Game.id,
      game_banner:
        session.Game.banner &&
        encodeURI(
          `${process.env.AWS_BUCKET_URL}/games/banner/${session.Game.banner}`
        ),
      game_logo:
        session.Game.logo &&
        encodeURI(
          `${process.env.AWS_BUCKET_URL}/games/logo/${session.Game.logo}`
        ),
      player_stats: session.Player.player_stats?.map((stat) => {
        return {
          name: stat.Game_stats.name,
          slug: stat.Game_stats.slug,
          value: stat.value,
        };
      }),
      canceledAt: session.canceledAt,
      playerHasSeat: session.players_seats,
    };
  }
}
