import { PrismaClient, Holiday } from ".prisma/client";
import { inject, injectable } from "tsyringe";
import { IHolidaysRepository } from "@modules/holiday/repositories/IHolidaysRepository"
 
@injectable()
export class HolidaysRepository implements IHolidaysRepository {
  

  constructor(@inject("PrismaClient") private prisma: PrismaClient) {}

  async listHolidays(): Promise<Holiday[]> {
    const holidays = await this.prisma.holiday.findMany();
    return holidays;
  }

}
