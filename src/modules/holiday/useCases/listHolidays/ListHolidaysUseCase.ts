import { inject, injectable, container } from "tsyringe";
import { IHolidaysRepository } from "@modules/holiday/repositories/IHolidaysRepository";
import { HolidaysRepository } from "@modules/holiday/infra/HolidaysRepository"
container.registerSingleton('HolidaysRepository', HolidaysRepository);
@injectable()
export class ListHolidaysUseCase {
  constructor(@inject("HolidaysRepository") private holidaysRepository: IHolidaysRepository) {}
  async execute(): Promise<any> {
      const allHolidays = await this.holidaysRepository.listHolidays();
      return allHolidays;
  }
}
