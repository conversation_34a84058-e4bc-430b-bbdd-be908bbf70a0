import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListHolidaysUseCase } from "./ListHolidaysUseCase";

export class ListHolidaysController {
  async handle(request: Request, response: Response): Promise<Response> {
    
    const listHolidaysUseCase = container.resolve(
      ListHolidaysUseCase
    );
    const allHolidays = await listHolidaysUseCase.execute();

    return response.json(allHolidays);
  }
}
