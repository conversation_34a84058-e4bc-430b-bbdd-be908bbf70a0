import { inject, injectable } from "tsyringe";
import { ISocketRepository } from "../repositories/ISocketRepository";
import { PrismaClient } from "@prisma/client";
import { IInsertMessageChatDTO } from "../dtos/IInsertMessageChatDTO";

@injectable()
export class SocketRepository implements ISocketRepository {
  constructor(@inject("PrismaClient") private prisma: PrismaClient) {}
  async insertMessage(data: IInsertMessageChatDTO): Promise<any> {
    const message = await this.prisma.message_chat.create({
      data: data,
    });

    return message;
  }
}
