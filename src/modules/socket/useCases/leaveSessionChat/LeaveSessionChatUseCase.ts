import { Server, Socket } from "socket.io";
import { configureSocket } from "@config/socket";
import { ISocketRepository } from "@modules/socket/repositories/ISocketRepository";
import { inject, injectable } from "tsyringe";

@injectable()
export class LeaveSessionChatUseCase {
  constructor(
    @inject("SocketRepository") private socketRepository: ISocketRepository
  ) {}
  async LeaveChat(
    socket,
    sessions,
    sessionName: string,
    player_id: string
  ): Promise<any> {
    sessions.forEach((users) => {
      users.delete(socket.id);
      users.delete(player_id);
      if (users.size === 0) {
        sessions.delete(sessionName);
      }
    });
    console.log(
      `Um cliente com socket ${socket.id} desconectou-se do servidor.`
    );
  }
}
