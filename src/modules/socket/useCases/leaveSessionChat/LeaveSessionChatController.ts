import { configureSocket } from "@config/socket";
import { LeaveSessionChatUseCase } from "./LeaveSessionChatUseCase";
import { container } from "tsyringe";

module.exports = (socket, sessions) => {
  socket.on("leaveSessionChat", (sessionName, player_id) => {
    try {
      const leaveSessionChatUseCase = container.resolve(
        LeaveSessionChatUseCase
      );
      const result = leaveSessionChatUseCase.LeaveChat(
        socket,
        sessions,
        sessionName,
        String(player_id)
      );
    } catch (error) {
      console.log(error);
    }
  });
};
