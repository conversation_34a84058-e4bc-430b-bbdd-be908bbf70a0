import { Server, Socket } from "socket.io";
import { configureSocket } from "@config/socket";
import { ISocketRepository } from "@modules/socket/repositories/ISocketRepository";
import { inject, injectable } from "tsyringe";

@injectable()
export class JoinSessionChatUseCase {
  constructor(
    @inject("SocketRepository") private socketRepository: ISocketRepository
  ) {}
  async JoinSessionChat(socket, sessions, sessionName: string): Promise<any> {
    if (sessions.has(sessionName)) {
      sessions.get(sessionName)?.add(socket.id);
      socket.join(sessionName);
      console.log(`Um player se junto ao grupo de chat ${sessionName}`);
    }
  }
}
