import { configureSocket } from "@config/socket";
import { JoinSessionChatUseCase } from "./JoinSessionChatUseCase";
import { container } from "tsyringe";

module.exports = (socket, sessions) => {
  socket.on("joinSessionChat", (sessionName) => {
    try {
      const joinSessionChatUseCase = container.resolve(JoinSessionChatUseCase);
      const result = joinSessionChatUseCase.JoinSessionChat(
        socket,
        sessions,
        sessionName
      );
    } catch (error) {
      console.log(error);
    }
  });
};
