module.exports = (socket, io, sessions) => {
  socket.on("getListOnline", async (socket_id) => {
    try {
      let result = [];
      sessions.forEach((session) => {
        const myArray = [...session];
        //Por algum motivo, as vezes a posição do Set se inverte, ordem certa <socket_id, player_id>
        //Essa lógica garante que sempre irá retornará para o front o player_id
        for (let i = 0; i < myArray.length; i++) {
          if (Number(myArray[i])) result.push(myArray[i]);
        }
      });

      io.to(socket_id).emit("getListOnline", result);
    } catch (error) {
      console.log(error);
    }
  });
};
