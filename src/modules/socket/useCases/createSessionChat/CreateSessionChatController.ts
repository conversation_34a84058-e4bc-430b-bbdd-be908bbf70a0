import { configureSocket } from "@config/socket";
import { CreateSessionChatUseCase } from "./CreateSessionChatUseCase";
import { container } from "tsyringe";

module.exports = (socket, sessions) => {
  socket.on("createSession", (sessionName, player_id) => {
    try {
      const createSessionChatUseCase = container.resolve(
        CreateSessionChatUseCase
      );
      const result = createSessionChatUseCase.CreateSessionChat(
        socket,
        sessions,
        sessionName,
        player_id
      );
    } catch (error) {
      console.log(error);
    }
  });
};
