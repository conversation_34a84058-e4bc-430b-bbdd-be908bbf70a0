import { Server, Socket } from "socket.io";
import { configureSocket } from "@config/socket";
import { ISocketRepository } from "@modules/socket/repositories/ISocketRepository";
import { container, inject, injectable } from "tsyringe";
import { JoinSessionChatUseCase } from "@modules/socket/useCases/joinSessionChat/JoinSessionChatUseCase";

@injectable()
export class CreateSessionChatUseCase {
  constructor(
    @inject("SocketRepository") private socketRepository: ISocketRepository
  ) {}
  async CreateSessionChat(
    socket,
    sessions,
    sessionName: string,
    player_id: string
  ): Promise<any> {
    const joinSessionChatUseCase = container.resolve(JoinSessionChatUseCase);
    if (!sessions.has(sessionName)) {
      sessions.set(sessionName, new Set<string>());
    } else {
      joinSessionChatUseCase.JoinSessionChat(socket, sessions, sessionName);
    }
    sessions.get(sessionName)?.add(socket.id);
    sessions.get(sessionName)?.add(player_id);
    socket.join(sessionName);
    console.log(`Chat criado com o grupo de nome ${sessionName}`);
  }
}
