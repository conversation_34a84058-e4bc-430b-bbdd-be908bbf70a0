import { configureSocket } from "@config/socket";
import { SendMessageUseCase } from "./SendMessageUseCase";
import { container } from "tsyringe";

module.exports = (socket, io) => {
  socket.on("sendMessage", async (data, sessionName) => {
    try {
      const sendMessageUseCase = container.resolve(SendMessageUseCase);
      const { message, data_message, session_id, player_id } = data;
      const result = await sendMessageUseCase.SendMessage(
        message,
        data_message,
        session_id,
        player_id
      );

      const message_obj = {
        ...result,
        Player: data.Player,
      };
      io.to(sessionName).emit("sendMessage", message_obj);
    } catch (error) {
      console.log(error);
    }
  });
};
