import { Server, Socket } from "socket.io";
import { configureSocket } from "@config/socket";
import { ISocketRepository } from "@modules/socket/repositories/ISocketRepository";
import { inject, injectable } from "tsyringe";
import { Message_chat } from "@prisma/client";

@injectable()
export class SendMessageUseCase {
  constructor(
    @inject("SocketRepository") private socketRepository: ISocketRepository
  ) {}
  async SendMessage(
    message: string,
    data_message: Date,
    session_id: number,
    player_id: number
  ): Promise<Message_chat> {
    const message_chat = await this.socketRepository.insertMessage({
      message: message,
      data_message: data_message,
      session_id: session_id,
      player_id: player_id,
    });

    return message_chat;
  }
}
