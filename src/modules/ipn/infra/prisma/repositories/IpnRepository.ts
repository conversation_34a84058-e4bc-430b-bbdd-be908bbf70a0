import { inject, injectable } from "tsyringe";
import { IWebhookRepository } from "@modules/webhook/repositories/IWebhookRepository";

import { Payment_info, PrismaClient } from ".prisma/client";
import { IUpdatePaymentInfoDTO } from "@modules/webhook/dtos/IUpdatePaymentInfoDTO";
import { IListPaymentInfoDTO } from "@modules/webhook/dtos/IListPaymentInfoDTO";
import { ICreateChargebackDTO } from "@modules/ipn/dtos/ICreateChargebackDTO";
import { Chargeback, chargeback_dispute, chargeback_documents, chargeback_history } from "@prisma/client";
import { IIpnRepository } from "@modules/ipn/repositories/IIpnRepository";
import { IUpdateChargebackDTO } from "@modules/ipn/dtos/IUpdateChargebackDTO";
import { ICreateChargebackHistoryDTO } from "@modules/ipn/dtos/ICreateChargebackHistory";
import { ICreateChargebackDocumentsDTO } from "@modules/ipn/dtos/ICreateChargebackDocumentsDTO";

@injectable()
export class IpnRepository implements IIpnRepository {
  constructor(@inject("PrismaClient") private prisma: PrismaClient) {}

  async create(data: ICreateChargebackDTO): Promise<Chargeback> {
    const chargeback_response = await this.prisma.chargeback.create({
      data,
    });
    
    return chargeback_response;
  };
  
  async createChargebackHistory(data: ICreateChargebackHistoryDTO): Promise<chargeback_history> {
    const newChargeback = await this.prisma.chargeback_history.create({
      data
    });
    return newChargeback;
  }
  async createChargebackDocuments(data: ICreateChargebackDocumentsDTO): Promise<chargeback_documents> {
    const newChargeback = await this.prisma.chargeback_documents.create({
      data
    });
    return newChargeback;
  }
  async createChargebackDispute(chargeback_id:number): Promise<chargeback_dispute> {
    const newChargeback = await this.prisma.chargeback_dispute.create({
      data: {
        chargeback_id: chargeback_id,
      },
    });
    return newChargeback;
  }
  

  async update(id: number, data: IUpdateChargebackDTO): Promise<Chargeback> {
    const updatedChargeback = await this.prisma.chargeback.update({
      where: {
        id,
      },
      data,
    });
  
    return updatedChargeback;
  };

  async findById(external_payment_id: number): Promise<Chargeback> {
    const chargeback_response = await this.prisma.chargeback.findFirst({
      where: {
        external_payment_id,
      },
    });
  
    return chargeback_response;
  };
  async findByHash(hash: string): Promise<chargeback_documents> {
    const chargeback_response = await this.prisma.chargeback_documents.findFirst({
      where: {
        hash,
      },
    });
  
    return chargeback_response;
  };

}