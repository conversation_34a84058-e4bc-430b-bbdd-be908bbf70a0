import { Chargeback, Payment_info, chargeback_dispute, chargeback_documents, chargeback_history } from "@prisma/client";
import { ICreateChargebackDTO } from "../dtos/ICreateChargebackDTO";
import { IUpdateChargebackDTO } from "../dtos/IUpdateChargebackDTO";
import { ICreateChargebackHistoryDTO } from "../dtos/ICreateChargebackHistory";
import { ICreateChargebackDocumentsDTO } from "../dtos/ICreateChargebackDocumentsDTO";
import { ICreateChargebackDisputeDTO } from "../dtos/ICreateChargebackDisputeDTO";

export interface IIpnRepository {
  create(data: ICreateChargebackDTO): Promise<Chargeback>;
  createChargebackHistory(data: ICreateChargebackHistoryDTO): Promise<chargeback_history>;
  createChargebackDocuments(data: ICreateChargebackDocumentsDTO): Promise<chargeback_documents>;
  createChargebackDispute(chargeback_id: number): Promise<chargeback_dispute>;
  update(id: number, data: IUpdateChargebackDTO): Promise<Chargeback>;
  findById(external_payment_id: number): Promise<Chargeback>;
  findByHash(hash: string): Promise<chargeback_documents>;
}
