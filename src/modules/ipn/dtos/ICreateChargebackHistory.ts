export interface ICreateChargebackHistoryDTO {
    external_id: string;
    external_payment_id: number;
    amount: number;
    coverage_applied: boolean;
    coverage_elegible: boolean;
    documentation_required: boolean;
    documentation_status: string;
    date_documentation_deadline: Date;
    date_created: Date;
    date_last_updated: Date;
    live_mode: boolean;
    chargeback_id: number;
  }
  