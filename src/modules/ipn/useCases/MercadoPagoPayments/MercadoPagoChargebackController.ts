import { AppError } from "@shared/errors/AppError";
import { Request, Response } from "express";
import { container } from "tsyringe";

import { MercadoPagoChargebackUseCase } from "./MercadoPagoChargebackUseCase";
import { IpnRepository } from "@modules/ipn/infra/prisma/repositories/IpnRepository";

export class MercadoPagoChargebackController {
  async handle(request: Request, response: Response): Promise<Response> {
    const {
      description,
      merchant_order,
      payment_id
    } = request.body;
    container.registerSingleton<IpnRepository>('IpnRepository', IpnRepository);
    const mercadoPagoChargebackUseCase = container.resolve(
      MercadoPagoChargebackUseCase
    );
    
    const result = await mercadoPagoChargebackUseCase.execute({
      description,
      merchant_order,
      payment_id
    });

    return response.status(200).send();
  }
}
