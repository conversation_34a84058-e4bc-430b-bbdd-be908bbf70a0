import Axios, { AxiosInstance } from "axios";
import { inject, injectable } from "tsyringe";
import * as crypto from 'crypto';
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { ITributeRepository } from "@modules/tributes/repositories/ITributeRepository";
import { IIpnRepository } from "@modules/ipn/repositories/IIpnRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";
import { IMercadoPagoIpnDTO } from "@modules/ipn/dtos/IIpnDTO";

@injectable()
export class MercadoPagoChargebackUseCase {
  private client: AxiosInstance;

  constructor(
    @inject("IpnRepository") private ipnRepository: IIpnRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("TributeRepository") private tributeRepository: ITributeRepository
  ) {
    this.client = Axios.create({
      baseURL: "https://api.mercadopago.com/v1",
      headers: {
        Authorization: `Bearer ${process.env.MP_ACCESS_TOKEN}`,
        "Content-Type": "application/json",
      },
    });
  }

  async execute(body: IMercadoPagoIpnDTO): Promise<void> {
    try {
      const payment_id = body.payment_id;
      const { data } = await this.client.get(`/chargeback/search?payment_id=${payment_id}`);
      const chargebackData ={
        external_id: data.id,
        external_payment_id: data.payments,
        amount: data.amount,
        coverage_applied: data.coverage_applied,
        coverage_elegible: data.coverage_elegible,
        documentation_required: data.documentation_required,
        documentation_status: data.documentation_status,
        date_documentation_deadline: new Date(data.date_documentation_deadline),
        date_created: new Date(data.date_created),
        date_last_updated: new Date(data.date_last_updated),
        live_mode: data.live_mode
      };
      const existChargeback = await this.ipnRepository.findById(chargebackData.external_payment_id);
      const documentation = data.documentation;
      if (!existChargeback) {
        const chargeback = await this.ipnRepository.create(chargebackData);
        await this.ipnRepository.createChargebackHistory({
          chargeback_id: chargeback.id,
          ...chargebackData,
        });
        await this.ipnRepository.createChargebackDispute(chargeback.id);
        for (const doc of documentation) {
          const { type, url, description } = doc;
          const hash = ''+ md5(JSON.stringify({ type, url, description }));
          const existingDocument = await this.ipnRepository.findByHash(hash);
          if (!existingDocument) {
            await this.ipnRepository.createChargebackDocuments({type, url, description, hash, chargeback_id:chargeback.id});
          }
        }
      };
      if(existChargeback){
        await this.ipnRepository.update(existChargeback.id, chargebackData)
        await this.ipnRepository.createChargebackHistory({
          chargeback_id: existChargeback.id,
          ...chargebackData,
        });
        for (const doc of documentation) {
          const { type, url, description } = doc;
          const hash = ''+ md5(JSON.stringify({ type, url, description }));
          const existingDocument = await this.ipnRepository.findByHash(hash);
          if (!existingDocument) {
            await this.ipnRepository.createChargebackDocuments({type, url, description, hash, chargeback_id:existChargeback.id});
          }
        }
      };

    } catch (err) {
      console.log(err);
      throw new AppError("Something went wrong!", 400);
    }
  }
}
function md5(data: string): string {
  return crypto.createHash('md5').update(data).digest('hex');
}