import { inject, injectable } from 'tsyringe';

import { AppError } from '@shared/errors/AppError';
import { ICreatePlayerDTO } from '@modules/players/dtos/ICreatePlayerDTO';
import { IPlayerRepository } from '@modules/players/repositories/IPlayerRepository';
import { IUpdatePlayerDTO } from '@modules/players/dtos/IUpdatePlayerDTO';
import { ICreatePlayerTokenDTO } from '@modules/players/dtos/ICreatePlayerTokenDTO';
import { IPlayerStatsByGame } from '@modules/players/dtos/IPlayerStatsByGame';
import { IUpdatePlayerStatsByGame } from '@modules/players/dtos/IUpdatePlayerStatsByGame';
import {
	fullPlayer,
	TPlayerStats,
	TRating,
	TStatement,
	TTag
} from '@modules/players/dtos/IPlayerDTO';

import {
	Player,
	Wallet,
	Player_token,
	PrismaClient,
	Federated_credentials,
	Payment_info,
	Rating,
	Player_notification,
	Notification,
	Account_statement,
	Game,
	Session,
	Mentoring_games
} from '.prisma/client';
import { Promoted_player } from '@prisma/client';
import { IDevice } from '@modules/players/dtos/IDeviceDTO';
import { ILanguage } from '@modules/players/dtos/ILanguageDTO';

@injectable()
export class PlayerRepository implements IPlayerRepository {
	constructor(@inject('PrismaClient') private prisma: PrismaClient) {}

	async create({
		email,
		nickname,
		password,
		name,
		cpf,
		birthdate,
		isPro,
		push_token,
		cover
	}: ICreatePlayerDTO): Promise<any> {
		try {
			const player = await this.prisma.player.create({
				data: {
					email,
					nickname,
					password,
					name,
					cpf,
					birthdate,
					isPro,
					push_token,
					cover_picture: cover,
					wallet: {
						create: {}
					}
				},
				include: {
					wallet: true,
					badges: true,
					games: true,
					sessions: true,
					Rating: true
				}
			});

			return player;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async rankingPlayersByViews(): Promise<Player[]> {
		try {
			const players = await this.prisma.player.findMany({
				orderBy: {
					total_views_twitch: 'desc'
				}
			});
			return players;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async rankingPlayersBySubscribes(): Promise<Player[]> {
		try {
			const players = await this.prisma.player.findMany({
				orderBy: {
					subscribe_twitch: 'desc'
				}
			});
			return players;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async listAll(): Promise<fullPlayer[]> {
		try {
			const players = await this.prisma.player.findMany({
				where: {
					deletedAt: null
				},
				include: {
					badges: true,
					wallet: true,
					games: true,
					Mentoring_games: {
						include: {
							Game: true
						}
					},
					sessions: {
						include: {
							promoted_session: {
								select: {
									active: true
								}
							},
							Game: {
								select: {
									id: true,
									logo: true
								}
							},
							Player: {
								select: {
									nickname: true,
									avatar: true
								}
							},
							players_seats: {
								select: {
									Player: {
										select: {
											id: true,
											avatar: true,
											nickname: true
										}
									},
									isConfirmed: true
								}
							}
						}
					}
				}
			});

			const allPlayers = await Promise.all(
				players.map(async player => {
					const sessions = await this.prisma.session.findMany({
						include: {
							promoted_session: {
								select: {
									active: true
								}
							},
							Game: {
								select: {
									id: true,
									logo: true
								}
							},
							Player: {
								select: {
									nickname: true,
									avatar: true
								}
							},
							players_seats: {
								select: {
									Player: {
										select: {
											id: true,
											avatar: true,
											nickname: true
										}
									},
									isConfirmed: true
								}
							}
						},
						where: {
							players_seats: {
								some: {
									player_id: player.id
								}
							}
						}
					});

					player.sessions = player.sessions.concat(sessions);

					return player;
				})
			);

			return allPlayers;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findById(id: number): Promise<fullPlayer> {
		try {
			const player = await this.prisma.player.findFirst({
				include: {
					badges: true,
					wallet: true,
					games: true,
					Mentoring_games: {
						include: {
							Game: true
						}
					},
					sessions: {
						where: {
							deletedAt: null
						},
						include: {
							promoted_session: {
								select: {
									active: true
								}
							},
							Game: {
								select: {
									id: true,
									logo: true
								}
							},
							Player: {
								select: {
									nickname: true,
									avatar: true
								}
							},
							players_seats: {
								select: {
									Payment_info: true,
									Player: {
										select: {
											id: true,
											avatar: true,
											nickname: true
										}
									},
									isConfirmed: true,
									isFinished: true,
									hasDispute: true,
									hasChargeback: true,
									isConfirmedAfterSession: true
								}
							}
						}
					}
				},
				where: {
					id
				}
			});

			if (player) {
				const sessions = await this.prisma.session.findMany({
					include: {
						promoted_session: {
							select: {
								active: true
							}
						},
						Game: {
							select: {
								id: true,
								logo: true
							}
						},
						Player: {
							select: {
								nickname: true,
								avatar: true
							}
						},
						players_seats: {
							select: {
								Player: {
									select: {
										id: true,
										avatar: true,
										nickname: true
									}
								},
								isConfirmed: true
							}
						}
					},
					where: {
						players_seats: {
							some: {
								player_id: player.id
							}
						}
					}
				});

				player.sessions = player.sessions.concat(sessions);
			}

			return player;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findByEmail(email: string): Promise<fullPlayer> {
		try {
			const player = await this.prisma.player.findFirst({
				include: {
					badges: true,
					wallet: true,
					games: true,
					Mentoring_games: {
						include: {
							Game: true
						}
					},
					sessions: {
						include: {
							promoted_session: {
								select: {
									active: true
								}
							},
							Game: {
								select: {
									id: true,
									logo: true
								}
							},
							Player: {
								select: {
									nickname: true,
									avatar: true
								}
							},
							players_seats: {
								select: {
									Player: {
										select: {
											id: true,
											avatar: true,
											nickname: true
										}
									},
									isConfirmed: true
								}
							}
						}
					}
				},
				where: {
					email
				}
			});

			if (player) {
				const sessions = await this.prisma.session.findMany({
					include: {
						promoted_session: {
							select: {
								active: true
							}
						},
						Game: {
							select: {
								id: true,
								logo: true
							}
						},
						Player: {
							select: {
								nickname: true,
								avatar: true
							}
						},
						players_seats: {
							select: {
								Player: {
									select: {
										id: true,
										avatar: true,
										nickname: true
									}
								},
								isConfirmed: true
							}
						}
					},

					where: {
						players_seats: {
							some: {
								player_id: player.id
							}
						}
					}
				});

				player.sessions = player.sessions.concat(sessions);
			}

			return player;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}
	async findByCpf(cpf: string): Promise<fullPlayer> {
		try {
			const player = await this.prisma.player.findFirst({
				include: {
					badges: true,
					wallet: true,
					games: true,
					Mentoring_games: {
						include: {
							Game: true
						}
					},
					sessions: {
						include: {
							promoted_session: {
								select: {
									active: true
								}
							},
							Game: {
								select: {
									id: true,
									logo: true
								}
							},
							Player: {
								select: {
									nickname: true,
									avatar: true
								}
							},
							players_seats: {
								select: {
									Player: {
										select: {
											id: true,
											avatar: true,
											nickname: true
										}
									},
									isConfirmed: true
								}
							}
						}
					}
				},
				where: {
					cpf
				}
			});

			if (player) {
				const sessions = await this.prisma.session.findMany({
					include: {
						promoted_session: {
							select: {
								active: true
							}
						},
						Game: {
							select: {
								id: true,
								logo: true
							}
						},
						Player: {
							select: {
								nickname: true,
								avatar: true
							}
						},
						players_seats: {
							select: {
								Player: {
									select: {
										id: true,
										avatar: true,
										nickname: true
									}
								},
								isConfirmed: true
							}
						}
					},

					where: {
						players_seats: {
							some: {
								player_id: player.id
							}
						}
					}
				});

				player.sessions = player.sessions.concat(sessions);
			}

			return player;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findByNickname(nickname: string): Promise<fullPlayer> {
		try {
			const player = await this.prisma.player.findFirst({
				include: {
					badges: true,
					wallet: true,
					games: true,
					Mentoring_games: {
						include: {
							Game: true
						}
					},
					sessions: {
						include: {
							promoted_session: {
								select: {
									active: true
								}
							},
							Game: {
								select: {
									id: true,
									logo: true
								}
							},
							Player: {
								select: {
									nickname: true,
									avatar: true
								}
							},
							players_seats: {
								select: {
									Player: {
										select: {
											id: true,
											avatar: true,
											nickname: true
										}
									},
									isConfirmed: true
								}
							}
						}
					}
				},
				where: {
					nickname
				}
			});

			if (player) {
				const sessions = await this.prisma.session.findMany({
					include: {
						promoted_session: {
							select: {
								active: true
							}
						},
						Game: {
							select: {
								id: true,
								logo: true
							}
						},
						Player: {
							select: {
								nickname: true,
								avatar: true
							}
						},
						players_seats: {
							select: {
								Player: {
									select: {
										id: true,
										avatar: true,
										nickname: true
									}
								},
								isConfirmed: true
							}
						}
					},
					where: {
						players_seats: {
							some: {
								player_id: player.id
							}
						}
					}
				});

				player.sessions = player.sessions.concat(sessions);
			}

			return player;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findByToken(token: string): Promise<Player_token> {
		try {
			const player_token = await this.prisma.player_token.findFirst({
				where: {
					token
				}
			});

			return player_token;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findTokenByCode(code: string): Promise<Player_token> {
		try {
			const player_token = await this.prisma.player_token.findFirst({
				where: {
					code
				}
			});

			return player_token;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async updateById({
		id,
		nickname,
		avatar,
		games,
		password,
		cover,
		birth_date,
		cpf,
		bio,
		name,
		phone,
		isPro,
		push_token,
		email,
		stream_link,
		isEmailVerified,
		address,
		nickname_twitch,
		subscribe_twitch,
		total_views_twitch
	}: IUpdatePlayerDTO): Promise<fullPlayer> {
		try {
			let migrationDate: Date;
			let badge_id;
			if (isPro) {
				migrationDate = new Date();
				const badge = await this.prisma.badge.findFirst({
					where: {
						name: 'Jogador Pro'
					}
				});

				badge_id = badge.id;
			}

			const player = await this.prisma.player.update({
				where: {
					id
				},
				data: {
					nickname,
					avatar,
					password,
					cpf,
					bio,
					isPro,
					email,
					stream_link,
					push_token,
					isEmailVerified,
					cep: address?.cep,
					state: address?.state,
					city: address?.city,
					district: address?.district,
					address: address?.street,
					number: address?.number,
					complement: address?.complement,
					ProMigrationDate: migrationDate,
					birthdate: birth_date,
					name,
					phone,
					cover_picture: cover,
					nickname_twitch,
					subscribe_twitch,
					total_views_twitch,
					games: {
						connect: games?.map(game => ({ id: game }))
					},
					badges: {
						connect: isPro && [{ id: badge_id }]
					}
				},
				include: {
					badges: true,
					wallet: true,
					games: true,
					Mentoring_games: {
						include: {
							Game: true
						}
					},
					sessions: {
						include: {
							promoted_session: {
								select: {
									active: true
								}
							},
							Game: {
								select: {
									id: true,
									logo: true
								}
							},
							Player: {
								select: {
									nickname: true,
									avatar: true
								}
							},
							players_seats: {
								select: {
									Player: {
										select: {
											id: true,
											avatar: true,
											nickname: true
										}
									},
									isConfirmed: true
								}
							}
						}
					}
				}
			});
			if (player) {
				const sessions = await this.prisma.session.findMany({
					include: {
						promoted_session: {
							select: {
								active: true
							}
						},
						Game: {
							select: {
								id: true,
								logo: true
							}
						},
						Player: {
							select: {
								nickname: true,
								avatar: true
							}
						},
						players_seats: {
							select: {
								Player: {
									select: {
										id: true,
										avatar: true,
										nickname: true
									}
								},
								isConfirmed: true
							}
						}
					},
					where: {
						players_seats: {
							some: {
								player_id: player.id
							}
						}
					}
				});

				player.sessions = player.sessions.concat(sessions);
			}

			return player;
		} catch (error) {
			console.log(error);
			throw new AppError('Internal server error', 500);
		}
	}

	async findAvaliations(id: number): Promise<TRating[]> {
		try {
			const avaliations = await this.prisma.rating.findMany({
				include: {
					Player: {
						select: {
							nickname: true,
							avatar: true
						}
					}
				},
				where: {
					Session: {
						player_id: id
					}
				}
			});

			return avaliations;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findAvaliationsByGame(
		player_id: number,
		game_id: number
	): Promise<
		(Rating & {
			Player: {
				nickname: string;
				avatar: string;
			};
		})[]
	> {
		try {
			const avaliations = await this.prisma.rating.findMany({
				include: {
					Player: {
						select: {
							nickname: true,
							avatar: true
						}
					}
				},
				where: {
					Session: {
						player_id,
						game_id
					}
				}
			});

			return avaliations;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findPromotedPlayerByPlayerId(player_id: number): Promise<Promoted_player> {
		const promoted_player = await this.prisma.promoted_player.findFirst({
			where: {
				player_id: player_id
			}
		});
		return promoted_player;
	}

	async promotePlayer(data: {
		player_id: number;
		until: Date;
		days_to_expire: number;
		price: number;
		reach_max?: number;
		reach_min?: number;
		payment_type: string;
		price_per_day: number;
		payment_info_id?: number;
	}): Promise<Promoted_player> {
		const promoted_player = await this.prisma.promoted_player.create({
			data: {
				until: data.until,
				days_to_expire: data.days_to_expire,
				price: data.price,
				expected_reach_max: data.reach_max,
				expected_reach_min: data.reach_min,
				active: false,
				payment_type: data.payment_type,
				price_per_day: data.price_per_day,
				player_id: data.player_id,
				payment_info_id: data.payment_info_id
			}
		});

		return promoted_player;
	}

	async listPromotePlayers(): Promise<Promoted_player[]> {
		let promotes_player = await this.prisma.promoted_player.findMany({
			where: {
				active: true
			},
			include: {
				Player: {
					select: {
						id: true,
						name: true,
						nickname: true,
						avatar: true,
						Rating: true
					}
				}
			}
		});
		promotes_player = promotes_player.sort((a, b) => {
			if (a.payment_type === 'now' && b.payment_type !== 'now') {
				return -1;
			}
			if (a.payment_type !== 'now' && b.payment_type === 'now') {
				return 1;
			}
			return b.price_per_day - a.price_per_day;
		});
		return promotes_player;
	}

	async listAllPromotePlayers(): Promise<Promoted_player[]> {
		const promoted_players = await this.prisma.promoted_player.findMany();
		return promoted_players;
	}

	async finishPromotePlayer(id: number): Promise<Promoted_player> {
		const promotePlayer = await this.prisma.promoted_player.update({
			where: {
				id: id
			},
			data: {
				active: false
			}
		});

		return promotePlayer;
	}
	async confirmPromotePlayer(id: number): Promise<Promoted_player> {
		const promotePlayer = await this.prisma.promoted_player.update({
			where: {
				id: id
			},
			data: {
				active: true
			}
		});

		return promotePlayer;
	}
	async findTags(id: number): Promise<TTag[]> {
		try {
			const tags: TTag[] = await this.prisma
				.$queryRaw`SELECT t.name as name, COUNT(t.name) as quantity
        FROM "_RatingToTag" rt
        INNER JOIN "Rating" r ON r.id = rt."A"
        INNER JOIN "Tag" t ON t.id = rt."B"
        INNER JOIN "Session" s ON s.id = r.session_id
        WHERE s.player_id = ${id}
        GROUP BY name
      `;

			return tags;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findPlayerStatsByGame(player_id: number, game_id: number): Promise<TPlayerStats[]> {
		try {
			const player_stats = await this.prisma.player_stats.findMany({
				select: {
					value: true,
					Game_stats: {
						select: {
							name: true,
							slug: true
						}
					}
				},
				where: {
					player_id,
					Game_stats: {
						game_id
					}
				}
			});

			return player_stats;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async deleteStatementById(id: number): Promise<any> {
		try {
			const delete_account = await this.prisma.account_statement.delete({
				where: {
					id
				}
			});
			return delete_account;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async createToken({
		code,
		expires_date,
		player_id,
		token
	}: ICreatePlayerTokenDTO): Promise<void> {
		try {
			await this.prisma.player_token.create({
				data: {
					code,
					expires_date,
					token,
					Player: {
						connect: {
							id: player_id
						}
					}
				}
			});
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async deleteTokenById(token_id: number): Promise<void> {
		try {
			await this.prisma.player_token.delete({
				where: {
					id: token_id
				}
			});
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async updateWallet(player_id: number, type: 'income' | 'outcome', amount: number): Promise<any> {
		try {
			const { total } = await this.prisma.wallet.findFirst({
				where: { player_id }
			});

			const new_total = type === 'income' ? total + amount : total - amount;
			console.log(`O saldo do player nº ${player_id} foi de: ${total} para: ${new_total}`);

			const wallet = await this.prisma.wallet.update({
				where: { player_id },
				data: {
					total: new_total
				}
			});

			return wallet;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async saveStatement(
		title: string,
		description: string,
		quantity: number,
		type: 'income' | 'outcome' | 'pending' | 'canceled',
		player_id: number,
		session_id?: number,
		buyer_id?: number,
		payment_info_id?: number
	): Promise<void> {
		try {
			if (!session_id) {
				await this.prisma.account_statement.create({
					data: {
						title,
						description,
						quantity,
						type,
						Wallet: {
							connect: {
								player_id
							}
						}
					}
				});

				return;
			}

			if (!buyer_id && !payment_info_id) {
				await this.prisma.account_statement.create({
					data: {
						title,
						description,
						quantity,
						type,
						Wallet: {
							connect: {
								player_id
							}
						},
						Session: {
							connect: {
								id: session_id
							}
						}
					}
				});

				return;
			}

			if (!buyer_id) {
				await this.prisma.account_statement.create({
					data: {
						title,
						description,
						quantity,
						type,
						Wallet: {
							connect: {
								player_id
							}
						},
						Session: {
							connect: {
								id: session_id
							}
						},
						Payment_info: {
							connect: {
								id: payment_info_id
							}
						}
					}
				});

				return;
			}

			if (buyer_id) {
				await this.prisma.account_statement.create({
					data: {
						title,
						description,
						quantity,
						type,
						Wallet: {
							connect: {
								player_id
							}
						},
						Session: {
							connect: {
								id: session_id
							}
						},
						Player: {
							connect: {
								id: buyer_id
							}
						}
					}
				});
			}
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findStatementByPlayer(player_id: number, orderBy: string): Promise<TStatement[]> {
		try {
			const statement = await this.prisma.account_statement.findMany({
				where: {
					Wallet: {
						Player: {
							id: player_id
						}
					}
				},
				orderBy: {
					[orderBy]: 'desc'
				},
				include: {
					Session: true,
					Payment_info: true,
					Withdraw_request: {
						select: {
							id: true,
							status: true,
							retry_id: true
						}
					}
				}
			});

			return statement;
		} catch (error) {
			console.log(error);
			throw new AppError('Internal server error', 500);
		}
	}

	async findLastStatementByPlayer(player_id: number): Promise<TStatement> {
		try {
			const statement = await this.prisma.account_statement.findMany({
				where: {
					Wallet: {
						Player: {
							id: player_id
						}
					}
				},
				orderBy: {
					createdAt: 'desc'
				},
				include: {
					Session: true,
					Payment_info: true
				}
			});
			console.log(statement);

			return statement[0];
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findStatementByPaymentInfoId(payment_id: number): Promise<any> {
		try {
			const statement = await this.prisma.account_statement.findFirst({
				where: {
					payment_info_id: payment_id
				},
				include: {
					Session: true
				}
			});

			return statement;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findPendingStatement(owner_id: number, buyer_id: number, session_id: number): Promise<any> {
		try {
			const statement = await this.prisma.account_statement.findFirst({
				where: {
					buyer_id,
					session_id,
					type: 'pending',
					Wallet: {
						Player: {
							id: owner_id
						}
					}
				}
			});
			return statement;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findAllPendingStatements(player_id: number): Promise<Account_statement[]> {
		try {
			const statement = await this.prisma.account_statement.findMany({
				where: {
					type: 'pending',
					Wallet: {
						Player: {
							id: player_id
						}
					}
				}
			});
			return statement;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}
	async findAllSaqueStatements(player_id: number): Promise<Account_statement[]> {
		try {
			const statement = await this.prisma.account_statement.findMany({
				where: {
					type: 'outcome',
					title: 'Saque',
					Wallet: {
						Player: {
							id: player_id
						}
					},
					Withdraw_request: {
						some: {
							status: 'Sucesso'
						}
					}
				},
				include: {
					Withdraw_request: {
						select: {
							status: true
						}
					}
				}
			});
			return statement;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async createFederatedCredentials(
		player_id: number,
		provider: 'apple' | 'google',
		provider_id: string
	): Promise<void> {
		try {
			await this.prisma.federated_credentials.create({
				data: {
					provider_id,
					provider,
					Player: {
						connect: {
							id: player_id
						}
					}
				}
			});
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findByFederatedCredentials(email: string): Promise<Federated_credentials> {
		try {
			return this.prisma.federated_credentials.findFirst({
				where: {
					Player: {
						email
					}
				}
			});
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async addMentoringGame(player_id: number, games_id: number[]): Promise<void> {
		try {
			// eslint-disable-next-line no-restricted-syntax
			for (const game of games_id) {
				// eslint-disable-next-line no-await-in-loop
				await this.prisma.mentoring_games.create({
					data: {
						Player: {
							connect: {
								id: player_id
							}
						},
						Game: {
							connect: {
								id: game
							}
						}
					}
				});
			}
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async saveNotification(data: {
		player_id: number;
		title: string;
		body: string;
		image?: string;
		primaryAction?: string;
		secondaryAction?: string;
		primaryButton?: string;
		secondaryButton?: string;
	}): Promise<void> {
		try {
			const notitifiactions = await this.prisma.notification.create({
				data: {
					title: data.title,
					message: data.body,
					image: data.image,
					primaryAction: data.primaryAction,
					secondaryAction: data.secondaryAction,
					primaryButton: data.primaryButton,
					secondaryButton: data.secondaryButton
				}
			});

			await this.prisma.player_notification.create({
				data: {
					Player: {
						connect: {
							id: data.player_id
						}
					},
					Notification: {
						connect: {
							id: notitifiactions.id
						}
					}
				}
			});
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async listNotifications(player_id: number): Promise<
		(Player_notification & {
			Notification: Notification;
		})[]
	> {
		try {
			const notifications = await this.prisma.player_notification.findMany({
				where: {
					Player: {
						id: player_id,
						deletedAt: null
					}
				},
				include: {
					Notification: true
				}
			});

			return notifications;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async updateNotification(
		player_id: number,
		notification_id: number,
		isRead: boolean,
		isClicked: boolean
	): Promise<any> {
		try {
			await this.prisma.player_notification.update({
				where: {
					id: notification_id
				},
				data: {
					isRead,
					isClicked
				}
			});
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async findCustomer(player_id: number): Promise<Payment_info> {
		try {
			const customer = await this.prisma.payment_info.findFirst({
				where: { player_id }
			});

			return customer;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async listStatements(): Promise<
		(Account_statement & {
			Session: Session & {
				Game: Game;
			};
			Wallet: Wallet & {
				Player: Player;
			};
		})[]
	> {
		try {
			const statements = await this.prisma.account_statement.findMany({
				include: {
					Session: {
						include: {
							Game: true
						}
					},
					Wallet: {
						include: {
							Player: true
						}
					}
				}
			});

			return statements;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async updateMentoringGames(data: { active: boolean; id: number }): Promise<Mentoring_games> {
		try {
			const game = await this.prisma.mentoring_games.update({
				where: {
					id: data.id
				},
				data: {
					active: data.active
				}
			});

			return game;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async createPlayerStatsByGame(data: IPlayerStatsByGame[]) {
		try {
			return this.prisma.player_stats.createMany({
				data
			});
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async updatePlayerStatsByGame({ id, value }: IUpdatePlayerStatsByGame) {
		try {
			return this.prisma.player_stats.update({
				where: {
					id
				},
				data: {
					value
				}
			});
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async listByPlayerIdPlayerStatsByGame(player_id, game_id) {
		try {
			return await this.prisma.player_stats.findMany({
				include: {
					Game_stats: {
						include: {
							Game: true
						}
					}
				},
				where: {
					Player: {
						deletedAt: null
					},
					Game_stats: {
						game_id: game_id || undefined
					},
					player_id
				}
			});
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async deletePlayer(id: number): Promise<any> {
		try {
			await this.prisma.player.update({
				where: {
					id
				},
				data: {
					deletedAt: new Date()
				}
			});
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async isDeletedPlayer(username: string): Promise<any> {
		try {
			return await this.prisma.player.findFirst({
				where: {
					NOT: {
						deletedAt: null
					},
					AND: {
						OR: [
							{
								nickname: username
							},
							{
								email: username
							}
						]
					}
				}
			});
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async activePlayer(id: number): Promise<Player> {
		try {
			const activePlayer = await this.prisma.player.update({
				where: {
					id
				},
				data: {
					deletedAt: null
				}
			});
			return activePlayer;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async getTotalActiveProPlayers(): Promise<number> {
		try {
			const total = await this.prisma.player.count({
				where: {
					isPro: true
				}
			});

			return total;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async getAllPlayersPaginations(numPag: number): Promise<Player[]> {
		try {
			const results = await this.prisma.player.findMany({
				take: numPag,
				where: {
					deletedAt: null
				},
				orderBy: {
					createdAt: 'desc'
				}
			});

			return results;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async getPlayerStatsByPlayerIdGameStatsId(
		player_id: number,
		game_stats_id: number
	): Promise<any> {
		try {
			const result = await this.prisma.player_stats.findFirst({
				where: {
					player_id,
					game_stats_id
				}
			});
			return result;
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}

	async getMyBoughtSessions(id: number): Promise<any> {
		try {
			const player = await this.prisma.player.findFirst({
				select: {
					id: true,
					name: true,
					nickname: true,
					avatar: true,
					sessions: true
				},
				where: {
					id
				}
			});

			if (player) {
				const sessions = await this.prisma.session.findMany({
					include: {
						Game: {
							select: {
								id: true,
								logo: true
							}
						},
						Player: {
							select: {
								nickname: true,
								avatar: true
							}
						},
						players_seats: {
							include: {
								Payment_info: true,
								Player: {
									select: {
										id: true,
										avatar: true,
										nickname: true
									}
								}
							}
						}
					},
					where: {
						players_seats: {
							some: {
								player_id: player.id
							}
						}
					}
				});

				player.sessions = sessions;
			}

			return player;
		} catch (error) {
			console.log(error);

			throw new AppError('Internal server error', 500);
		}
	}

	async listPlayersWithTwitch(): Promise<Player[]> {
		try {
			const players = await this.prisma.player.findMany({
				where: {
					NOT: {
						nickname_twitch: null || ''
					}
				}
			});
			return players;
		} catch (error) {
			console.log(error);
			throw new AppError('Internal server error', 500);
		}
	}

	// async createDevice(data: IDevice): Promise<void> {
	// 	try {
	// 		await this.prisma.device.create({
	// 			data
	// 		});
	// 	} catch (error) {
	// 		throw new AppError('Internal server error', 500);
	// 	}
	// }

	// async updateDevice(data: IDevice): Promise<void> {
	// 	try {
	// 		await this.prisma.device.update({
	// 			where: { id: data.device_id },
	// 			data
	// 		});
	// 	} catch (error) {
	// 		throw new AppError('Internal server error', 500);
	// 	}
	// }

	// async deleteDevice(device_id: number): Promise<void> {
	// 	try {
	// 		await this.prisma.device.delete({
	// 			where: { id: device_id }
	// 		});
	// 	} catch (error) {
	// 		throw new AppError('Internal server error', 500);
	// 	}
	// }

	// async findDeviceByPlayerId(player_id: number): Promise<IDevice[]> {
	// 	try {
	// 		return await this.prisma.device.findMany({
	// 			where: { player_id }
	// 		});
	// 	} catch (error) {
	// 		throw new AppError('Internal server error', 500);
	// 	}
	// }

	// async findDeviceByDeviceId(device_id: number): Promise<IDevice> {
	// 	try {
	// 		return await this.prisma.device.findFirst({
	// 			where: { device_id }
	// 		});
	// 	} catch (error) {
	// 		throw new AppError('Internal server error', 500);
	// 	}
	// }

	async findAllLanguages(): Promise<ILanguage[]> {
		try {
			return await this.prisma.language.findMany();
		} catch (error) {
			throw new AppError('Internal server error', 500);
		}
	}
}
