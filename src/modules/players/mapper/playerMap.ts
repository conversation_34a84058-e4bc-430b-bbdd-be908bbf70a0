import { RRule } from "rrule";

import { fullPlayer, IPlayerDTO } from "../dtos/IPlayerDTO";

const weekdays = [
  "segunda",
  "terca",
  "quarta",
  "quinta",
  "sexta",
  "sabado",
  "domingo",
];

const freq = ["ano", "mes", "semana", "dia"];

export class playerMap {
  public static toDTO(player: fullPlayer): IPlayerDTO {
    if (player.avatar) {
      const avatarUrl =
        process.env.disk === "local"
          ? `${process.env.APP_API_URL}/avatar/${player.avatar}`
          : `${process.env.AWS_BUCKET_URL}/avatar/${player.avatar}`;

      player.avatar = encodeURI(avatarUrl);
    }

    if (player.cover_picture) {
      const coverUrl =
        process.env.disk === "local"
          ? `${process.env.APP_API_URL}/cover/${player.cover_picture}`
          : `${process.env.AWS_BUCKET_URL}/cover/${player.cover_picture}`;

      player.cover_picture = encodeURI(coverUrl);
    }

    const sessions = player.sessions.filter(
      (session) => session.player_id !== player.id
    );

    const quantity_sessions = player.sessions.filter(
      (session) => session.player_id === player.id && session.isActive === false
    ).length;

    const appointments = player.sessions
      .filter(
        (session) =>
          session.player_id === player.id &&
          session.players_seats.length > 0 &&
          session.isActive === true
      )
      .sort((a, b) => +a.start_time - +b.start_time);

    const available_appointments = player.sessions.filter(
      (session) =>
        session.player_id === player.id &&
        session.players_seats.length < session.seats
    );

    return {
      id: player.id,
      name: player.name,
      nickname: player.nickname,
      cpf: player.cpf,
      email: player.email,
      phone: player.phone,
      stream_link: player.stream_link,
      bio: player.bio,
      birthdate: player.birthdate,
      country: player.country,
      cover_picture: player.cover_picture,
      avatar: player.avatar,
      isEmailVerified: player.isEmailVerified,
      nickname_twitch: player.nickname_twitch,
      subscribe_twitch: Number(player.subscribe_twitch),
      total_views_twitch: Number(player.total_views_twitch),
      address: {
        cep: player.cep || "",
        state: player.state || "",
        city: player.city || "",
        district: player.district || "",
        street: player.address || "",
        complement: player.complement || "",
        number: player.number || "",
      },
      quantity_sessions,
      isPro: {
        migration_date: player.ProMigrationDate,
        active: player.isPro,
      },
      badges: player.badges.map((badge) => {
        return {
          id: badge.id,
          name: badge.name,
          icon: encodeURI(`${process.env.AWS_BUCKET_URL}/badges/${badge.icon}`),
          description: badge.description,
        };
      }),
      knowbie_points: player.wallet.total,
      games: player.games.map((game) => {
        return {
          id: game.id,
          name: game.name,
          logo: encodeURI(
            `${process.env.AWS_BUCKET_URL}/games/logo/${game.logo}`
          ),
          banner: encodeURI(
            `${process.env.AWS_BUCKET_URL}/games/banner/${game.banner}`
          ),
          isActive: game.isActive,
          createdAt: game.createdAt,
          updatedAt: game.updatedAt,
        };
      }),
      mentoring_games: player.Mentoring_games?.map((mentoring) => {
        return {
          mentoring_id: mentoring.id,
          id: mentoring.Game.id,
          name: mentoring.Game.name,
          logo: encodeURI(
            `${process.env.AWS_BUCKET_URL}/games/logo/${mentoring.Game.logo}`
          ),
          banner: encodeURI(
            `${process.env.AWS_BUCKET_URL}/games/banner/${mentoring.Game.banner}`
          ),
          active: mentoring.active,
        };
      }),
      avaliations: player.avaliations && {
        average: player.avaliations.average,
        stars: {
          1: player.avaliations.comments.filter(
            (comment) => comment.points === 1
          ).length,
          2: player.avaliations.comments.filter(
            (comment) => comment.points === 2
          ).length,
          3: player.avaliations.comments.filter(
            (comment) => comment.points === 3
          ).length,
          4: player.avaliations.comments.filter(
            (comment) => comment.points === 4
          ).length,
          5: player.avaliations.comments.filter(
            (comment) => comment.points === 5
          ).length,
        },
        tags: player.avaliations.tags,
        comments: player.avaliations.comments
          .filter((comment) => comment.description !== null)
          .map((comment) => {
            return comment.isAnonymous
              ? {
                  id: comment.id,
                  points: comment.points,
                  description: comment.description,
                  date: comment.createdAt,
                }
              : {
                  id: comment.id,
                  nickname: comment.Player.nickname,
                  avatar:
                    comment.Player.avatar &&
                    encodeURI(
                      `${process.env.AWS_BUCKET_URL}/avatar/${comment.Player.avatar}`
                    ),
                  points: comment.points,
                  description: comment.description,
                  date: comment.createdAt,
                };
          })
          .sort((a, b) => +b.date - +a.date),
      },
      sessions: sessions.map((session) => {
        return {
          id: session.id,
          session_base_id: session.session_base_id,
          confirmed: session.players_seats.filter(
            (seats) => seats.Player.id === player.id
          )[0].isConfirmed,
          start_time: session.start_time,
          end_time: session.end_time,
          isActive: session.isActive,
          canceledAt: session.canceledAt,
          price: session.price,
          communication_link: session.communication_link,
          platform_id: session.platform_id,
          input_id: session.input_id,
          seats: session.seats,
          pro_player_id: session.player_id,
          pro_player_avatar:
            session.Player.avatar &&
            encodeURI(
              `${process.env.AWS_BUCKET_URL}/avatar/${session.Player.avatar}`
            ),
          pro_player_nickname: session.Player.nickname,
          game_id: session.Game.id,
          game_logo:
            session.Game.logo &&
            encodeURI(
              `${process.env.AWS_BUCKET_URL}/games/logo/${session.Game.logo}`
            ),
        };
      }),
      appointments: appointments.map((session) => {
        return {
          id: session.id,
          isPromoted:
            session.promoted_session[session.promoted_session.length - 1]
              ?.active ?? false,
          session_base_id: session.session_base_id,
          start_time: session.start_time,
          end_time: session.end_time,
          isActive: session.isActive,
          canceledAt: session.canceledAt,
          communication_link: session.communication_link,
          platform_id: session.platform_id,
          input_id: session.input_id,
          price: session.price,
          confirmations: session.players_seats.reduce((acc, seat) => {
            return seat.isConfirmed ? acc + 1 : acc;
          }, 0),
          seats: session.seats,
          pro_player_id: session.player_id,
          game_id: session.Game.id,
          game_logo:
            session.Game.logo &&
            encodeURI(
              `${process.env.AWS_BUCKET_URL}/games/logo/${session.Game.logo}`
            ),
          players: session.players_seats
            .map((player) => {
              return {
                id: player.Player.id,
                confirmed: player.isConfirmed,
                nickname: player.Player.nickname,
                avatar:
                  player.Player.avatar &&
                  encodeURI(
                    `${process.env.AWS_BUCKET_URL}/avatar/${player.Player.avatar}`
                  ),
              };
            })
            .sort((a, b) => +b.confirmed - +a.confirmed),
        };
      }),
      available_appointments: available_appointments.map((session) => {
        const repeat =
          player.recurring_sessions &&
          player.recurring_sessions
            .map((recurring) => {
              const rule = RRule.fromText(recurring.pattern);
              return (
                (recurring.session_id === session.id ||
                  recurring.session_id === session.session_base_id) && {
                  freq: freq[rule.options.freq],
                  interval: rule.options.interval,
                  byweekday: rule.options.byweekday
                    ? rule.options.byweekday.map((day) => weekdays[day])
                    : [],
                  until: rule.options.until,
                  count: rule.options.count,
                }
              );
            })
            .filter((recurring) => recurring);

        return {
          id: session.id,
          isPromoted:
            session.promoted_session[session.promoted_session.length - 1]
              ?.active ?? false,
          session_base_id: session.session_base_id,
          start_time: session.start_time,
          end_time: session.end_time,
          isActive: session.isActive,
          canceledAt: session.canceledAt,
          price: session.price,
          seats: session.seats,
          pro_player_id: session.player_id,
          game_id: session.Game.id,
          game_logo:
            session.Game.logo &&
            encodeURI(
              `${process.env.AWS_BUCKET_URL}/games/logo/${session.Game.logo}`
            ),
          repeat: repeat ? repeat[0] : null,
        };
      }),
    };
  }
}
