export interface IUpdatePlayerDTO {
  id: number;
  bio?: string;
  isEmailVerified?: boolean;
  nickname?: string;
  email?: string;
  stream_link?: string;
  avatar?: string;
  cover?: string;
  games?: number[];
  mentoring_games?: number[];
  communication_link?: string;
  password?: string;
  name?: string;
  cpf?: string;
  birth_date?: Date;
  phone?: string;
  isPro?: boolean;
  push_token?: string;
  address?: {
    cep?: string;
    state?: string;
    city?: string;
    district?: string;
    street?: string;
    complement?: string;
    number?: string;
  };
  nickname_twitch?: string;
  subscribe_twitch?: number;
  total_views_twitch?: number;
}
