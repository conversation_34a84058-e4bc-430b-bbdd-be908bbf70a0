import {
  Account_statement,
  Badge,
  Game,
  Player,
  Rating,
  Session,
  Wallet,
  Recurring_sessions,
  Mentoring_games,
  Payment_info,
} from ".prisma/client";

export type TStatement = Account_statement & {
  Session: Session;
  Payment_info: Payment_info;
};

export type TPlayerStats = {
  value: number;
  Game_stats: {
    name: string;
    slug: string;
  };
};

export type IPlayerStats = {
  id: number;
  value: number;
  player_id: number;
  game_stats_id: number;
};

export type TRating = Rating & {
  Player: {
    nickname: string;
    avatar: string;
  };
};

export type TTag = { name: string; quantity: number };

type TSessionDTO = Omit<
  Session,
  "player_id" | "game_id" | "createdAt" | "updatedAt" | "deletedAt"
> & {
  pro_player_id: number;
  pro_player_nickname: string;
  pro_player_avatar: string;
  game_logo: string;
};

type TAppointment = Omit<
  Session,
  | "player_id"
  | "game_id"
  | "createdAt"
  | "updatedAt"
  | "deletedAt"
  | "communication_link"
  | "platform_id"
  | "input_id"
> & {
  pro_player_id: number;
  game_logo: string;
  game_id: number;
  confirmations?: number;
  repeat?: {
    freq: string;
    interval: number;
    byweekday?: string[] | null;
    until?: Date | null;
    count?: number | null;
  };
  players?: {
    id: number;
    confirmed: boolean;
    nickname: string;
    avatar: string;
  }[];
};

type TSession = Session & {
  promoted_session: {
    active: boolean;
  }[];
  Player: {
    nickname: string;
    avatar: string;
  };
  Game: {
    logo: string;
    id: number;
  };
  players_seats: {
    Payment_info?: Payment_info[];
    Player: {
      id: number;
      avatar: string;
      nickname: string;
    };
    isConfirmed: boolean;
  }[];
};

export type fullPlayer = Player & {
  badges: Badge[];
  wallet: Wallet;
  games: Game[];
  sessions: TSession[];
  Mentoring_games: (Mentoring_games & {
    Game: Game;
  })[];
  recurring_sessions?: Recurring_sessions[];
  avaliations?: {
    average: number;
    tags?: TTag[];
    comments: TRating[];
  };
};

export interface IPlayerDTO {
  id: number;
  name: string;
  nickname: string;
  email: string;
  cpf: string;
  phone: string;
  bio: string;
  stream_link: string;
  birthdate: Date;
  country: string;
  cover_picture: string;
  avatar: string;
  isEmailVerified: boolean;
  nickname_twitch: string;
  subscribe_twitch: number;
  total_views_twitch: number;
  address?: {
    cep?: string;
    state?: string;
    city?: string;
    district?: string;
    street?: string;
    complement?: string;
    number?: string;
  };
  quantity_sessions?: number;
  isPro: {
    migration_date: Date;
    active: boolean;
  };
  badges: Badge[];
  knowbie_points: number;
  games: Game[];
  mentoring_games: {
    mentoring_id: number;
    id: number;
    name: string;
    logo: string;
    banner: string;
    active: boolean;
  }[];
  sessions: TSessionDTO[];
  appointments: TAppointment[];
  available_appointments: TAppointment[];
  avaliations?: {
    average: number;
    tags: TTag[];
    stars: {
      1: number;
      2: number;
      3: number;
      4: number;
      5: number;
    };
    comments: {
      id: number | string;
      date: Date;
      nickname?: string;
      avatar?: string;
      points: number;
      description: string;
    }[];
  };
}
