import {
	Wallet,
	Player_token,
	Federated_credentials,
	Payment_info,
	Withdraw_request,
	Rating,
	Notification,
	Player_notification,
	Account_statement,
	Player,
	Game,
	Session,
	Mentoring_games
} from '.prisma/client';

import { ICreatePlayerDTO } from '../dtos/ICreatePlayerDTO';
import { ICreatePlayerTokenDTO } from '../dtos/ICreatePlayerTokenDTO';
import { IPlayerStatsByGame } from '../dtos/IPlayerStatsByGame';
import { fullPlayer, TPlayerStats, TRating, TStatement, TTag } from '../dtos/IPlayerDTO';
import { IUpdatePlayerDTO } from '../dtos/IUpdatePlayerDTO';
import { IUpdatePlayerStatsByGame } from '../dtos/IUpdatePlayerStatsByGame';
import { Promoted_player } from '@prisma/client';
import { ILanguage } from '../dtos/ILanguageDTO';

export interface IPlayerRepository {
	create(data: ICreatePlayerDTO): Promise<any>;
	createPlayerStatsByGame(data: IPlayerStatsByGame[]): Promise<any>;
	updatePlayerStatsByGame(data: IUpdatePlayerStatsByGame): Promise<IPlayerStatsByGame>;
	listByPlayerIdPlayerStatsByGame(
		player_id: number,
		game_id?: number
	): Promise<IPlayerStatsByGame[]>;
	deletePlayer(id: number): Promise<any>;
	activePlayer(id: number): Promise<Player>;
	isDeletedPlayer(username: string): Promise<any>;
	findAvaliations(id: number): Promise<TRating[]>;
	findAvaliationsByGame(
		player_id: number,
		game_id: number
	): Promise<
		(Rating & {
			Player: {
				nickname: string;
				avatar: string;
			};
		})[]
	>;
	findPromotedPlayerByPlayerId(player_id: number): Promise<Promoted_player>;
	promotePlayer(data: {
		player_id: number;
		until: Date;
		days_to_expire: number;
		price: number;
		reach_max?: number;
		reach_min?: number;
		payment_type: string;
		price_per_day: number;
		payment_info_id?: number;
	}): Promise<Promoted_player>;
	listPromotePlayers(): Promise<Promoted_player[]>;
	listAllPromotePlayers(): Promise<Promoted_player[]>;
	finishPromotePlayer(id: number): Promise<Promoted_player>;
	confirmPromotePlayer(id: number): Promise<Promoted_player>;
	findTags(id: number): Promise<TTag[]>;
	findPlayerStatsByGame(player_id: number, game_id: number): Promise<TPlayerStats[]>;
	listAll(): Promise<fullPlayer[]>;
	rankingPlayersByViews(): Promise<Player[]>;
	rankingPlayersBySubscribes(): Promise<Player[]>;
	findById(id: number): Promise<fullPlayer>;
	findByEmail(email: string): Promise<fullPlayer>;
	findByCpf(cpf: string): Promise<fullPlayer>;
	findByNickname(nickname: string): Promise<fullPlayer>;
	updateById(data: IUpdatePlayerDTO): Promise<fullPlayer>;
	findByToken(token: string): Promise<Player_token>;
	findTokenByCode(code: string): Promise<Player_token>;
	createToken(data: ICreatePlayerTokenDTO): Promise<void>;
	deleteTokenById(token_id: number): Promise<void>;
	updateWallet(player_id: number, type: 'income' | 'outcome', amount: number): Promise<any>;
	saveStatement(
		title: string,
		description: string,
		quantity: number,
		type: 'income' | 'outcome' | 'pending' | 'canceled',
		player_id: number,
		session_id?: number,
		buyer_id?: number,
		payment_info_id?: number
	): Promise<void>;
	deleteStatementById(id: number): Promise<any>;
	findStatementByPlayer(player_id: number, orderBy: string): Promise<TStatement[]>;
	findLastStatementByPlayer(player_id: number): Promise<TStatement>;
	findStatementByPaymentInfoId(payment_id: number): Promise<any>;
	findPendingStatement(owner_id: number, buyer_id: number, session_id: number): Promise<any>;
	findAllPendingStatements(player_id: number): Promise<Account_statement[]>;
	findAllSaqueStatements(player_id: number): Promise<Account_statement[]>;
	createFederatedCredentials(
		player_id: number,
		provider: 'apple' | 'google',
		provider_id: string
	): Promise<void>;
	findByFederatedCredentials(email: string): Promise<Federated_credentials>;
	addMentoringGame(player_id: number, games_id: number[]): Promise<void>;
	listNotifications(player_id: number): Promise<
		(Player_notification & {
			Notification: Notification;
		})[]
	>;
	updateNotification(
		player_id: number,
		notification_id: number,
		isRead: boolean,
		isClicked: boolean
	): Promise<any>;
	saveNotification(data: {
		player_id: number;
		title: string;
		body: string;
		image?: string;
		primaryAction?: string;
		secondaryAction?: string;
		primaryButton?: string;
		secondaryButton?: string;
	}): Promise<void>;
	findCustomer(player_id: number): Promise<Payment_info>;
	listStatements(): Promise<
		(Account_statement & {
			Session: Session & {
				Game: Game;
			};
			Wallet: Wallet & {
				Player: Player;
			};
		})[]
	>;
	updateMentoringGames(data: { id: number; active: boolean }): Promise<Mentoring_games>;
	getTotalActiveProPlayers(): Promise<number>;
	getAllPlayersPaginations(numPag: number): Promise<Player[]>;
	getPlayerStatsByPlayerIdGameStatsId(player_id: number, game_stats_id: number): Promise<any>;
	getMyBoughtSessions(player_id: number): Promise<any>;
	listPlayersWithTwitch(): Promise<Player[]>;
	findAllLanguages(): Promise<ILanguage[]>;
}
