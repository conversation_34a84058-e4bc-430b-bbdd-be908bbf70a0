import { Request, Response } from "express";
import { container } from "tsyringe";

import { ChangePasswordUseCase } from "./ChangePasswordUseCase";

export class ChangePasswordController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { password, new_password } = request.body;

    const changePasswordUseCase = container.resolve(ChangePasswordUseCase);

    const result = await changePasswordUseCase.execute({
      player_id: Number(id),
      password,
      new_password,
    });

    return response.json(result);
  }
}
