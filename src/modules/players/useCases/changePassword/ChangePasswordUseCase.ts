import { compare, hash } from "bcrypt";
import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IMailProvider } from "@shared/container/providers/MailProvider/IMailProvider";
import { AppError } from "@shared/errors/AppError";
import { KingHostProvider } from "@shared/container/providers/MailProvider/implementations/KingHostProvider";
import { Player } from "@prisma/client";

interface IRequest {
  player_id: number;
  password: string;
  new_password: string;
}

@injectable()
export class ChangePasswordUseCase {
  constructor(
    @inject("MailProvider") private mailProvider: IMailProvider,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ new_password, password, player_id }: IRequest): Promise<any> {
    const player = await this.playerRepository.findById(player_id);

    const passwordMatched = await compare(password, player.password);

    if (!passwordMatched) {
      throw new AppError("Password invalid!");
    }

    const hashedPassword = await hash(new_password, 10);

    await this.playerRepository.updateById({
      password: hashedPassword,
      id: player_id,
    });

    const html = `
    <html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
      html, body {
        height: 100%;
        width: 100%;
        margin: 0;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .centered-div {
          width: 70%; /* ajuste conforme necessário */
          height: 400px; /* ajuste conforme necessário */
          background-color: #f0f0f0;
          text-align: center;
          padding: 20px;
          border-radius: 10px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }
      .alert {
        font-size: 12px;
      }
    </style>
    </head>
    <body>
    
    <br>
    
    <div class="centered-div">
    <h3>Olá, ${player.name || player.nickname},</h3>

      <p>Gostaríamos de informar que a alteração de senha em sua conta foi realizada com sucesso. Agora você pode acessar sua conta com a nova senha configurada.
    Por favor, lembre-se de manter sua senha em segurança e não compartilhá-la com terceiros. 
    Agradecemos pela sua atenção e confiança em nossos serviços.</p>

    <div>Atenciosamente,</div>
    <p>Equipe Knowbie - WTF</p> 
    <p></p>

    <p class="alert">Este é um e-mail automático. Por favor, não o responda.</p>

    <p class="alert">Quer falar conosco? Entre em contato através do e-mail:
    <EMAIL></p>
    </div>
    
    </body>
    </html>`;

    const emailProvider = new KingHostProvider();
    await emailProvider.sendMail(
      player.email,
      `Alteração de Senha Concluída com Sucesso`,
      {},
      this.emailModelChangePasswordHtml(player)
    );

    return { message: "Password changed successfully!" };
  }

  private emailModelChangePasswordHtml(player: Player): string {
    return `
    <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          html, body {
            height: 100%;
            width: 100%;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .centered-div {
              width: 60%; /* ajuste conforme necessário */
              height: 400px; /* ajuste conforme necessário */
              background-color: #f0f0f0;
              text-align: center;
              padding: 20px;
              border-radius: 10px;
              box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
          }
          .alert {
            font-size: 12px;
          }
        </style>
      </head>
      <body>
        <div class="centered-div">
          <h3>Olá, ${player.name || player.nickname},</h3>

          <p>
            Gostaríamos de informar que a alteração de senha em sua conta foi realizada com sucesso. Agora você pode acessar sua conta com a nova senha configurada.
            Por favor, lembre-se de manter sua senha em segurança e não compartilhá-la com terceiros. 
            Agradecemos pela sua atenção e confiança em nossos serviços.
          </p>

          <div>Atenciosamente,</div>
          <p>Equipe Knowbie - WTF</p> 

          <p class="alert">Este é um e-mail automático. Por favor, não o responda.</p>

          <p class="alert">
            Quer falar conosco? Entre em contato através do e-mail:
            <EMAIL>
          </p>
        </div>
      </body>
    </html>`;
  }
}
