import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";

@injectable()
export class GetTotalActiveProPlayerUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(game_id: number): Promise<number> {
    if(game_id == 0)
      return await this.playerRepository.getTotalActiveProPlayers();
    
    let total = 0;
    const players = await this.playerRepository.listAll();
    
    const proPlayer = players.filter((player) => player.isPro);
    
    if (proPlayer.length === 0)
    return total;
    
    proPlayer.forEach((proPlayer) =>{
      for (let i = 0; i < proPlayer.sessions.length; i++) {
        if(proPlayer.sessions[i].Game.id == game_id){
          total++;
          break;
        }
      }
    })
    return total;
  }
}
