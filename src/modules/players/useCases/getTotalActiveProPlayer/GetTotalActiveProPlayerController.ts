import { Request, Response } from "express";
import { container } from "tsyringe";
import { GetTotalActiveProPlayerUseCase } from "./GetTotalActiveProPlayerUseCase";

export class GetTotalActiveProPlayerController {
  async handle(request: Request, response: Response): Promise<Response> {

    const { game_id } = request.query;
    
    const totalProPlayerUseCase = container.resolve(GetTotalActiveProPlayerUseCase);
    
    const players = await totalProPlayerUseCase.execute(Number(game_id));

    return response.json(players);
  }
}
