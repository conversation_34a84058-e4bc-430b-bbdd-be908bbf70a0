import { Request, Response } from "express";
import { container } from "tsyringe";

import { RequestWithdrawUseCase } from "./RequestWithdrawUseCase";

export class RequestWithdrawController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { amount, pix_key } = request.body;

    const requestWithdrawUseCase = container.resolve(RequestWithdrawUseCase);

    const withdraw = await requestWithdrawUseCase.execute({
      player_id: Number(id),
      amount,
      pix_key,
    });

    return response.status(200).json(withdraw);
  }
}
