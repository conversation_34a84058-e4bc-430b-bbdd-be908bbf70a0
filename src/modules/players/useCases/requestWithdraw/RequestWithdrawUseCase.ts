import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import {} from "@shared/container/providers/PaymentGatewayProvider/IMercadoPagoProvider";
import { ITributeRepository } from "@modules/tributes/repositories/ITributeRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { CreateWithdrawDTO } from "@shared/container/providers/WithdrawGatewayProvider/DTO/IWithdrawDTO";
import { IWithdrawGatewayProvider } from "@shared/container/providers/WithdrawGatewayProvider/IWithdrawGatewayProvider";
import { IWithdrawGatewayRepository } from "@shared/container/providers/WithdrawGatewayProvider/repositories/IWithdrawGatewayRepository";
import { AppError } from "@shared/errors/AppError";
import { getNextFriday } from "@utils/getNextFriday";
import { Tribute } from "@prisma/client";

interface IRequest {
  player_id: number;
  amount: number;
  pix_key: string;
}

@injectable()
export class RequestWithdrawUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("TributeRepository")
    private tributeRepository: ITributeRepository,
    @inject("WithdrawGatewayProvider")
    private withdrawGatewayProvider: IWithdrawGatewayProvider,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("WithdrawGatewayRepository")
    private withdrawGatewayRepository: IWithdrawGatewayRepository
  ) {}

  async executeMock(
    { player_id, amount, pix_key }: IRequest,
    paymentDay?: Date,
    referentTo?: number,
    tribute?: Array
  ): Promise<any> {
    const authPix = ["+*************"];
    if (!authPix.includes(pix_key)) throw new AppError("Unauthorized Pix!");

    if (typeof amount !== "number")
      throw new AppError("Amount is not a valid number!");

    if (Number(amount) <= 0)
      throw new AppError("Amount must be greater than zero!");

    const allTribute = await this.tributeRepository.listAllDistinctTribute();

    const fee = allTribute.filter(
      (item) => item.description === "withdraw pix fee"
    );

    if (fee.length < 1) {
      throw new AppError(
        "Problem with tribute, please notify the Administrator!",
        501
      );
    }

    const liquidAmount = (Number(amount.toFixed(2)) - fee[0].value).toFixed(2);

    const player = await this.playerRepository.findById(player_id);

    const saqueStatements = await this.playerRepository.findAllSaqueStatements(
      player_id
    );
    let processFunds = 0;
    saqueStatements.forEach((statement) => {
      if (statement.Withdraw_request[0]?.status === "Sucesso")
        processFunds += statement.quantity;
    });
    const walletTotal = (player.wallet.total - processFunds).toFixed(2);

    if (Number(walletTotal) < amount) {
      throw new AppError("Not enough money to withdraw");
    }

    if (!paymentDay) paymentDay = getNextFriday();

    const withdrawRequest: CreateWithdrawDTO = {
      valor_pagamento: liquidAmount.toString(),
      tipo_pagamento: "PIX",
      data_pagamento: this.dateProvider.dateFormat(paymentDay),
      chave: pix_key,
      pagador: {
        tipo_conta: process.env.PAGADORA_TIPO_CONT,
        conta: process.env.PAGADORA_CONTA,
        agencia: process.env.PAGADORA_AGENCIA,
        tipo_pessoa: process.env.PAGADORA_TIPO_PESSOA,
        documento: process.env.PAGADORA_DOCUMENTO,
      },
    };

    await this.withdrawGatewayRepository.create(
      {
        title: "Saque",
        description: "Saque",
        type: "outcome",
        status: "Sucesso",
        amount,
        liquidAmount: "1",
        payment_external_code: "6ef970fa-5d92-469c-8e74-3d34fed577cc",
        payment_type: "pix",
        player_id,
        pix_key: "+*************",
        receiver_name: "DANIEL CARBALLO MARTINEZ",
        bank_account_type: "Conta Pagamento",
        bank_account_number: "********",
        bank_account_agency: "NU PAGAMENTOS - IP",
        paymentDay: new Date(paymentDay),
        launch_number: "86655",
        batch_number: "*********",
        payer: "",
        receiver: "",
        json_request: JSON.stringify(withdrawRequest),
        json_response: "",
        referentTo,
      },
      [{ id: fee[0].id }]
    );

    return "";
  }

  async execute(
    { player_id, amount, pix_key }: IRequest,
    paymentDay?: Date,
    referentTo?: number,
    tribute?: Array<Tribute>
  ): Promise<any> {
    const authPix = ["+*************"];
    if (!authPix.includes(pix_key)) throw new AppError("Unauthorized Pix!");

    if (typeof amount !== "number")
      throw new AppError("Amount is not a valid number!");

    if (Number(amount) <= 0)
      throw new AppError("Amount must be greater than zero!");

    let liquidAmount = amount.toFixed(2);

    if (!referentTo) {
      console.log("taxa");

      const allTribute = await this.tributeRepository.listAllDistinctTribute();

      var fee = allTribute.filter(
        (item) => item.description === "withdraw pix fee"
      );

      if (fee.length < 1) {
        throw new AppError(
          "Problem with tribute, please notify the Administrator!",
          501
        );
      }

      liquidAmount = (Number(amount.toFixed(2)) - fee[0].value).toFixed(2);

      const player = await this.playerRepository.findById(player_id);

      const saqueStatements =
        await this.playerRepository.findAllSaqueStatements(player_id);
      let processFunds = 0.0;
      saqueStatements.forEach((statement) => {
        if (statement.Withdraw_request[0]?.status === "Sucesso")
          processFunds += statement.quantity;
      });
      const walletTotal = (player.wallet.total - processFunds).toFixed(2);

      if (Number(walletTotal) < amount) {
        throw new AppError("Not enough money to withdraw");
      }
    }

    if (!paymentDay) paymentDay = getNextFriday();

    const withdrawRequest: CreateWithdrawDTO = {
      valor_pagamento: liquidAmount.toString(),
      tipo_pagamento: "PIX",
      data_pagamento: this.dateProvider.dateFormat(paymentDay),
      chave: pix_key,
      pagador: {
        tipo_conta: process.env.PAGADORA_TIPO_CONT,
        conta: process.env.PAGADORA_CONTA,
        agencia: process.env.PAGADORA_AGENCIA,
        tipo_pessoa: process.env.PAGADORA_TIPO_PESSOA,
        documento: process.env.PAGADORA_DOCUMENTO,
      },
    };

    const withdrawResponse = await this.withdrawGatewayProvider.createWithdraw(
      withdrawRequest
    );

    if (withdrawResponse) {
      await this.withdrawGatewayRepository.create(
        {
          title: "Saque",
          description: "Saque",
          type: "outcome",
          status: withdrawResponse.data.status_pagamento,
          amount: referentTo ? amount + tribute[0].value : amount,
          liquidAmount: withdrawResponse.data.valor_pagamento,
          payment_external_code: withdrawResponse.data.cod_pagamento,
          payment_type: withdrawResponse.data.tipo_pagamento,
          player_id,
          pix_key: withdrawResponse.data.recebedor.identificacao_chave,
          receiver_name: withdrawResponse.data.recebedor.nome,
          bank_account_type: withdrawResponse.data.recebedor.tipo_conta,
          bank_account_number: withdrawResponse.data.recebedor.conta,
          bank_account_agency: withdrawResponse.data.recebedor.agencia,
          paymentDay: new Date(withdrawResponse.data.data_pagamento),
          launch_number: withdrawResponse.data.numero_lancamento,
          batch_number: withdrawResponse.data.numero_lote,
          payer: JSON.stringify(withdrawResponse.data.pagador),
          receiver: JSON.stringify(withdrawResponse.data.recebedor),
          json_request: JSON.stringify(withdrawRequest),
          json_response: JSON.stringify(withdrawResponse.data),
          referentTo,
        },
        [{ id: referentTo ? tribute[0].id : fee[0]?.id }]
      );
    } else throw new AppError("Error while requesting withdraw");

    return withdrawResponse.data;
  }
}
