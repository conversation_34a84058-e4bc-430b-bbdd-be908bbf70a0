import { inject, injectable } from "tsyringe";

import { IPlayerDTO } from "@modules/players/dtos/IPlayerDTO";
import { playerMap } from "@modules/players/mapper/playerMap";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  token: string;
}

@injectable()
export class ConfirmEmailUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ token }: IRequest): Promise<IPlayerDTO> {
    const playerToken = await this.playerRepository.findByToken(token);

    if (!playerToken) {
      throw new AppError("Invalid token");
    }

    const isTokenExpired = this.dateProvider.compareIfBefore(
      playerToken.expires_date,
      new Date()
    );

    if (isTokenExpired) {
      throw new AppError("Token expired");
    }

    const player = await this.playerRepository.findById(playerToken.player_id);

    const updatedPlayer = await this.playerRepository.updateById({
      id: player.id,
      isEmailVerified: true,
    });

    await this.playerRepository.deleteTokenById(playerToken.id);

    return playerMap.toDTO(updatedPlayer);
  }
}
