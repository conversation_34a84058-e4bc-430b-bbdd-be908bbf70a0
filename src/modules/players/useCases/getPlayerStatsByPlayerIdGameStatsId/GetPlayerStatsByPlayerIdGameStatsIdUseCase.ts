import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { Player } from "@prisma/client";

@injectable()
export class GetPlayerStatsByPlayerIdGameStatsIdUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(player_id: number, game_stats_id: number): Promise<Player[]> {
    const players =
      await this.playerRepository.getPlayerStatsByPlayerIdGameStatsId(
        player_id,
        game_stats_id
      );

    return players;
  }
}
