import { Request, Response } from "express";
import { container } from "tsyringe";
import { GetPlayerStatsByPlayerIdGameStatsIdUseCase } from "./GetPlayerStatsByPlayerIdGameStatsIdUseCase";

export class GetPlayerStatsByPlayerIdGameStatsIdController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { player_id, game_stats_id } = request.body;

    const getPlayerStatsByPlayerIdGameStatsIdUseCase = container.resolve(
      GetPlayerStatsByPlayerIdGameStatsIdUseCase
    );

    const players = await getPlayerStatsByPlayerIdGameStatsIdUseCase.execute(
      player_id,
      game_stats_id
    );

    return response.json(players);
  }
}
