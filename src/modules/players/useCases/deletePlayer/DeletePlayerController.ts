import { Request, Response } from "express";
import { container } from "tsyringe";

import { DeletePlayerUseCase } from "./DeletePlayerUseCase";

export class DeletePlayerController {
  async handle(request: Request, response: Response): Promise<Response> {   
    const { password } = request.body 
    
    const { id } = request.params;    

    const deletePlayerUseCase = container.resolve(DeletePlayerUseCase);
    
    const player = await deletePlayerUseCase.execute(Number(id), password);

    return response.json(player);
  }
}
