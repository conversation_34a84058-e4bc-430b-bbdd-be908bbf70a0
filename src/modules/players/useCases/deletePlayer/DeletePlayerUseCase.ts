import { compare } from "bcrypt";
import { inject, injectable } from "tsyringe";

import { IJobsRepository } from "@modules/jobs/repositories/IJobsRepository";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class DeletePlayerUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("JobsRepository") private jobRepository: IJobsRepository
  ) {}

  async execute(id: number, password: string): Promise<number | AppError> {
    const player = await this.playerRepository.findById(id);

    const passwordMatched = await compare(password, player.password);

    if (!passwordMatched) throw new AppError("Password invalid!", 401);

    const seatsBuyed = await this.sessionRepository.findSeatsByPlayer(id);

    for (let i = 0; i < seatsBuyed.length; i++) {
      const seatOnFutureSessions = await this.jobRepository.findbySeat(
        seatsBuyed[i].id
      );

      if (seatOnFutureSessions && seatOnFutureSessions) {
        throw new AppError("Player has buyed seats on future sessions");
      }
    }

    const saqueStatements = await this.playerRepository.findAllSaqueStatements(
      id
    );

    let processFunds = 0;
    let pendingFunds = 0;
    saqueStatements.forEach((statement) => {
      if (statement.Withdraw_request[0]?.status === "Sucesso")
        processFunds += statement.quantity;
    });

    if (processFunds > 0) {
      throw new AppError("Can't delete, player has money in Process!", 403);
    }
    const pendingStatements =
      await this.playerRepository.findAllPendingStatements(id);

    pendingStatements.forEach((statement) => {
      pendingFunds += statement.quantity;
    });

    if (pendingFunds > 0) {
      throw new AppError("Can't delete, player has money in Pending!", 403);
    }

    if (player.wallet && player.wallet.total > 0) {
      throw new AppError(
        "Can't delete, player has money available to withdraw in your wallet!",
        403
      );
    }

    if ((await this.sessionRepository.findSeatsByPlayer(id)).length > 0) {
      throw new AppError("Can't delete, player has seats available!", 403);
    }

    const sessionScheduled =
      await this.sessionRepository.findSessionsScheduledbyPlayerId(id);
    try {
      if (sessionScheduled.length > 0) {
        for (let i = 0; i < sessionScheduled.length; i += 1) {
          if (sessionScheduled[i].players_seats.length > 0)
            throw new AppError(
              "Can't delete, player has scheduled session, need canceled first!",
              406
            );
        }
        for (const element of sessionScheduled) {
          await this.sessionRepository.deleteSession(element.id);
        }
      }
      return await this.playerRepository.deletePlayer(id);
    } catch (error) {
      console.log(error);

      throw error;
    }
  }
}
