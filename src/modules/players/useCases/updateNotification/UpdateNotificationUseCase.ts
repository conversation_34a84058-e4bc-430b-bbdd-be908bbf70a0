import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  player_id: number;
  notification_id: number;
  isRead: boolean;
  isClicked: boolean;
}

@injectable()
export class UpdateNotificationUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({
    player_id,
    notification_id,
    isRead,
    isClicked,
  }: IRequest): Promise<void> {
    const player = await this.playerRepository.findById(player_id);

    if (!player) {
      throw new AppError("Player not found");
    }

    await this.playerRepository.updateNotification(
      player.id,
      notification_id,
      isRead,
      isClicked
    );
  }
}
