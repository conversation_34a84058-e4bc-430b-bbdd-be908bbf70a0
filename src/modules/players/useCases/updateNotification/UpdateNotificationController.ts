import { Request, Response } from "express";
import { container } from "tsyringe";

import { UpdateNotificationUseCase } from "./UpdateNotificationUseCase";

export class UpdateNotificationController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { notification_id, isRead, isClicked } = request.body;

    const updateNotificationUseCase = container.resolve(
      UpdateNotificationUseCase
    );

    await updateNotificationUseCase.execute({
      player_id: Number(id),
      notification_id: Number(notification_id),
      isRead,
      isClicked,
    });

    return response.send();
  }
}
