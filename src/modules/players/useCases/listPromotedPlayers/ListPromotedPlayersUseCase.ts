import "dayjs/locale/pt-br";
import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";



@injectable()
export class ListPromotedPlayersUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
  ) {}

  async execute(): Promise<any> {
    const promotedPlayers = await this.playerRepository.listPromotePlayers();

    const playersWithRatingAndStars = promotedPlayers.map(async (player: any) => {
      const avaliations = await this.playerRepository.findAvaliations(player.player_id);
      const Tags = await this.playerRepository.findTags(player.player_id);

      let average = 5;

      if (avaliations.length !== 0) {
        average =
          avaliations.reduce((acc, curr) => {
            return acc + curr.points;
          }, 5) /
          (avaliations.length + 1);
      }
      const formattedTags = Tags.map((tag) => ({
        name: tag.name,
        quantity: Number(tag.quantity),
      }));

      const comments = avaliations.map(avaliation => ({
        player_id: avaliation.player_id,
        name: avaliation.Player,
        description: avaliation.description
      }));

      return {
        id: player.id,
        player_id: player.player_id,
        Player: {
          id: player.Player.id,
          name: player.Player.name,
          nickname: player.Player.nickname,
          avatar: player.Player.avatar,
        },
        Avaliations: {
          average: Number(average.toFixed(1)),
          tags: formattedTags,
          comments: comments,
        }
      };
    });
  
    return Promise.all(playersWithRatingAndStars);
  }
}
