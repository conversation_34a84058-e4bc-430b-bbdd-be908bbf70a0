import { Request, Response } from "express";
import { container } from "tsyringe";
import { ListPromotedPlayersUseCase } from "./ListPromotedPlayersUseCase";

export class ListPromotedPlayersController {
  async handle(request: Request, response: Response): Promise<Response> {
    const listPromotedPlayersUseCase = container.resolve(ListPromotedPlayersUseCase);

    const promoted_players = await listPromotedPlayersUseCase.execute();

    return response.json(promoted_players);
  }
}
