import { injectable, inject } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  token: string;
  email: string;
}

@injectable()
export class VerifyTokenPasswordUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ token, email }: IRequest): Promise<any> {
    const playerToken = await this.playerRepository.findByToken(token);    
    if (!playerToken) {
      throw new AppError("Token inválido");
    }
    const player = await this.playerRepository.findById(playerToken.player_id);
    const playerEmail = player.email
    if(email != playerEmail){
      throw new AppError("Token Invalido")
    }
    
    return;
  }
}
