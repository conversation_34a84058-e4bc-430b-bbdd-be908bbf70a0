import { Request, Response } from 'express';
import { container } from 'tsyringe';
import { VerifyTokenPasswordUseCase } from '@modules/players/useCases/verifyTokenPassword/VerifyTokenPasswordUseCase';

export class VerifyTokenPasswordController {

  async handle(request: Request, response: Response): Promise<Response> {
    try {
      const { token, email } = request.body;
      
      const verifyTokenPasswordUseCase = container.resolve(VerifyTokenPasswordUseCase);

      await verifyTokenPasswordUseCase.execute({ token, email });

      return response.json({ message: "Token válido" });;
    } catch (error) {
      return response.status(400).json({ message: error });
    }
  }
}
