import { Request, Response } from "express";
import { container } from "tsyringe";

import { UpdatePlayerStatsByGameUseCase } from "./UpdatePlayerStatsByGameUseCase";

export class UpdatePlayerStatsByGameController {
  async handle(request: Request, response: Response): Promise<Response> {
    const listPlayerStatsByGame = request.body;
    const updatePlayerStatsByGameUseCase = container.resolve(
      UpdatePlayerStatsByGameUseCase
    );

    const player_stats = await updatePlayerStatsByGameUseCase.execute(
      listPlayerStatsByGame
    );

    return response.json(player_stats);
  }
}
