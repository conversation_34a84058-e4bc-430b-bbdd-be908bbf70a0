import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";

import { IUpdatePlayerStatsByGame } from "@modules/players/dtos/IUpdatePlayerStatsByGame";
import { IPlayerStatsByGame } from "@modules/players/dtos/IPlayerStatsByGame";

@injectable()
export class UpdatePlayerStatsByGameUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(
    listPlayerStatsByGame: IUpdatePlayerStatsByGame[]
  ): Promise<IPlayerStatsByGame[]> {
    let response = [];
    for (const element of listPlayerStatsByGame) {
      const { id, value } = element;
      response.push(
        await this.playerRepository.updatePlayerStatsByGame({
          id,
          value: Number(value),
        })
      );
    }
    return response;
  }
}
