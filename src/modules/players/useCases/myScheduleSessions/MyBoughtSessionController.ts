import { Request, Response } from "express";
import { container } from "tsyringe";

import { MyBoughtSessionUseCase } from "./MyBoughtSessionUseCase";

export class MyBoughtSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.params;

    const listPlayerUseCase = container.resolve(MyBoughtSessionUseCase);

    const player = await listPlayerUseCase.execute(Number(id));

    return response.json(player);
  }
}
