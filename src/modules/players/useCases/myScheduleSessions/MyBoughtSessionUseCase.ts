import { inject, injectable } from "tsyringe";

import { fullPlayer } from "@modules/players/dtos/IPlayerDTO";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class MyBoughtSessionUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("SessionRepository") private SessionRepository: ISessionRepository
  ) {}

  async execute(id: number): Promise<fullPlayer> {
    const player = await this.playerRepository.getMyBoughtSessions(id);

    if (!player) {
      throw new AppError("Player not found");
    }

    // const avaliations = await this.playerRepository.findAvaliations(player.id);

    // let average = 5;

    // if (avaliations.length !== 0) {
    //   average =
    //     avaliations.reduce((acc, curr) => {
    //       return acc + curr.points;
    //     }, 5) /
    //     (avaliations.length + 1);
    // }

    // player.avaliations = {
    //   average: Number(average.toFixed(1)),
    //   tags: await this.playerRepository.findTags(player.id),
    //   comments: avaliations,
    // };

    const recurring_sessions =
      await this.SessionRepository.listRecurringSessions();

    player.recurring_sessions = recurring_sessions;

    return player;
  }
}
