import { Request, Response } from "express";
import { container } from "tsyringe";

import { PlayerWalletUseCase } from "./PlayerWalletUseCase";

export class PlayerWalletController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { page, limit } = request.query;
    const orderBy = request.query.orderBy || "date";

    const playerWalletUseCase = container.resolve(PlayerWalletUseCase);

    const wallet = await playerWalletUseCase.execute({
      player_id: id,
      page: Number(page),
      limit: Number(limit),
      orderBy: String(orderBy),
    });

    return response.json(wallet);
  }
}
