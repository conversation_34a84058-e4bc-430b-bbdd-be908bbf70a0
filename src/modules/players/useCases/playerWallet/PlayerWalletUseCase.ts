import { injectable, inject } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  player_id: number | string;
  limit: number;
  page: number;
  orderBy?: string;
}

interface IResponse {
  balance: number;
  statements: {
    id: number;
    type: string;
    amount: number;
    date: string;
    description: string;
    session_id?: number;
    buyer_id?: number;
  }[];
}

@injectable()
export class PlayerWalletUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ player_id, page, limit, orderBy }: IRequest): Promise<any> {
    const player = await this.playerRepository.findById(Number(player_id));
    if (!player) {
      throw new AppError("Player not found");
    }
    const fieldMapping = {
      date: "createdAt",
      amount: "quantity",
    };

    const allowedFields = ["date", "amount", "type"];

    if (!allowedFields.includes(orderBy)) {
      throw new AppError("Invalid 'orderBy' field");
    }
    const validOrderByField = fieldMapping[orderBy] || orderBy;

    const statements = await this.playerRepository.findStatementByPlayer(
      Number(player_id),
      String(validOrderByField)
    );

    let processFunds = 0;
    statements.forEach((statement) => {
      if (statement.Withdraw_request[0]?.status === "Sucesso")
        processFunds += statement.quantity;
    });

    if (player.isPro) {
      const statementsWithPagination = statements
        .filter(
          (statement) =>
            statement.type !== "pending" &&
            (statement.Withdraw_request.length === 0 ||
              statement.Withdraw_request.some((withdraw) => {
                return withdraw.retry_id == null;
              }))
        )
        .map((statement) => ({
          id: statement.id,
          title: statement.title,
          description: statement.description,
          type: statement.type,
          amount: statement.quantity,
          date: statement.createdAt,
          session_id: statement.session_id || null,
          buyer_id: statement.buyer_id || null,
          Withdraw_request: statement.Withdraw_request,
        }))
        .slice((page - 1) * limit, page * limit);

      return {
        balance: Number(player.wallet.total - processFunds).toFixed(2),
        statements: statementsWithPagination,
      };
    }

    const statementsWithPagination = statements
      .map((statement) => ({
        id: statement.id,
        title: statement.title,
        description: statement.description,
        type: statement.type,
        amount: statement.quantity,
        date: statement.createdAt,
        session_id: statement.session_id || null,
      }))
      .slice((page - 1) * limit, page * limit);

    return {
      balance: (player.wallet.total - processFunds).toFixed(2),
      statements: statementsWithPagination,
    };
  }
}
