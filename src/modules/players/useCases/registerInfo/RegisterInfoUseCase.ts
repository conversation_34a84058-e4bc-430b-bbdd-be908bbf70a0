import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";

@injectable()
export class RegisterInfoUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(): Promise<any> {
    const months = [
      "Janeiro",
      "Fevereiro",
      "Março",
      "Abril",
      "Maio",
      "Junho",
      "Julho",
      "Agosto",
      "Setembro",
      "Outubro",
      "Novembro",
      "Dezembro",
    ];
    const players = await this.playerRepository.listAll();

    const registersByDay = players.reduce((accumulator, player) => {
      const registerDate = player.createdAt.toISOString().split("T")[0];

      if (!accumulator[registerDate]) {
        accumulator[registerDate] = {
          quantity: 0,
        };
      }

      accumulator[registerDate].quantity += 1;

      return accumulator;
    }, {});

    const registersByDayArray = Object.keys(registersByDay).map((key) => {
      return {
        date: key,
        quantity: registersByDay[key].quantity,
      };
    });

    const registersByMonth = players.reduce((accumulator, player) => {
      const registerDate = player.createdAt.toISOString().split("T")[0];

      const registerMonth = Number(registerDate.split("-")[1]);

      if (!accumulator[months[registerMonth - 1]]) {
        accumulator[months[registerMonth - 1]] = {
          quantity: 0,
        };
      }

      accumulator[months[registerMonth - 1]].quantity += 1;

      return accumulator;
    }, {});

    const registersByMonthArray = Object.keys(registersByMonth).map((key) => {
      return {
        month: key,
        quantity: registersByMonth[key].quantity,
      };
    });

    return {
      registersByDay: registersByDayArray,
      registersByMonth: registersByMonthArray,
    };
  }
}
