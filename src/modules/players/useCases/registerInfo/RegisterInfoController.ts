import { Request, Response } from "express";
import { container } from "tsyringe";

import { RegisterInfoUseCase } from "./RegisterInfoUseCase";

export class RegisterInfoController {
  async handle(request: Request, response: Response): Promise<Response> {
    const registerInfoUseCase = container.resolve(RegisterInfoUseCase);

    const infos = await registerInfoUseCase.execute();

    return response.json(infos);
  }
}
