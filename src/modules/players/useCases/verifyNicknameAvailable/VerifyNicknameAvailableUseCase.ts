import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  nickname: string;
  player_id: number;
}

@injectable()
export class VerifyNicknameAvailableUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ nickname, player_id }: IRequest): Promise<string> {
    const player = await this.playerRepository.findByNickname(nickname);

    if (player && player.id !== player_id) {
      throw new AppError("Nickname not available");
    }

    return "Nickname available";
  }
}
