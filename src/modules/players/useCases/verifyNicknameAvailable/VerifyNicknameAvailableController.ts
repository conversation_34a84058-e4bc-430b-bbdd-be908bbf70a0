import { Request, Response } from "express";
import { container } from "tsyringe";

import { VerifyNicknameAvailableUseCase } from "./VerifyNicknameAvailableUseCase";

export class VerifyNicknameAvailableController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { nickname } = request.params;

    const verifyNicknameAvailableUseCase = container.resolve(
      VerifyNicknameAvailableUseCase
    );

    const result = await verifyNicknameAvailableUseCase.execute({
      nickname,
      player_id: Number(id),
    });

    return response.json({ message: result });
  }
}
