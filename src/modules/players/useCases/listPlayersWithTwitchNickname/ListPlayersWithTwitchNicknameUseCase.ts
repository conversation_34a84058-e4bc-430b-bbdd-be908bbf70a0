import { IPlatformRepository } from "@modules/platforms/repositories/IPlatformRepository";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { Player } from "@prisma/client";
import { ITwitchDevelopers } from "@shared/container/providers/TwitchDevelopers/ITwitchDevelopers";
import { container, inject, injectable } from "tsyringe";

@injectable()
export class ListPlayersWithTwitchNicknameUseCase {
  constructor(
    @inject("TwitchDevelopers") private twitchDevelopers: ITwitchDevelopers, // private twitchDevelopers: ITwitchDevelopers
    @inject("PlatformRepository") private playerRepository: IPlayerRepository // private twitchDevelopers: ITwitchDevelopers
  ) {}

  async execute(): Promise<Player[]> {
    const playersData = await this.playerRepository.listPlayersWithTwitch();
    return playersData;
  }
}
