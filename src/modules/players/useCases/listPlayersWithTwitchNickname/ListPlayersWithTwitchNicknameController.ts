import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListPlayersWithTwitchNicknameUseCase } from "./ListPlayersWithTwitchNicknameUseCase";

export class ListPlayersWithTwitchNicknameController {
  async handle(request: Request, response: Response): Promise<Response> {
    const listPlayersWithTwitchNicknameUseCase = container.resolve(
      ListPlayersWithTwitchNicknameUseCase
    );

    const playersData = await listPlayersWithTwitchNicknameUseCase.execute();

    return response.json(playersData);
  }
}
