import { Request, Response } from "express";
import { container } from "tsyringe";

import { VerifyEmailAvailableUseCase } from "./VerifyEmailAvailableUseCase";

export class VerifyEmailAvailableController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { email } = request.params;

    const verifyEmailAvailableUseCase = container.resolve(
      VerifyEmailAvailableUseCase
    );

    const result = await verifyEmailAvailableUseCase.execute(email);

    return response.json({ message: result });
  }
}
