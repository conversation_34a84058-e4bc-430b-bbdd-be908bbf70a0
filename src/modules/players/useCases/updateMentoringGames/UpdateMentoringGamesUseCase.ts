import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";

interface IRequest {
  game_id: number;
  player_id: number;
  is_mentoring: boolean;
}

@injectable()
export class UpdateMentoringGamesUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ game_id, player_id, is_mentoring }: IRequest): Promise<any> {
    const player = await this.playerRepository.findById(player_id);

    const mentoringGames = player.Mentoring_games.filter((mentoring) => {
      return game_id === mentoring.game_id;
    });

    const updatedMentoringGames = [];
    mentoringGames.forEach((mentoring) => {
      updatedMentoringGames.push(
        this.playerRepository.updateMentoringGames({
          id: mentoring.id,
          active: is_mentoring,
        })
      );
    });

    return Promise.all(updatedMentoringGames);
  }
}
