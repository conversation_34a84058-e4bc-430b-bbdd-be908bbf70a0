import { Request, Response } from "express";
import { container } from "tsyringe";

import { UpdateMentoringGamesUseCase } from "./UpdateMentoringGamesUseCase";

export class UpdateMentoringGamesController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { game_id, is_mentoring } = request.body;
    const { id } = request.user;

    const updateMentoringGamesUseCase = container.resolve(
      UpdateMentoringGamesUseCase
    );

    const mentoring_games = await updateMentoringGamesUseCase.execute({
      player_id: Number(id),
      game_id: Number(game_id),
      is_mentoring,
    });

    return response.json(mentoring_games);
  }
}
