import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";

@injectable()
export class ListNotificationsUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(player_id: number): Promise<any> {
    const notifications = await this.playerRepository.listNotifications(
      player_id
    );

    return notifications.sort((a, b) => +b.createdAt - +a.createdAt);
  }
}
