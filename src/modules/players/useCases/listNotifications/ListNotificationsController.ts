import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListNotificationsUseCase } from "./ListNotificationsUseCase";

export class ListNotificationsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;

    const listNotificationsUseCase = container.resolve(
      ListNotificationsUseCase
    );

    const notifications = await listNotificationsUseCase.execute(Number(id));

    return response.json(notifications);
  }
}
