import { Request, Response } from "express";
import { container } from "tsyringe";

import { CreatePlayerStatsByGameUseCase } from "./CreatePlayerStatsByGameUseCase";

export class CreatePlayerStatsByGameController {
  async handle(request: Request, response: Response): Promise<Response> {    
    const listPlayerStatsByGame= request.body;    

    const createPlayerStatsByGameUseCase = container.resolve(CreatePlayerStatsByGameUseCase);
    
    const player_stats = await createPlayerStatsByGameUseCase.execute(listPlayerStatsByGame);

    return response.json(player_stats);
  }
}
