import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";

import { IPlayerStatsByGame } from "@modules/players/dtos/IPlayerStatsByGame";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class CreatePlayerStatsByGameUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(listPlayerStatsByGame: IPlayerStatsByGame[]): Promise<any> {
    for (let i = 0; i < listPlayerStatsByGame.length; i++) {
      let { player_id, game_stats_id } = listPlayerStatsByGame[i];
      let result =
        await this.playerRepository.getPlayerStatsByPlayerIdGameStatsId(
          player_id,
          game_stats_id
        );

      if (result) {
        throw new AppError("Player Stats already exists!");
      }
    }

    return await this.playerRepository.createPlayerStatsByGame(
      listPlayerStatsByGame
    );
  }
}
