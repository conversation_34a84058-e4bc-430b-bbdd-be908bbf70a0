import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListPendingswithdrawUseCase } from "./ListPendingswithdrawUseCase";

export class ListPendingswithdrawController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { status } = request.params;
    const listWithdrawsUseCase = container.resolve(ListPendingswithdrawUseCase);
    const withdraws = await listWithdrawsUseCase.execute(status);

    return response.json(withdraws);
  }
}
