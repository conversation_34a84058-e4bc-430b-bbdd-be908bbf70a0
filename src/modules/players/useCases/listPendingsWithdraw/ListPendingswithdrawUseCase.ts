import { inject, injectable } from "tsyringe";

import {} from "@shared/container/providers/PaymentGatewayProvider/IMercadoPagoProvider";
import { Withdraw_request } from "@prisma/client";
import { IWithdrawGatewayRepository } from "@shared/container/providers/WithdrawGatewayProvider/repositories/IWithdrawGatewayRepository";

@injectable()
export class ListPendingswithdrawUseCase {
  constructor(
    @inject("WithdrawGatewayRepository")
    private withdrawGatewayRepository: IWithdrawGatewayRepository
  ) {}

  async execute(status: string): Promise<Withdraw_request[]> {
    return this.withdrawGatewayRepository.getWithdrawByStatus(status);
  }
}
