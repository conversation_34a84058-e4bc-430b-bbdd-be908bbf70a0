import { Request, Response } from "express";
import { container } from "tsyringe";
import { PromotedMostValuePlayerUseCase } from "./PromotedMostValuePlayerUseCase";

export class PromotedMostValuePlayerController {
  async handle(request: Request, response: Response): Promise<Response> {
    const promotedMostValuePlayerUseCase = container.resolve(PromotedMostValuePlayerUseCase);

    const promoted_players = await promotedMostValuePlayerUseCase.execute();

    return response.json(promoted_players);
  }
}
