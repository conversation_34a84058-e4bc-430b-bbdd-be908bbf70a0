import "dayjs/locale/pt-br";
import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";

@injectable()
export class PromotedMostValuePlayerUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
  ) {}

  async execute(): Promise<any> {
    const promotedPlayers = await this.playerRepository.listPromotePlayers();

    if (promotedPlayers.length === 0) {
      return [];
    }

    const maxPrice = Math.max(...promotedPlayers.map(player => player.price_per_day));
    
    const mostExpensivePlayers = promotedPlayers.filter(player => player.price_per_day === maxPrice);
  
    return mostExpensivePlayers;
  }
}
