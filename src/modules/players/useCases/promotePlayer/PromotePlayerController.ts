import { Request, Response } from "express";
import { container } from "tsyringe";

import { PromotePlayerUseCase } from "./PromotePlayerUseCase";

export class PromotePlayerController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { price, days, reach_max, reach_min, payment_type, payment_from_wallet  } = request.body;

    const promotePlayerUseCase = container.resolve(PromotePlayerUseCase);

    const promoted_player = await promotePlayerUseCase.execute({
      days,
      price,
      reach_max,
      reach_min,
      player_id: Number(id),
      payment_type,
      payment_from_wallet
    });

    return response.json(promoted_player);
  }
}
