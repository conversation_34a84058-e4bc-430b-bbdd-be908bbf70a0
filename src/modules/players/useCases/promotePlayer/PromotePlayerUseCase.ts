import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";
import { IWebhookRepository } from "@modules/webhook/repositories/IWebhookRepository";
import { IMercadoPagoProvider } from "@shared/container/providers/PaymentGatewayProvider/IMercadoPagoProvider";

interface IRequest {
  player_id: number;
  price: number;
  days: number;
  reach_max?: number;
  reach_min?: number;
  payment_type?: "now";
  payment_from_wallet: number;
}

@injectable()
export class PromotePlayerUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("MercadoPagoProvider") private mercadoPagoProvider: IMercadoPagoProvider,
    @inject("WebhookRepository") private webhookRepository: IWebhookRepository
  ) {}

  async execute({
    player_id,
    price,
    days,
    reach_max = 800,
    reach_min = 400,
    payment_type = "now",
    payment_from_wallet,
  }: IRequest): Promise<any> {
    const isPlayerPromoted =
      await this.playerRepository.findPromotedPlayerByPlayerId(player_id);

    if (isPlayerPromoted && isPlayerPromoted.active) {
      throw new AppError("Player already promoted");
    }
    
    const player = await this.playerRepository.findById(player_id);
    const until = this.dateProvider.addDays(days);
    const price_per_day = price / days;
    
    if (payment_from_wallet>= price){
      if (player.wallet.total < price) {
        throw new AppError("Knowbie point is not enough");
      }
      
      await this.playerRepository.saveStatement(
        "promoção de perfil",
        "perfil promovido",
        price,
        "outcome",
        player_id
      );
      
      await this.playerRepository.updateWallet(player_id, "outcome", price);
      
      const promotedPlayerFromWallet = await this.playerRepository.promotePlayer({
        player_id,
        price,
        until,
        days_to_expire: days,
        reach_max,
        reach_min,
        payment_type,
        price_per_day,
      });
      return promotedPlayerFromWallet;
    }
    if(payment_from_wallet<price){
      if (player.wallet.total < payment_from_wallet) {
        throw new AppError("Knowbie point is not enough");
      }
      await this.playerRepository.updateWallet(player_id, "outcome", payment_from_wallet);
      const payment_pending = price - payment_from_wallet
      const date_of_expiration = new Date();
      date_of_expiration.setMinutes(date_of_expiration.getMinutes() + 20);
        const preferenceData = {
          items: [
            {
              title: `Perfil de ${player.nickname}`,
              description: `Promoção de perfil de ${player.nickname}`,
              picture_url: player.avatar || null,
              category_id: "Services",
              quantity: 1,
              currency_id: "BRL",
              unit_price: payment_pending,
            },
          ],
          payer: {
            identification: {
              type: player.cpf ? "CPF" : null,
              number: player.cpf || null,
            },
          },
          payment_methods: {
            excluded_payment_types: [
              {
                id: "ticket,atm,debit_card",
              },
            ],
          },
          back_urls: {
            success: "knowbie://success",
            failure: "knowbie://failure",
            pending: "knowbie://pending",
          },
          auto_return: "approved",
          external_reference: "",
          date_of_expiration,
          notification_url: `${process.env.APP_URL_BASE}/webhook/mercadopago`,
        };

        let payment_info = await this.webhookRepository.create({
          player_id,
          payment_from_external: payment_pending,
          service_type: "2"
        });
        preferenceData.external_reference = String(payment_info.id);

        const mercadoPagoResponse = await this.mercadoPagoProvider.createPreference(
          preferenceData
        );

        await this.webhookRepository.update(payment_info.id, {
          payment_url: mercadoPagoResponse?.data?.init_point,
        });

        await this.playerRepository.saveStatement(
          "promoção de perfil",
          "perfil promovido",
          price,
          "outcome",
          player_id
        );

        const promotedPlayerNow = await this.playerRepository.promotePlayer({
          player_id,
          price,
          until,
          days_to_expire: days,
          reach_max,
          reach_min,
          payment_type,
          price_per_day,
          payment_info_id: payment_info.id
        });
        return {
          ...promotedPlayerNow,
          payment_url: mercadoPagoResponse?.data?.init_point, // Link de pagamento
        };
    }
    // switch (payment_type) {
    //   case "from_wallet":
    //     if (player.wallet.total < price) {
    //       throw new AppError("Knowbie point is not enough");
    //     }

    //     await this.playerRepository.saveStatement(
    //       "promoção de perfil",
    //       "perfil promovido",
    //       price,
    //       "outcome",
    //       player_id
    //     );

    //     await this.playerRepository.updateWallet(player_id, "outcome", price);

    //     const promotedPlayerFromWallet = await this.playerRepository.promotePlayer({
    //       player_id,
    //       price,
    //       until,
    //       days_to_expire: days,
    //       reach_max,
    //       reach_min,
    //       payment_type,
    //       price_per_day,
    //     });
    //     return promotedPlayerFromWallet;

    //   case "now":
    //     const date_of_expiration = new Date();
    //     date_of_expiration.setMinutes(date_of_expiration.getMinutes() + 20);

    //     const preferenceData = {
    //       items: [
    //         {
    //           title: `Perfil de ${player.nickname}`,
    //           description: `Promoção de perfil de ${player.nickname}`,
    //           picture_url: player.avatar || null,
    //           category_id: "Services",
    //           quantity: 1,
    //           currency_id: "BRL",
    //           unit_price: price,
    //         },
    //       ],
    //       payer: {
    //         identification: {
    //           type: player.cpf ? "CPF" : null,
    //           number: player.cpf || null,
    //         },
    //       },
    //       payment_methods: {
    //         excluded_payment_types: [
    //           {
    //             id: "ticket,atm,debit_card",
    //           },
    //         ],
    //       },
    //       back_urls: {
    //         success: "knowbie://success",
    //         failure: "knowbie://failure",
    //         pending: "knowbie://pending",
    //       },
    //       auto_return: "approved",
    //       external_reference: "",
    //       date_of_expiration,
    //       notification_url: `${process.env.APP_URL_BASE}/webhook/mercadopago`,
    //     };

    //     let payment_info = await this.webhookRepository.create({
    //       player_id,
    //       payment_from_external: price,
    //     });
    //     preferenceData.external_reference = String(payment_info.id);

    //     const mercadoPagoResponse = await this.mercadoPagoProvider.createPreference(
    //       preferenceData
    //     );

    //     await this.webhookRepository.update(payment_info.id, {
    //       payment_url: mercadoPagoResponse?.data?.init_point,
    //     });

    //     await this.playerRepository.saveStatement(
    //       "promoção de perfil",
    //       "perfil promovido",
    //       price,
    //       "outcome",
    //       player_id
    //     );

    //     const promotedPlayerNow = await this.playerRepository.promotePlayer({
    //       player_id,
    //       price,
    //       until,
    //       days_to_expire: days,
    //       reach_max,
    //       reach_min,
    //       payment_type,
    //       price_per_day,
    //     });
    //     return promotedPlayerNow;

    //   case "after_purchase":
    //     price += price * 0.1;

    //     const promotedPlayerAfterPurchase = await this.playerRepository.promotePlayer({
    //       player_id,
    //       price,
    //       until,
    //       days_to_expire: days,
    //       reach_max,
    //       reach_min,
    //       payment_type,
    //       price_per_day,
    //     });
    //     return promotedPlayerAfterPurchase;

    //   default:
    //     throw new AppError("Invalid payment type");
    // }
  }
}
