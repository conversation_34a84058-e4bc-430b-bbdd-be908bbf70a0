import { Request, Response } from "express";
import { container } from "tsyringe";
import { GetAllPlayersPaginationsUseCase } from "./GetAllPlayersPaginationsUseCase";

export class GetAllPlayersPaginationsController {
  async handle(request: Request, response: Response): Promise<Response> {

    const { num_pag } = request.query;
    
    const getAllPlayersPaginationsUseCase = container.resolve(GetAllPlayersPaginationsUseCase);
    
    const players = await getAllPlayersPaginationsUseCase.execute(Number(num_pag));

    return response.json(players);
  }
}
