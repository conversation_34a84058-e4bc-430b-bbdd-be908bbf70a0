import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { Player } from "@prisma/client";


@injectable()
export class GetAllPlayersPaginationsUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(num_pag: number): Promise<Player[] > {
    
    const players = await this.playerRepository.getAllPlayersPaginations(num_pag);

    return players;
  }
}
