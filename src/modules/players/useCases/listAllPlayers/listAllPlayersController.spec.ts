import request from "supertest";

import { app } from "@shared/infra/http/app";

import { PrismaClient } from ".prisma/client";

const prisma = new PrismaClient();

describe("List all players Controller", () => {
  beforeAll(async () => {
    await request(app).post("/players/create").send({
      email: "<EMAIL>",
      password: "123456789",
    });

    await request(app).post("/players/create").send({
      email: "<EMAIL>",
      password: "123456789",
    });
  });

  afterAll(async () => {
    const wallet = prisma.wallet.deleteMany();
    const token = prisma.player_token.deleteMany();
    const game = prisma.game.deleteMany();
    const player = prisma.player.deleteMany();

    await prisma.$transaction([wallet, token, game, player]);
    await prisma.$disconnect();
  });
  it("should be able to list all players", async () => {
    const response = await request(app).get("/players").send();

    expect(response.status).toBe(200);
    // expect(response.body).toHaveLength(2);
  });
});
