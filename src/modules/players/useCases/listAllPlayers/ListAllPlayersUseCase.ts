import { inject, injectable } from "tsyringe";

import { IPlayerDTO } from "@modules/players/dtos/IPlayerDTO";
import { playerMap } from "@modules/players/mapper/playerMap";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";

@injectable()
export class ListAllPlayersUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(): Promise<IPlayerDTO[]> {
    const players = await this.playerRepository.listAll();

    const playersDTO = await Promise.all(
      players.map(async (player) => {
        const avaliations = await this.playerRepository.findAvaliations(
          player.id
        );

        let average = 5;

        if (avaliations.length > 0) {
          average =
            avaliations.reduce((acc, curr) => {
              return acc + curr.points;
            }, 5) /
            (avaliations.length + 1);
        }

        player.avaliations = {
          average: Number(average.toFixed(1)),
          comments: avaliations,
        };

        return playerMap.toDTO(player);
      })
    );

    return playersDTO;
  }
}
