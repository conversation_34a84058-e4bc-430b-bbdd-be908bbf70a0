import { Request, Response } from "express";
import { container } from "tsyringe";

import { RankingPlayerbyViewsUseCase } from "./RankingPlayerByViewsUseCase";

export class RankingPlayerbyViewsController {
  async handle(request: Request, response: Response): Promise<Response> {
    
    const rankingPlayerbyViewsUseCase = container.resolve(
      RankingPlayerbyViewsUseCase
    );
    const rankingByViews = await rankingPlayerbyViewsUseCase.execute();

    return response.json(rankingByViews);
  }
}
