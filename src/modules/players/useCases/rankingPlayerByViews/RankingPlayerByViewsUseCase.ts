import { inject, injectable, container } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { PlayerRepository } from "@modules/players/infra/prisma/repositories/PlayerRepository";
import { map } from "lodash";

container.registerSingleton('PlayerRepository', PlayerRepository);

@injectable()
export class RankingPlayerbyViewsUseCase {
  constructor(@inject("PlayerRepository") private playerRepository: IPlayerRepository) {}

  async execute(): Promise<any> {
    const rankingByViews = await this.playerRepository.rankingPlayersByViews();
    
    // Converter subscribe_twitch e total_views_twitch para números em cada jogador do ranking
    const rankingWithNumberViews = rankingByViews.map((ranking) => {
      return {
        id: ranking.id,
        name: ranking.name,
        link_channel: ranking.stream_link,
        picture: ranking.cover_picture,
        total_views_twitch: Number(ranking.total_views_twitch)
      };
    });


    return rankingWithNumberViews;
  }
}
