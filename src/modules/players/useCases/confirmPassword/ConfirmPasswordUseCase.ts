import { compare } from "bcrypt";
import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class ConfirmPasswordUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(password: string, player_id: number): Promise<string> {
    const player = await this.playerRepository.findById(player_id);

    if (!player) {
      throw new AppError("username don't exist!");
    }
    const passwordMatched = await compare(password, player.password);

    if (player.deletedAt != null) {
      throw new AppError("User is Inactive in Platform!");
    }

    if (!passwordMatched) {
      throw new AppError("password invalid!");
    }

    return "password confirmed!";
  }
}
