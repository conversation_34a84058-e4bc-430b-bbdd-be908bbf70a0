import { Request, Response } from "express";
import { container } from "tsyringe";

import { ConfirmPasswordUseCase } from "./ConfirmPasswordUseCase";

export class ConfirmPasswordController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { password } = request.body;
    const { id } = request.user;

    const confirmPasswordUseCase = container.resolve(ConfirmPasswordUseCase);

    const result = await confirmPasswordUseCase.execute(password, Number(id));

    return response.json(result);
  }
}
