import { Request, Response } from "express";
import { container } from "tsyringe";

import { GetWithdrawByIdUseCase } from "./GetWithdrawByIdUseCase";

export class GetWithdrawByIdController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { withdraw_id } = request.params;

    const getWithdrawByIdUseCase = container.resolve(GetWithdrawByIdUseCase);

    const withdraw = await getWithdrawByIdUseCase.execute(Number(withdraw_id));

    return response.status(200).json(withdraw);
  }
}
