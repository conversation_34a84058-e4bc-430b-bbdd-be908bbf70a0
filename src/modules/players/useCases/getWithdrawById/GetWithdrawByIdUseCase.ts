import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import {} from "@shared/container/providers/PaymentGatewayProvider/IMercadoPagoProvider";
import { ITributeRepository } from "@modules/tributes/repositories/ITributeRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { CreateWithdrawDTO } from "@shared/container/providers/WithdrawGatewayProvider/DTO/IWithdrawDTO";
import { IWithdrawGatewayProvider } from "@shared/container/providers/WithdrawGatewayProvider/IWithdrawGatewayProvider";
import { IWithdrawGatewayRepository } from "@shared/container/providers/WithdrawGatewayProvider/repositories/IWithdrawGatewayRepository";
import { AppError } from "@shared/errors/AppError";
import { getNextFriday } from "@utils/getNextFriday";
import { Json } from "aws-sdk/clients/robomaker";
import { Player, Tribute } from "@prisma/client";
import { forEach } from "lodash";

interface IResponse {
  dados_pix: Json;
  Player: Player;
  Valor_bruto: number;
  Tributes: Tribute;
}

@injectable()
export class GetWithdrawByIdUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("TributeRepository") private tributeRepository: ITributeRepository,
    @inject("WithdrawGatewayRepository")
    private withdrawGatewayRepository: IWithdrawGatewayRepository
  ) {}

  async execute(id: number): Promise<IResponse> {
    let aux = await this.withdrawGatewayRepository.getWithdrawById(id);

    // O resultado desse loop é o último pix do reprocessamento, ele que retorna nessa rota
    do {
      var withdraw = aux;
      aux = await this.withdrawGatewayRepository.getWithdrawByRetryId(
        withdraw.id
      );
    } while (aux && aux.retry_id);

    const dados_pix = JSON.parse(withdraw.json_response);
    let Valor_bruto = parseFloat(dados_pix.valor_pagamento);

    // Caso a taxa um dia seja em porcentagem, terá que adaptar esse cálculo.
    withdraw.Tribute.forEach((element) => {
      Valor_bruto += Number(element.value);
    });

    const response = {
      dados_pix,
      Valor_bruto,
      Player: withdraw.Player,
      Tributes: withdraw.Tribute,
      status: withdraw.status,
    };
    return response;
  }
}
