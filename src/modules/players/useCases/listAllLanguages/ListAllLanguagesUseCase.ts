import { inject, injectable } from 'tsyringe';

import { ILanguage } from '@modules/players/dtos/ILanguageDTO';
import { IPlayerRepository } from '@modules/players/repositories/IPlayerRepository';

@injectable()
export class ListAllLanguagesUseCase {
	constructor(
		@inject('PlayerRepository')
		private playerRepository: IPlayerRepository
	) {}

	async execute(): Promise<ILanguage[]> {
		const languages = await this.playerRepository.findAllLanguages();
		console.log('languages', languages);
		return languages;
	}
}
