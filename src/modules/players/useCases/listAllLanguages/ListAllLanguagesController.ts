import { Request, Response } from 'express';
import { container } from 'tsyringe';

import { ListAllLanguagesUseCase } from './ListAllLanguagesUseCase';

export class ListAllLanguagesController {
	async handle(request: Request, response: Response): Promise<Response> {
		console.log('listAllLanguagesController');
		console.log('request', request);
		console.log('response', response);

		const listAllLanguagesUseCase = container.resolve(ListAllLanguagesUseCase);

		const languages = await listAllLanguagesUseCase.execute();

		return response.json(languages);
	}
}
