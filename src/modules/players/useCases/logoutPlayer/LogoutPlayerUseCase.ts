import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class LogoutPlayerUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(token: string): Promise<void> {
    const tokenToDelete = await this.playerRepository.findByToken(token);

    await this.playerRepository.updateById({
      id: tokenToDelete.player_id,
      push_token: null,
    });

    if (!tokenToDelete) {
      throw new AppError("Invalid Token!");
    }

    await this.playerRepository.deleteTokenById(tokenToDelete.id);
  }
}
