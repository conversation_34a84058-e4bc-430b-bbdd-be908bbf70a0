import { Request, Response } from "express";
import { container } from "tsyringe";

import { LogoutPlayerUseCase } from "./LogoutPlayerUseCase";

export class LogoutPlayerController {
  async handle(request: Request, response: Response): Promise<Response> {
    const authHeader = request.headers.authorization;
    const [, token] = authHeader.split(" ");

    const logoutPlayerUseCase = container.resolve(LogoutPlayerUseCase);

    await logoutPlayerUseCase.execute(String(token));

    return response.status(200).send();
  }
}
