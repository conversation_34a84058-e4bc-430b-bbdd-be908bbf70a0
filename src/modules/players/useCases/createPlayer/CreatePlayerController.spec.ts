import request from "supertest";

import { app } from "@shared/infra/http/app";

import { PrismaClient } from ".prisma/client";

const prisma = new PrismaClient();

describe("Create Player Controller", () => {
  afterEach(async () => {
    const wallet = prisma.wallet.deleteMany();
    const token = prisma.player_token.deleteMany();
    const player = prisma.player.deleteMany();

    await prisma.$transaction([wallet, token, player]);
    await prisma.$disconnect();
  });

  it("should be able to create a new player account", async () => {
    const response = await request(app).post("/players/create").send({
      email: "<EMAIL>",
      password: "*********",
    });

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty("token");
    expect(response.body).toHaveProperty("id");
  });

  it("should not be able to create a new player account with same email", async () => {
    await request(app).post("/players/create").send({
      email: "<EMAIL>",
      password: "*********",
    });

    const response = await request(app).post("/players/create").send({
      email: "<EMAIL>",
      password: "*********",
    });

    expect(response.status).toBe(400);
    expect(response.body).toHaveProperty("message");
    expect(response.body.message).toBe("Email already exists!");
  });

  it("should not be able to create a new player with password length less than 8", async () => {
    const response = await request(app).post("/players/create").send({
      email: "<EMAIL>",
      password: "1234",
    });

    expect(response.status).toBe(400);
    expect(response.body).toHaveProperty("message");
    expect(response.body.message).toBe("Invalid password length!");
  });
});
