import { hash } from "bcrypt";
import { sign } from "jsonwebtoken";
import { shuffle } from "lodash";
import { inject, injectable } from "tsyringe";
import validator from "validator";

import auth from "@config/auth";
import { ICreatePlayerDTO } from "@modules/players/dtos/ICreatePlayerDTO";
import { IPlayerDTO } from "@modules/players/dtos/IPlayerDTO";
import { playerMap } from "@modules/players/mapper/playerMap";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

type IResponse = IPlayerDTO & {
  token: string;
};

@injectable()
export class CreatePlayerUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({
    email,
    password,
    name,
    cpf,
    birthdate,
    isPro,
    provider,
    provider_id,
    push_token,
  }: ICreatePlayerDTO): Promise<IResponse> {
    const maximumNicknameLength = 12;
    const { secret_token, expires_in_token, expires_in_token_days } = auth;
    const emailAlreadyExists = await this.playerRepository.findByEmail(email);
    const cpfAlreadyExists = await this.playerRepository.findByCpf(cpf);

    if (emailAlreadyExists) {
      throw new AppError("Email already exists!");
    }
    if (cpfAlreadyExists) {
      throw new AppError("CPF already exists!");
    }

    if (!validator.isEmail(email)) {
      throw new AppError("Email invalid!");
    }

    let [nickname] = email.split("@");

    if (nickname.length >= maximumNicknameLength) {
      nickname = nickname.substring(0, maximumNicknameLength - 2);
    }

    const existsNickname = await this.playerRepository.findByNickname(nickname);

    if (existsNickname) {
      nickname += Math.floor(Math.random() * 1000);
    }

    let player;
    if (provider === "apple" || provider === "google") {
      player = await this.playerRepository.create({
        email,
        nickname,
        name,
        cpf,
        birthdate,
        isPro,
        push_token,
      });

      await this.playerRepository.createFederatedCredentials(
        player.id,
        provider,
        provider_id
      );
    } else {
      if (!validator.isLength(password, { min: 8 })) {
        throw new AppError("Invalid password length!");
      }

      const hashedPassword = await hash(password, 10);

      const covers = shuffle([
        "Capa_inicial-1.png",
        "Capa_inicial-3.png",
        "Capa_inicial-5.png",
        "Capa_inicial-7.png",
      ]);

      player = await this.playerRepository.create({
        email,
        nickname,
        name,
        cpf,
        birthdate,
        isPro,
        cover: covers[0],
        password: hashedPassword,
        push_token,
      });
    }

    const token = sign({}, secret_token, {
      subject: String(player.id),
      expiresIn: expires_in_token,
    });

    const expires_date = this.dateProvider.addDays(expires_in_token_days);
    await this.playerRepository.createToken({
      expires_date,
      player_id: player.id,
      token,
    });

    return {
      ...playerMap.toDTO(player),
      token,
    };
  }
}
