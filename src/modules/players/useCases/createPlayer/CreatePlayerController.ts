import { Request, Response } from "express";
import { container } from "tsyringe";

import { CreatePlayerUseCase } from "./CreatePlayerUseCase";

export class CreatePlayerController {
  /* #swagger.tags = ['Player'] */
  async handle(request: Request, response: Response): Promise<Response> {
    const {
      email,
      password,
      name,
      cpf,
      birthdate,
      isPro,
      provider,
      provider_id,
      push_token,
    } = request.body;
    const createPlayerUseCase = container.resolve(CreatePlayerUseCase);

    const player = await createPlayerUseCase.execute({
      email,
      password,
      name,
      cpf,
      birthdate,
      isPro,
      provider,
      push_token,
      provider_id,
    });

    return response.json(player);
  }
}
