import { compare } from "bcrypt";
import { sign } from "jsonwebtoken";
import { inject, injectable } from "tsyringe";
import validator from "validator";

import auth from "@config/auth";
import { fullPlayer, IPlayerDTO } from "@modules/players/dtos/IPlayerDTO";
import { playerMap } from "@modules/players/mapper/playerMap";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  username: string;
  password: string;
  provider?: "apple" | "google";
}

type IResponse = IPlayerDTO & {
  token: string;
};

@injectable()
export class AuthenticatePlayerUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("SessionRepository") private SessionRepository: ISessionRepository
  ) {}

  async execute({
    username,
    password,
    provider,
  }: IRequest): Promise<IResponse> {
    const { secret_token, expires_in_token, expires_in_token_days } = auth;

    let player: fullPlayer;

    if (provider === "apple" || provider === "google") {
      const federatedCredentials =
        await this.playerRepository.findByFederatedCredentials(username);

      player = await this.playerRepository.findById(
        federatedCredentials.player_id
      );
    } else {
      if (validator.isEmail(username)) {
        player = await this.playerRepository.findByEmail(username);
      } else {
        player = await this.playerRepository.findByNickname(username);
      }
      if(!player) {
        throw new AppError("username don't exist!");
      }      
      const passwordMatched = await compare(password, player.password);
      
      let isDeletedPlayer = await this.playerRepository.isDeletedPlayer(username);
      
      if (isDeletedPlayer) {
        throw new AppError("User is Inactive in Platform!");
      }
      
      if (!passwordMatched) {
        throw new AppError("username or password invalid!");
      }
    }

    if (!player) {
      throw new AppError("username or password invalid!");
    }

    const token = sign({}, secret_token, {
      subject: String(player.id),
      expiresIn: expires_in_token,
    });

    const expires_date = this.dateProvider.addDays(expires_in_token_days);
    await this.playerRepository.createToken({
      expires_date,
      player_id: player.id,
      token,
    });

    const recurring_sessions =
      await this.SessionRepository.listRecurringSessions();

    player.recurring_sessions = recurring_sessions;

    return {
      ...playerMap.toDTO(player),
      token,
    };
  }
}
