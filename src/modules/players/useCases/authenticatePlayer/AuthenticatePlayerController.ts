import { Request, Response } from "express";
import { container } from "tsyringe";

import { AuthenticatePlayerUseCase } from "./AuthenticatePlayerUseCase";

export class AuthenticatePlayerController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { username, password, provider } = request.body;
    
    const authenticatePlayerUseCase = container.resolve(
      AuthenticatePlayerUseCase
    );

    const result = await authenticatePlayerUseCase.execute({
      username,
      password,
      provider,
    });

    return response.json(result);
  }
}
