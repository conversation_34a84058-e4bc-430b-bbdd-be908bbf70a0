import request from "supertest";

import { app } from "@shared/infra/http/app";

import { PrismaClient } from ".prisma/client";

const prisma = new PrismaClient();

describe("Authenticate Player Controller", () => {
  afterEach(async () => {
    const wallet = prisma.wallet.deleteMany();
    const token = prisma.player_token.deleteMany();
    const player = prisma.player.deleteMany();

    await prisma.$transaction([wallet, token, player]);
    await prisma.$disconnect();
  });

  it("should be able to authenticate an existing player account with email", async () => {
    await request(app).post("/players/create").send({
      email: "<EMAIL>",
      password: "*********",
    });

    const response = await request(app).post("/players/authenticate").send({
      username: "<EMAIL>",
      password: "*********",
    });

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty("id");
    expect(response.body).toHaveProperty("token");
  });

  it("should be able to authenticate an existing player account with nickname", async () => {
    const { body } = await request(app).post("/players/create").send({
      email: "<EMAIL>",
      password: "*********",
    });

    const response = await request(app).post("/players/authenticate").send({
      username: body.nickname,
      password: "*********",
    });

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty("id");
    expect(response.body).toHaveProperty("token");
  });
});
