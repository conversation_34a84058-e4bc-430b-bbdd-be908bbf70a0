import { Request, Response } from "express";
import { container } from "tsyringe";

import { VerifyPixUseCase } from "./VerifyPixUseCase";

export class VerifyPixController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.user;
    const { key } = request.body;

    const verifyPixUseCase = container.resolve(VerifyPixUseCase);

    // const verify_pix = await verifyPixUseCase.execute(key);
    const verify_pix = await verifyPixUseCase.executetwo(Number(id), key);

    return response.status(200).json(verify_pix);
  }
}
