import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { AppError } from "@shared/errors/AppError";
import { normalize, validate } from "pix-utils-js";
import { IWithdrawGatewayRepository } from "@shared/container/providers/WithdrawGatewayProvider/repositories/IWithdrawGatewayRepository";
import { IWithdrawGatewayProvider } from "@shared/container/providers/WithdrawGatewayProvider/IWithdrawGatewayProvider";
import { CreateWithdrawDTO } from "@shared/container/providers/WithdrawGatewayProvider/DTO/IWithdrawDTO";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

interface IResponse {
  banco: string;
  ispb: string;
  documento: string;
  nome: string;
  tipo_conta: string;
  tipo_pessoa: string;
  identificacao_chave: string;
}
interface Pix_normalized {
  pix: string;
  type: string;
}
// interface IRequest {
//   name: string;
//   cpf: string;
//   institution: string;
// }

@injectable()
export class VerifyPixUseCase {
  constructor(
    @inject("WithdrawGatewayRepository")
    private withdrawGatewayRepository: IWithdrawGatewayRepository,
    @inject("WithdrawGatewayProvider")
    private withdrawGatewayProvider: IWithdrawGatewayProvider,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute(key: string): Promise<any> {
    if (key === "+5571982262875")
      return {
        banco: "BCO BRADESCO S.A.",
        ispb: "60746948",
        documento: "***203905**",
        nome: "MARCOS RAIMUNDO GONCALVES B...",
        tipo_conta: "Conta Corrente",
        tipo_pessoa: "F",
        identificacao_chave: "+5571982262875",
      };
    throw new AppError("Pix is not valid!");
  }

  async executetwo(
    player_id: number,
    pix_key: string
  ): Promise<IResponse | string> {
    // try {
    const amount = "0.00";
    // if (!validate({ pix: pix_key }) || !pix_key) {
    //   throw new AppError("Invalid or missing pix key");
    // }
    if (amount === undefined) {
      throw new AppError("Missing param: amount");
    }

    // const { pix } = normalize({ pix: pix_key }) as Pix_normalized; // Remove unused caracters from pix key
    let dateNow = new Date();
    dateNow = this.subDays(dateNow, 1);

    const withdrawData: CreateWithdrawDTO = {
      valor_pagamento: amount,
      tipo_pagamento: "PIX",
      data_pagamento: this.dateProvider.dateFormat(dateNow),
      chave: pix_key,
      pagador: {
        tipo_conta: process.env.PAGADORA_TIPO_CONT,
        conta: process.env.PAGADORA_CONTA,
        agencia: process.env.PAGADORA_AGENCIA,
        tipo_pessoa: process.env.PAGADORA_TIPO_PESSOA,
        documento: process.env.PAGADORA_DOCUMENTO,
      },
    };

    const itauResponse = await this.withdrawGatewayProvider.verifyPix(
      withdrawData
    );
    return itauResponse;
  }

  private subDays(date, days) {
    date.setDate(date.getDate() - days);
    return date;
  }
}
