import crypto from "crypto";
import { resolve } from "path";
import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { IMailProvider } from "@shared/container/providers/MailProvider/IMailProvider";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class SendForgotPasswordMailUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("MailProvider") private mailProvider: IMailProvider
  ) {}
  async execute(email: string): Promise<void> {
    const player = await this.playerRepository.findByEmail(email);

    const templatePath = resolve(
      __dirname,
      "..",
      "..",
      "views",
      "email",
      "forgotPassword.hbs"
    );

    if (!player) {
      throw new AppError("Player does not exists");
    }

    const buffer = crypto.randomBytes(6)
    let token = '';
    for (let i = 0; i < buffer.length; i++) {
      const num = buffer[i] % 10;
      token += num.toString();
    }
    
    
    const expires_date = this.dateProvider.addMinutes(5);
    
    await this.playerRepository.createToken({
      player_id: player.id,
      token,
      expires_date,
    });

    const variables = {
      name: player.name,
      link: token,
    };

    await this.mailProvider.sendMail(
      email,
      "Redefinição de senha",
      variables,
      templatePath
    );
  }
}
