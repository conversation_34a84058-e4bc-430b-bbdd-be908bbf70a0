/* eslint-disable no-restricted-syntax */
import crypto from "crypto";
import { resolve } from "path";
import { inject, injectable } from "tsyringe";

import { IUpdatePlayerDTO } from "@modules/players/dtos/IUpdatePlayerDTO";
import { playerMap } from "@modules/players/mapper/playerMap";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { IMailProvider } from "@shared/container/providers/MailProvider/IMailProvider";
import { IStorageProvider } from "@shared/container/providers/StorageProvider/IStorageProvider";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class UpdatePlayerUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("StorageProvider") private storageProvider: IStorageProvider,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("MailProvider") private mailProvider: IMailProvider
  ) {}

  async execute({
    id,
    nickname,
    avatar,
    games,
    cover,
    birth_date,
    cpf,
    name,
    bio,
    address,
    phone,
    stream_link,
    communication_link,
    isPro,
    mentoring_games,
    push_token,
    email,
    nickname_twitch,
    subscribe_twitch,
    total_views_twitch,
  }: IUpdatePlayerDTO): Promise<any> {
    const player = await this.playerRepository.findById(id);

    if (nickname) {
      const nicknameExists = await this.playerRepository.findByNickname(
        nickname
      );

      if (nicknameExists && nicknameExists.id !== player.id) {
        throw new AppError("Nickname already exists");
      }
    }

    if (avatar) {
      if (player.avatar) {
        await this.storageProvider.delete(player.avatar, "avatar");
      }

      await this.storageProvider.save(avatar, "avatar");
    }

    if (cover) {
      const defaultCovers = [
        "Capa_inicial-1.png",
        "Capa_inicial-3.png",
        "Capa_inicial-5.png",
        "Capa_inicial-7.png",
      ];

      if (!defaultCovers.includes(player.cover_picture)) {
        await this.storageProvider.delete(player.cover_picture, "cover");
      }

      await this.storageProvider.save(cover, "cover");
    }

    if (mentoring_games) {
      // if a player already has mentoring games, we need to remove them
      const mentoringGamesIds = player.Mentoring_games.map(
        (mentoring) => mentoring.game_id
      );

      mentoring_games.push(...mentoringGamesIds);

      const mentoringGamesArray = [...new Set(mentoring_games)];
      const mentoringGames = mentoringGamesArray.filter(
        (mentoring) => !mentoringGamesIds.includes(mentoring)
      );

      if (mentoringGames.length > 0) {
        await this.playerRepository.addMentoringGame(id, mentoringGames);
      }
    }

    if (email) {
      const emailExists = await this.playerRepository.findByEmail(email);

      if (emailExists) {
        throw new AppError("Email already in use");
      }

      const token = crypto.randomBytes(32).toString("hex");
      const expires_date = this.dateProvider.addHours(24);

      await this.playerRepository.createToken({
        player_id: player.id,
        token,
        expires_date,
      });

      const templatePath = resolve(
        __dirname,
        "..",
        "..",
        "views",
        "email",
        "verifyEmail.hbs"
      );

      const variables = {
        email,
        link: `${process.env.CONFIRM_MAIL_URL}${token}`,
      };

      await this.mailProvider.sendMail(
        email,
        "Confirmação de email",
        variables,
        templatePath
      );

      await this.playerRepository.updateById({
        id: player.id,
        isEmailVerified: false,
      });
    }

    if (birth_date) {
      const age = this.dateProvider.compareInYears(birth_date);

      if (age < 13) {
        throw new AppError("You must be at least 13 years old to register");
      }
    }

    const updatedPlayer = await this.playerRepository.updateById({
      id,
      nickname,
      avatar,
      games,
      cover,
      name,
      bio,
      birth_date: birth_date && new Date(birth_date),
      phone,
      stream_link,
      communication_link,
      cpf,
      isPro,
      push_token,
      email,
      address,
      nickname_twitch,
      subscribe_twitch,
      total_views_twitch,
    });

    return playerMap.toDTO(updatedPlayer);
  }
}
