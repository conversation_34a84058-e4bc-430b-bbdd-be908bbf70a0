import { Request, Response } from "express";
import { container } from "tsyringe";

import { resizeImage } from "@utils/resizeImage";

import { UpdatePlayerUseCase } from "./UpdatePlayerUseCase";

export class UpdatePlayerController {
  async handle(request: Request, response: Response): Promise<Response> {
    const {
      nickname,
      games,
      name,
      cpf,
      birth_date,
      phone,
      stream_link,
      communication_link,
      bio,
      isPro,
      mentoring_games,
      push_token,
      email,
      address,
      nickname_twitch,
      subscribe_twitch,
      total_views_twitch,
    } = request.body;
    const { id } = request.user;

    let arrGames: number[] = [];
    if (games !== undefined) {
      arrGames = typeof games === "object" ? games : JSON.parse(games);
    }

    let arrMentoringGames: number[] = [];
    if (mentoring_games !== undefined) {
      arrMentoringGames =
        typeof mentoring_games === "object"
          ? mentoring_games
          : JSON.parse(mentoring_games);
    }

    const files = request.files as {
      [fieldname: string]: Express.Multer.File[];
    };

    let avatar;
    if (files?.avatar) {
      const tempAvatar = files?.avatar[0];

      avatar = await resizeImage(tempAvatar.path, tempAvatar.destination);
    }

    let cover;
    if (files?.cover) {
      const tempCover = files?.cover[0];

      cover = await resizeImage(tempCover.path, tempCover.destination, {
        width: 700,
        height: 400,
      });
    }

    const updatePlayerUseCase = container.resolve(UpdatePlayerUseCase);

    const player = await updatePlayerUseCase.execute({
      id: Number(id),
      nickname,
      avatar,
      cover,
      name,
      cpf,
      birth_date,
      phone,
      stream_link,
      communication_link,
      games: arrGames,
      isPro,
      bio,
      mentoring_games: arrMentoringGames,
      push_token,
      email,
      address,
      nickname_twitch,
      subscribe_twitch,
      total_views_twitch,
    });

    return response.json(player);
  }
}
