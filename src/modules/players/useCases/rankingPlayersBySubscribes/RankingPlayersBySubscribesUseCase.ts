import { inject, injectable, container } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { PlayerRepository } from "@modules/players/infra/prisma/repositories/PlayerRepository";
import { map } from "lodash";

container.registerSingleton('PlayerRepository', PlayerRepository);

@injectable()
export class RankingPlayerbySubscribesUseCase {
  constructor(@inject("PlayerRepository") private playerRepository: IPlayerRepository) {}

  async execute(): Promise<any> {
    const rankingBySubscribes = await this.playerRepository.rankingPlayersBySubscribes();
    
    const rankingWithNumberSubscribes = rankingBySubscribes.map((ranking) => {
      return {
        id: ranking.id,
        name: ranking.name,
        link_channel: ranking.stream_link,
        picture: ranking.cover_picture,
        subscribes_twitch: Number(ranking.subscribe_twitch)
      };
    });


    return rankingWithNumberSubscribes;
  }
}
