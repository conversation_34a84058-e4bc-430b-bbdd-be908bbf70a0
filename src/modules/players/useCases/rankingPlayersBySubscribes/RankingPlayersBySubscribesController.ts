import { Request, Response } from "express";
import { container } from "tsyringe";

import { RankingPlayerbySubscribesUseCase } from "./RankingPlayersBySubscribesUseCase";

export class RankingPlayerbySubscribesController {
  async handle(request: Request, response: Response): Promise<Response> {
    
    const rankingPlayerbySubscribesUseCase = container.resolve(
      RankingPlayerbySubscribesUseCase
    );
    const rankingBySubscribes = await rankingPlayerbySubscribesUseCase.execute();

    return response.json(rankingBySubscribes);
  }
}
