import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  code: string;
  player_id: number;
}

@injectable()
export class VerifySMSCodeUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ player_id, code }: IRequest): Promise<string> {
    const playerCode = await this.playerRepository.findTokenByCode(code);

    if (!playerCode) {
      throw new AppError("Invalid code");
    }

    if (playerCode.player_id !== player_id) {
      throw new AppError("Invalid code");
    }

    const isTokenExpired = this.dateProvider.compareIfBefore(
      playerCode.expires_date,
      new Date()
    );

    if (isTokenExpired) {
      throw new AppError("Code expired");
    }

    await this.playerRepository.deleteTokenById(playerCode.id);

    return "Code verified";
  }
}
