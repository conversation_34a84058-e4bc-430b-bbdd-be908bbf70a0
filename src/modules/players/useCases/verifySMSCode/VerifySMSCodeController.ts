import { Request, Response } from "express";
import { container } from "tsyringe";

import { VerifySMSCodeUseCase } from "./VerifySMSCodeUseCase";

export class VerifySMSCodeController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { code } = request.params;
    const { id } = request.user;

    const verifySMSCodeUseCase = container.resolve(VerifySMSCodeUseCase);

    const result = await verifySMSCodeUseCase.execute({
      code: String(code),
      player_id: Number(id),
    });

    return response.json(result);
  }
}
