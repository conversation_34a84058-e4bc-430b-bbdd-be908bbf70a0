import { hash } from "bcrypt";
import { resolve } from "path";
import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { IMailProvider } from "@shared/container/providers/MailProvider/IMailProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  token: string;
  password: string;
}

@injectable()
export class ResetPasswordPlayerUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("MailProvider") private mailProvider: IMailProvider
  ) {}

  async execute({ token, password }: IRequest): Promise<void> {
    const playerToken = await this.playerRepository.findByToken(token);

    if (!playerToken) {
      throw new AppError("Invalid token");
    }

    const isTokenExpired = this.dateProvider.compareIfBefore(
      playerToken.expires_date,
      new Date()
    );

    if (isTokenExpired) {
      throw new AppError("Token expired");
    }

    const player = await this.playerRepository.findById(playerToken.player_id);

    const hashedPassword = await hash(password, 10);

    await this.playerRepository.updateById({
      id: player.id,
      password: hashedPassword,
    });

    await this.playerRepository.deleteTokenById(playerToken.id);

    const templatePath = resolve(
      __dirname,
      "..",
      "..",
      "views",
      "email",
      "resetPassword.hbs"
    );

    const variables = {};

    await this.mailProvider.sendMail(
      player.email,
      "Alerta de Segurança",
      variables,
      templatePath
    );
  }
}
