import { Request, Response } from "express";
import { container } from "tsyringe";

import { ResetPasswordPlayerUseCase } from "./ResetPasswordUseCase";

export class ResetPasswordPlayerController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { token } = request.query;
    const { password } = request.body;

    const resetPasswordPlayerUseCase = container.resolve(
      ResetPasswordPlayerUseCase
    );

    await resetPasswordPlayerUseCase.execute({
      token: String(token),
      password,
    });

    return response.send();
  }
}
