import { Request, Response } from "express";
import { container } from "tsyringe";

import { RankingProUseCase } from "./RankingProUseCase";

export class RakingProController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { filter, game_id } = request.query;

    const rankingProUseCase = container.resolve(RankingProUseCase);

    const result = await rankingProUseCase.execute({
      filter: filter as "classificacao" | "top10" | "qtd_pro",
      game_id: Number(game_id),
    });

    return response.json(result);
  }
}
