import { inject, injectable } from "tsyringe";

import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";
import { TPlayerStats } from "@modules/players/dtos/IPlayerDTO";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { <PERSON><PERSON>, Player } from "@prisma/client";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  filter: "classificacao" | "top10" | "qtd_pro";
  game_id: number;
}

type IPlayer = Pick<Player, "id" | "nickname" | "avatar"> & {
  position?: number;
  avaliation?: number;
  player_stats?: TPlayerStats[];
  badges?: Badge[];
};

@injectable()
export class RankingProUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("GamesRepository") private gamesRepository: IGamesRepository,
    @inject("SessionRepository") private sessionRepository: ISessionRepository
  ) {}

  async addPlayerAvaliation(player: IPlayer) {
    const avaliations = await this.playerRepository.findAvaliations(player.id);

    let average = 5;

    if (avaliations.length > 0) {
      average =
        avaliations.reduce((acc, curr) => {
          return acc + curr.points;
        }, 5) /
        (avaliations.length + 1);
    }

    Object.assign(player, {
      avaliation: average,
    });

    return player;
  }

  async addPlayerAvaliationByGame(player: IPlayer, game_id: number) {
    const avaliations = await this.playerRepository.findAvaliationsByGame(
      player.id,
      game_id
    );

    let average = 5;

    if (avaliations.length > 0) {
      average =
        avaliations.reduce((acc, curr) => {
          return acc + curr.points;
        }, 5) /
        (avaliations.length + 1);
    }

    Object.assign(player, {
      avaliation: average,
    });

    return player;
  }

  async execute({ filter, game_id }: IRequest): Promise<any> {
    const players = await this.playerRepository.listAll();

    const proPlayer = players.filter((player) => player.isPro);

    if (proPlayer.length === 0) {
      return [];
    }

    if (filter === "qtd_pro") {
      const proByGame = proPlayer.reduce((acc, player) => {
        const { games } = player;

        games.forEach((game) => {
          if (!acc[game.name]) {
            acc[game.name] = {
              id: game.id,
              logo: game.logo,
              banner: game.banner,
              quantity: 0,
            };
          }

          acc[game.name].quantity += 1;
        });

        return acc;
      }, {});

      const proByGameArray = Object.keys(proByGame).map((key) => {
        return {
          game_id: proByGame[key].id,
          name: key,
          banner: encodeURI(
            `${process.env.AWS_BUCKET_URL}/games/banner/${proByGame[key].banner}`
          ),
          logo: encodeURI(
            `${process.env.AWS_BUCKET_URL}/games/logo/${proByGame[key].logo}`
          ),
          quantity: proByGame[key].quantity,
        };
      });

      return proByGameArray;
    }

    if (filter === "top10") {
      if (game_id) {
        const game = await this.gamesRepository.findById(game_id);

        if (!game) {
          throw new AppError("Game not found");
        }

        let ranking: IPlayer[] =
          await this.gamesRepository.listProPlayersByGame(game_id);

        ranking = await Promise.all(
          ranking.map(async (player) => {
            return this.addPlayerAvaliationByGame(player, game_id);
          })
        );

        ranking.sort((a, b) => b.avaliation - a.avaliation);

        ranking.map((player, index) => {
          Object.assign(player, {
            position: index + 1,
          });
        });

        return ranking
          .map((player) => {
            return {
              id: player.id,
              position: player.position,
              nickname: player.nickname,
              avatar: player.avatar
                ? encodeURI(
                    `${process.env.AWS_BUCKET_URL}/avatar/${player.avatar}`
                  )
                : null,
              badges: player.badges
                .map((badge) => {
                  return {
                    id: badge.id,
                    name: badge.name,
                    icon: encodeURI(
                      `${process.env.AWS_BUCKET_URL}/badges/${badge.icon}`
                    ),
                    description: badge.description,
                  };
                })
                .filter((badge) => badge.name !== "Jogador Pro"),
              avaliation: player.avaliation.toFixed(1),
            };
          })
          .slice(0, 10);
      }

      const ranking = await Promise.all(
        proPlayer.map(async (player) => {
          return this.addPlayerAvaliation(player);
        })
      );

      ranking.sort((a, b) => b.avaliation - a.avaliation);

      ranking.map((player, index) => {
        Object.assign(player, {
          position: index + 1,
        });
      });

      return ranking
        .map((player) => {
          return {
            id: player.id,
            position: player.position,
            nickname: player.nickname,
            avatar: player.avatar
              ? encodeURI(
                  `${process.env.AWS_BUCKET_URL}/avatar/${player.avatar}`
                )
              : null,
            badges: player.badges
              .map((badge) => {
                return {
                  id: badge.id,
                  name: badge.name,
                  icon: encodeURI(
                    `${process.env.AWS_BUCKET_URL}/badges/${badge.icon}`
                  ),
                  description: badge.description,
                };
              })
              .filter((badge) => badge.name !== "Jogador Pro"),
            avaliation: player.avaliation.toFixed(1),
          };
        })
        .slice(0, 10);
    }

    if (filter === "classificacao") {
      if (game_id) {
        const game = await this.gamesRepository.findById(game_id);

        if (!game) {
          throw new AppError("Game not found");
        }

        let ranking: IPlayer[] =
          await this.gamesRepository.listProPlayersByGame(game_id);

        ranking = await Promise.all(
          ranking.map(async (player) => {
            return this.addPlayerAvaliationByGame(player, game_id);
          })
        );

        const rankingByGame = ranking.reduce(
          (acc, player) => {
            if (player.avaliation >= 5) {
              acc[0].quantity += 1;
            }

            if (player.avaliation >= 4 && player.avaliation < 5) {
              acc[1].quantity += 1;
            }

            if (player.avaliation >= 3 && player.avaliation < 4) {
              acc[2].quantity += 1;
            }

            if (player.avaliation >= 2 && player.avaliation < 3) {
              acc[3].quantity += 1;
            }

            if (player.avaliation >= 1 && player.avaliation < 2) {
              acc[4].quantity += 1;
            }

            return acc;
          },
          [
            {
              star: 5,
              quantity: 0,
            },
            {
              star: 4,
              quantity: 0,
            },
            {
              star: 3,
              quantity: 0,
            },
            {
              star: 2,
              quantity: 0,
            },
            {
              star: 1,
              quantity: 0,
            },
          ]
        );

        const average =
          ranking.reduce((acc, curr) => {
            return acc + curr.avaliation;
          }, 0) / ranking.length || 0;

        return {
          avaliations: ranking.length,
          average: average.toFixed(1),
          stars: rankingByGame,
        };
      }
    }

    return {};
  }
}
