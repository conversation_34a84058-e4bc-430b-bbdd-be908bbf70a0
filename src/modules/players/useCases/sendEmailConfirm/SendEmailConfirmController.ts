import { Request, Response } from "express";
import { container } from "tsyringe";
import { SendEmailConfirmUseCase } from "./SendEmailConfirmUseCase";


export class SendEmailConfirmController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { email } = request.body;

    const sendEmailConfirmUseCase = container.resolve(
        SendEmailConfirmUseCase
    );

    await sendEmailConfirmUseCase.execute(email);
    return response.send();
  }
}
