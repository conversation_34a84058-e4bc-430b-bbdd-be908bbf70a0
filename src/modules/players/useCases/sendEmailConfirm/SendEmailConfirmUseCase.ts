import crypto from "crypto";
import { resolve } from "path";
import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { IMailProvider } from "@shared/container/providers/MailProvider/IMailProvider";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class SendEmailConfirmUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider,
    @inject("MailProvider") private mailProvider: IMailProvider
  ) {}
  async execute(email: string): Promise<void> {
    const player = await this.playerRepository.findByEmail(email);
    
    if(!player){
        throw new AppError("player doesn't exists")
    }
    
    const token = crypto.randomBytes(32).toString("hex");
    const expires_date = this.dateProvider.addHours(24);

    await this.playerRepository.createToken({
        player_id: player.id,
        token,
        expires_date,
    });
    
    const templatePath = resolve(
        __dirname,
        "..",
        "..",
        "views",
        "email",
        "verifyEmail.hbs"
    );
    
    const variables = {
        email,
        link: `${process.env.CONFIRM_MAIL_URL}${token}`,
    };
    
    await this.mailProvider.sendMail(
        email,
        "Confirmação de email",
        variables,
        templatePath
    );

    await this.playerRepository.updateById({
        id: player.id,
        isEmailVerified: false,
    });
    }
}
