import { Request, Response } from 'express';
import { container } from 'tsyringe';
import { VerifyCpfAvailableUseCase } from './VerifyCpfAvailableUseCase';

export class VerifyCpfAvailableController {

  async handle(request: Request, response: Response): Promise<Response> {
    const { cpf } = request.params;

    const verifyCpfAvailableUseCase = container.resolve(VerifyCpfAvailableUseCase);

    const result = await verifyCpfAvailableUseCase.execute(cpf);

    return response.json({ message: result });
  }
}