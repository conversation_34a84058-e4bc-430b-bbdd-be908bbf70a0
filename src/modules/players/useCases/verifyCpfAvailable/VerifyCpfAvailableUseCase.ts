import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class VerifyCpfAvailableUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(cpf: string): Promise<string> {
    const calculateDigit = (cpf: string, weight: number[]): number => {
      let sum = 0;
      for (let i = 0; i < weight.length; i++) {
        sum += parseInt(cpf[i]) * weight[i];
      }
      const remainder = (sum % 11);
      return remainder < 2 ? 0 : 11 - remainder;
    };

    const isValidCPF = (cpf: string): boolean => {
      cpf = cpf.replace(/\D/g, '');

      if (cpf.length !== 11) return false;

      if (/^(\d)\1{10}$/.test(cpf)) return false;

      const weight1 = [10, 9, 8, 7, 6, 5, 4, 3, 2];
      const digit1 = calculateDigit(cpf, weight1);
      if (parseInt(cpf[9]) !== digit1) return false;

      const weight2 = [11, 10, 9, 8, 7, 6, 5, 4, 3, 2];
      const digit2 = calculateDigit(cpf, weight2);
      return parseInt(cpf[10]) === digit2;
    };

    if (!isValidCPF(cpf)) {
      throw new AppError("Invalid CPF");
    }

    const player = await this.playerRepository.findByCpf(cpf);

    if (player) {
      throw new AppError("Cpf alread's exists");
    }

    return "Cpf available";
  }
}
