import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IPlayerStatsByGame } from "@modules/players/dtos/IPlayerStatsByGame";

@injectable()
export class ListPlayerStatsByGameUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(
    player_id: number,
    game_id: number
  ): Promise<IPlayerStatsByGame[]> {
    return await this.playerRepository.listByPlayerIdPlayerStatsByGame(
      player_id,
      game_id
    );
  }
}
