import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListPlayerStatsByGameUseCase } from "./ListByPlayerIdPlayerStatsByGameUseCase";

export class ListPlayerStatsByGameController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { player_id, game_id } = request.params;

    const listPlayerStatsByGameUseCase = container.resolve(
      ListPlayerStatsByGameUseCase
    );

    const list_player_stats = await listPlayerStatsByGameUseCase.execute(
      Number(player_id),
      Number(game_id)
    );

    return response.json(list_player_stats);
  }
}
