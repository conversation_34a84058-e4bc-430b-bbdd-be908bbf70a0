import { Request, Response } from "express";
import { container } from "tsyringe";

import { CheckSessionUseCase } from "./CheckSessionUseCase";

export class CheckSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { token, player_id } = request.body;

    const checkSessionUseCase = container.resolve(CheckSessionUseCase);

    const result = await checkSessionUseCase.execute({
      token,
      player_id,
    });

    return response.json(result);
  }
}
