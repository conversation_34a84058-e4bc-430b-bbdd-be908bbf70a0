import { sign } from "jsonwebtoken";
import { inject, injectable } from "tsyringe";

import auth from "@config/auth";
import { IPlayerDTO } from "@modules/players/dtos/IPlayerDTO";
import { playerMap } from "@modules/players/mapper/playerMap";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  token: string;
  player_id: string | number;
}

type IResponse = IPlayerDTO & {
  token: string;
};

@injectable()
export class CheckSessionUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ token, player_id }: IRequest): Promise<IResponse> {
    const { secret_token, expires_in_token, expires_in_token_days } = auth;
    const playerToken = await this.playerRepository.findByToken(token);

    if (!playerToken) {
      throw new AppError("Invalid token");
    }

    if (player_id) {
      if (playerToken.player_id !== Number(player_id)) {
        throw new AppError("Invalid token");
      }
    }

    const player = await this.playerRepository.findById(playerToken.player_id);

    const isTokenExpired = this.dateProvider.compareIfBefore(
      playerToken.expires_date,
      new Date()
    );

    let newToken: string;
    if (isTokenExpired) {
      newToken = sign({}, secret_token, {
        subject: String(player.id),
        expiresIn: expires_in_token,
      });

      const expires_date = this.dateProvider.addDays(expires_in_token_days);

      await this.playerRepository.deleteTokenById(playerToken.id);
      await this.playerRepository.createToken({
        expires_date,
        token: newToken,
        player_id: player.id,
      });
    }

    return {
      ...playerMap.toDTO(player),
      token: newToken,
    };
  }
}
