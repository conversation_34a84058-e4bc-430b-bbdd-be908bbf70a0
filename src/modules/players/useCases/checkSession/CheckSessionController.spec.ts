import request from "supertest";

import { app } from "@shared/infra/http/app";

import { PrismaClient } from ".prisma/client";

const prisma = new PrismaClient();

let token: string;
let player_id: number;

describe("Check Session Controller", () => {
  beforeAll(async () => {
    await request(app).post("/players/create").send({
      email: "<EMAIL>",
      password: "123456789",
    });

    const { body } = await request(app).post("/players/authenticate").send({
      username: "<EMAIL>",
      password: "123456789",
    });

    token = body.token;
    player_id = body.id;
  });

  afterAll(async () => {
    const wallet = prisma.wallet.deleteMany();
    const token = prisma.player_token.deleteMany();
    const player = prisma.player.deleteMany();

    await prisma.$transaction([wallet, token, player]);
    await prisma.$disconnect();
  });

  it("should be able to check a session", async () => {
    const response = await request(app).post("/players/check_session").send({
      token,
      player_id,
    });

    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      id: player_id,
      name: null,
      nickname: "eric",
      email: "<EMAIL>",
      phone: null,
      bio: null,
      birthdate: null,
      country: null,
      cover_picture: null,
      avatar: null,
      isPro: false,
      badges: [],
      knowbie_points: 0,
      games: [],
      sessions: [],
      appointments: [],
      available_appointments: [],
    });
  });

  it("should not be able to check a session if player_id is different of token player_id", async () => {
    const response = await request(app)
      .post("/players/check_session")
      .send({
        token,
        player_id: player_id + 1,
      });

    expect(response.status).toBe(400);
    expect(response.body).toMatchObject({ message: "Invalid token" });
  });

  it("should not be able to check a session if token is invalid", async () => {
    const response = await request(app).post("/players/check_session").send({
      token:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE2MzUyNTM2ODksImV4cCI6MTYzNzg0NTY4OSwic3ViIjoiMzIifQ.mWY9l5Vkp1_Xku2bC_agpqblQCEnytojWmD648QGO1U",
      player_id,
    });

    expect(response.status).toBe(400);
    expect(response.body).toMatchObject({ message: "Invalid token" });
  });
});
