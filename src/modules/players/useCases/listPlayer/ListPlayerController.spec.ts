import request from "supertest";

import { app } from "@shared/infra/http/app";

import { PrismaClient } from ".prisma/client";

const prisma = new PrismaClient();

let player_id: number;

describe("ListPlayerController", () => {
  beforeAll(async () => {
    const { body } = await request(app).post("/players/create").send({
      email: "<EMAIL>",
      password: "123456789",
    });

    player_id = body.id;
  });

  afterAll(async () => {
    const wallet = prisma.wallet.deleteMany();
    const token = prisma.player_token.deleteMany();
    const player = prisma.player.deleteMany();

    await prisma.$transaction([wallet, token, player]);
    await prisma.$disconnect();
  });

  it("should be able to list a player by a given id", async () => {
    const response = await request(app).get(`/players/${player_id}`);

    expect(response.status).toBe(200);
  });
});
