import { inject, injectable } from "tsyringe";

import { IPlayerDTO } from "@modules/players/dtos/IPlayerDTO";
import { playerMap } from "@modules/players/mapper/playerMap";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class ListPlayerUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("SessionRepository") private SessionRepository: ISessionRepository
  ) {}

  async execute(id: number): Promise<IPlayerDTO> {
    const player = await this.playerRepository.findById(id);

    if (!player) {
      throw new AppError("Player not found");
    }

    const avaliations = await this.playerRepository.findAvaliations(player.id);

    let average = 5;

    if (avaliations.length !== 0) {
      average =
        avaliations.reduce((acc, curr) => {
          return acc + curr.points;
        }, 5) /
        (avaliations.length + 1);
    }

    const Tags = await this.playerRepository.findTags(player.id);

    // Necessaria conversão pois estava vindo bigint e bugando typagem do JS
    const formattedTags = Tags.map((tag) => ({
      name: tag.name,
      quantity: Number(tag.quantity),
    }));

    player.avaliations = {
      average: Number(average.toFixed(1)),
      tags: formattedTags,
      comments: avaliations,
    };

    const recurring_sessions =
      await this.SessionRepository.listRecurringSessions();

    player.recurring_sessions = recurring_sessions;

    return playerMap.toDTO(player);
  }
}
