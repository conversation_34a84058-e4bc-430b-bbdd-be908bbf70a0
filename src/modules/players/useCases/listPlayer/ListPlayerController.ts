import { Request, Response } from 'express';
import { container } from 'tsyringe';

import { ListPlayerUseCase } from './ListPlayerUseCase';

export class ListPlayerController {
	async handle(request: Request, response: Response): Promise<Response> {
		const { id } = request.params;

		const listPlayerUseCase = container.resolve(ListPlayerUseCase);

		const player = await listPlayerUseCase.execute(Number(id));

		return response.json(player);
	}
}
