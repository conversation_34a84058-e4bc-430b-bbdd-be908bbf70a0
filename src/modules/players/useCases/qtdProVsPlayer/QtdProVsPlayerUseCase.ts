import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IQtdProVsPlayerDTO } from "@modules/players/dtos/IQtdProVsPlayerDTO";

@injectable()
export class QtdProVsPlayerUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(): Promise<IQtdProVsPlayerDTO> {
    
    const players = await this.playerRepository.listAll();
    
    const proPlayer = players.filter((player) => player.isPro);

    const qtdProPlayer = proPlayer.length

    const qtdPlayer = players.length - qtdProPlayer

    return {
      label_player: "Player",
      qtd_player: qtdPlayer,
      label_pro_player: "Pro Player",
      qtd_pro_player: qtdProPlayer,
      total: qtdProPlayer + qtdPlayer
    }

  }
}
