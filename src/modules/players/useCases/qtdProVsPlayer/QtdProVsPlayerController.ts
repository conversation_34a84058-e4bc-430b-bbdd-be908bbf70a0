import { Request, Response } from "express";
import { container } from "tsyringe";
import { QtdProVsPlayerUseCase } from "./QtdProVsPlayerUseCase";

export class QtdProVsPlayerController {
  async handle(request: Request, response: Response): Promise<Response> {
    
    const qtdProVsPlayerUseCase = container.resolve(QtdProVsPlayerUseCase);
    
    const players = await qtdProVsPlayerUseCase.execute();

    return response.json(players);
  }
}
