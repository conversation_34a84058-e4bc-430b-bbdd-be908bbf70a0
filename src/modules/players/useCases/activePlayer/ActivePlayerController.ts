import { Request, Response } from "express";
import { container } from "tsyringe";

import { ActivePlayerUseCase } from "./ActivePlayerUseCase";

export class ActivePlayerController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.params;

    const activePlayerUseCase = container.resolve(ActivePlayerUseCase);

    const player = await activePlayerUseCase.execute(Number(id));

    return response.json(player);
  }
}
