import { inject, injectable } from "tsyringe";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { Player } from "@prisma/client";

@injectable()
export class ActivePlayerUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(id: number): Promise<Player> {
    return this.playerRepository.activePlayer(id);
  }
}
