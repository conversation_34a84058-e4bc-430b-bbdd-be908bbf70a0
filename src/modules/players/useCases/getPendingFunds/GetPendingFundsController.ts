import { Request, Response } from "express";
import { container } from "tsyringe";

import { GetPendingFundsUseCase } from "./GetPendingFundsUseCase";

export class GetPendingFundsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { player_id } = request.params;

    const getPendingFundsUseCase = container.resolve(GetPendingFundsUseCase);

    const pendingFunds = await getPendingFundsUseCase.execute(
      Number(player_id)
    );

    return response.json(pendingFunds);
  }
}
