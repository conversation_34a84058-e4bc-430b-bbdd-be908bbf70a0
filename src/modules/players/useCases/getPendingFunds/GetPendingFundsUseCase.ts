import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";

interface IResponse {
  pendingFunds: number;
  processFunds: number;
}
@injectable()
export class GetPendingFundsUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute(player_id: number): Promise<IResponse> {
    let pendingFunds = 0;
    let processFunds = 0;
    const pendingStatements =
      await this.playerRepository.findAllPendingStatements(player_id);

    const saqueStatements = await this.playerRepository.findAllSaqueStatements(
      player_id
    );

    pendingStatements.forEach((statement) => {
      pendingFunds += statement.quantity;
    });

    saqueStatements.forEach((statement) => {
      if (statement.Withdraw_request[0]?.status === "Sucesso")
        processFunds += statement.quantity;
    });

    return { pendingFunds, processFunds };
  }
}
