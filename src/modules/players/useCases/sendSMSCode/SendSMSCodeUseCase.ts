import crypto from "crypto";
import { inject, injectable } from "tsyringe";
import validator from "validator";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { ISMSProvider } from "@shared/container/providers/SMSProvider/ISMSProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  phoneNumber: string;
  player_id: number;
}

@injectable()
export class SendSMSCodeUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("SMSProvider") private smsProvider: ISMSProvider,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ phoneNumber, player_id }: IRequest): Promise<string> {
    const player = await this.playerRepository.findById(player_id);

    const code = crypto.randomInt(100000, 999999).toString();
    const expires_date = this.dateProvider.addMinutes(5);

    if (!validator.isMobilePhone(phoneNumber, "pt-BR", { strictMode: true })) {
      throw new AppError("Phone number is invalid");
    }

    await this.playerRepository.createToken({
      player_id: player.id,
      expires_date,
      code,
      token: "",
    });

    await this.smsProvider.sendSMS(
      phoneNumber,
      `Código de verificação Knowbie: ${code}`
    );

    return "Code sent";
  }
}
