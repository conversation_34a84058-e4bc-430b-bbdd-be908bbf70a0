import { Request, Response } from "express";
import { container } from "tsyringe";

import { SendSMSCodeUseCase } from "./SendSMSCodeUseCase";

export class SendSM<PERSON>odeController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { phoneNumber } = request.params;
    const { id } = request.user;

    const sendSMSCodeUseCase = container.resolve(SendSMSCodeUseCase);

    const result = await sendSMSCodeUseCase.execute({
      phoneNumber: String(phoneNumber),
      player_id: Number(id),
    });

    return response.json(result);
  }
}
