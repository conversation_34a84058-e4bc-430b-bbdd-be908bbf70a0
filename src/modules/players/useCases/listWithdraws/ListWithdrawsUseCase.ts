import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

interface IRequest {
  date: Date | string;
}

@injectable()
export class ListWithdrawsUseCase {
  constructor(
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ date }: IRequest): Promise<any> {
    const statements = await this.playerRepository.listStatements();

    const withdraws = date
      ? statements.filter(
          (statement) =>
            statement.title.includes("Saque") &&
            statement.type === "outcome" &&
            this.dateProvider.compareIfSameMonth(statement.createdAt, date)
        )
      : statements.filter(
          (statement) =>
            statement.title.includes("Saque") && statement.type === "outcome"
        );

    const withdrawsByDay = withdraws.reduce(
      (acc, statement) => {
        const day = new Date(statement.createdAt).getDate();

        if (day >= 1 && day <= 6) {
          acc["1 - 6"].quantity += statement.quantity;
        }

        if (day >= 7 && day <= 13) {
          acc["7 - 13"].quantity += statement.quantity;
        }

        if (day >= 14 && day <= 19) {
          acc["14 - 19"].quantity += statement.quantity;
        }

        if (day >= 20 && day <= 26) {
          acc["20 - 26"].quantity += statement.quantity;
        }

        if (day >= 27 && day <= 31) {
          acc["27 - 31"].quantity += statement.quantity;
        }

        return acc;
      },
      {
        "1 - 6": { quantity: 0 },
        "7 - 13": { quantity: 0 },
        "14 - 19": { quantity: 0 },
        "20 - 26": { quantity: 0 },
        "27 - 31": { quantity: 0 },
      }
    );

    const withdrawsByDayArray = Object.keys(withdrawsByDay).map((key) => {
      return {
        label: key,
        quantity: withdrawsByDay[key].quantity,
      };
    });

    return withdrawsByDayArray;
  }
}
