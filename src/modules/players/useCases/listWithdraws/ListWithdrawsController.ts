import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListWithdrawsUseCase } from "./ListWithdrawsUseCase";

export class ListWithdrawsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { date } = request.query;

    const listWithdrawsUseCase = container.resolve(ListWithdrawsUseCase);

    const withdraws = await listWithdrawsUseCase.execute({
      date: date as string | Date,
    });

    return response.json(withdraws);
  }
}
