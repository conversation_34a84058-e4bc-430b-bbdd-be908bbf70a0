import { inject, injectable } from "tsyringe";

import { PrismaClient, Tribute } from "@prisma/client";
import { ITributeRepository } from "@modules/tributes/repositories/ITributeRepository";
import { ICreateTributeDTO } from "@modules/tributes/dtos/ICreateTributeDTO";
import { IUpdateTributeDTO } from "@modules/tributes/dtos/IUpdateTributeDTO";

@injectable()
export class TributeRepository implements ITributeRepository {
  constructor(@inject("PrismaClient") private prisma: PrismaClient) {}

  async disablesOldersTributesAndCreate(data: ICreateTributeDTO): Promise<any> {
    const transaction = await this.prisma.$transaction([
      this.prisma.tribute.updateMany({
        where: {
          description: String(data.description),
        },
        data: {
          status_tribute: false,
        },
      }),
      this.prisma.tribute.create({ data }),
    ]);

    return transaction;
  }

  async listAllDistinctTribute(): Promise<Tribute[]> {
    const allTribute = await this.prisma.tribute.findMany({
      where: {
        status_tribute: true,
      },
      orderBy: {
        id: "asc",
      },
    });

    return allTribute;
  }

  async listHistoricTributeUseCase(description: string): Promise<Tribute[]> {
    const allTribute = await this.prisma.tribute.findMany({
      where: {
        description: description != "undefined" ? description : {},
      },
    });

    return allTribute;
  }

  async UpdateTributes(data: IUpdateTributeDTO): Promise<Tribute> {
    const tribute = await this.prisma.tribute.update({
      where: {
        id: data.id,
      },
      data: {
        description: data.description,
        value: data.value,
        status_tribute: data.status_tribute,
        type_tribute_value_id: data.type_tribute_value_id,
        user_id: data.user_id,
      },
    });
    return tribute;
  }
}
