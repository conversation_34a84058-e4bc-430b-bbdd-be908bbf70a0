import { Tribute } from ".prisma/client";
import { ICreateTributeDTO } from "../dtos/ICreateTributeDTO";
import { IUpdateTributeDTO } from "../dtos/IUpdateTributeDTO";

export interface ITributeRepository {
  disablesOldersTributesAndCreate(data: ICreateTributeDTO): Promise<any>;
  listAllDistinctTribute(): Promise<Tribute[]>;
  listHistoricTributeUseCase(description: string): Promise<Tribute[]>;
  UpdateTributes(data: IUpdateTributeDTO): Promise<Tribute>;
}
