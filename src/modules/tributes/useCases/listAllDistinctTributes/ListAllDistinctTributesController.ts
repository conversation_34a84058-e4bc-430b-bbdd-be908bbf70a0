import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListAllDistinctTributesUseCase } from "./ListAllDistinctTributesUseCase";

export class ListAllDistinctTributesController {
  async handle(request: Request, response: Response): Promise<Response> {
    const listAllDistinctTributesUseCase = container.resolve(
      ListAllDistinctTributesUseCase
    );

    const allTribute = await listAllDistinctTributesUseCase.execute();

    return response.json(allTribute);
  }
}
