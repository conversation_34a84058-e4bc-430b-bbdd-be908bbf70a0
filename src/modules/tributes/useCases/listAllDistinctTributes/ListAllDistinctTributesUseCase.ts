import { inject, injectable } from "tsyringe";
import { ITributeRepository } from "@modules/tributes/repositories/ITributeRepository";

@injectable()
export class ListAllDistinctTributesUseCase {
  constructor(
    @inject("TributeRepository") private tributeRepository: ITributeRepository
  ) {}

  async execute(): Promise<any> {
    const allTribute = await this.tributeRepository.listAllDistinctTribute();

    return allTribute;
  }
}
