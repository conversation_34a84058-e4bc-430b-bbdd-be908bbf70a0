import { inject, injectable } from "tsyringe";
import validator from "validator";
import { ITributeRepository } from "@modules/tributes/repositories/ITributeRepository";
import { Tribute, prisma } from "@prisma/client";
import { IUpdateTributeDTO } from "@modules/tributes/dtos/IUpdateTributeDTO";

@injectable()
export class UpdateTributeUseCase {
  constructor(
    @inject("TributeRepository") private tributeRepository: ITributeRepository
  ) {}

  async execute({
    id,
    description,
    value,
    status_tribute,
    type_tribute_value_id,
    user_id,
  }: IUpdateTributeDTO): Promise<Tribute> {
    let result = await this.tributeRepository.UpdateTributes({
      id,
      description,
      value,
      status_tribute,
      type_tribute_value_id,
      user_id,
    });

    return result;
  }
}
