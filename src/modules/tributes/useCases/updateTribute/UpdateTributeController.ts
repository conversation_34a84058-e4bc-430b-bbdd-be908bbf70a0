import { Request, Response } from "express";
import { container } from "tsyringe";

import { UpdateTributeUseCase } from "./UpdateTributeUseCase";

export class UpdateTributeController {
  /* #swagger.tags = ['Player'] */
  async handle(request: Request, response: Response): Promise<Response> {
    const { id, description, value, type_tribute_value_id, user_id } =
      request.body;

    const createTributeUseCase = container.resolve(UpdateTributeUseCase);

    const validDescriptions = [
      "comission fee",
      "license fee",
      "withdraw ted fee",
      "withdraw pix fee",
      "payment player fee",
    ];

    if (!validDescriptions.includes(description)) {
      throw new Error("Description is not valid");
    }

    const player = await createTributeUseCase.execute({
      id,
      description,
      value,
      status_tribute: true,
      type_tribute_value_id,
      user_id,
    });

    return response.json(player);
  }
}
