import { inject, injectable } from "tsyringe";
import validator from "validator";
import { ICreateTributeDTO } from "@modules/tributes/dtos/ICreateTributeDTO";
import { ITributeRepository } from "@modules/tributes/repositories/ITributeRepository";
import { Tribute, prisma } from "@prisma/client";

@injectable()
export class CreateTributeUseCase {
  constructor(
    @inject("TributeRepository") private tributeRepository: ITributeRepository
  ) {}

  async execute({
    description,
    value,
    status_tribute,
    type_tribute_value_id,
    user_id,
  }: ICreateTributeDTO): Promise<Tribute> {
    let result = await this.tributeRepository.disablesOldersTributesAndCreate({
      description,
      value,
      status_tribute,
      type_tribute_value_id,
      user_id,
    });

    return result;
  }
}
