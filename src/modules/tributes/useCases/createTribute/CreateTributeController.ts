import { Request, Response } from "express";
import { container } from "tsyringe";

import { CreateTributeUseCase } from "./CreateTributeUseCase";

export class CreateTributeController {
  /* #swagger.tags = ['Player'] */
  async handle(request: Request, response: Response): Promise<Response> {
    const { description, value, type_tribute_value_id, user_id } = request.body;

    const createTributeUseCase = container.resolve(CreateTributeUseCase);

    const validDescriptions = [
      "comission fee",
      "license fee",
      "withdraw ted fee",
      "withdraw pix fee",
      "payment player fee",
    ];

    if (!validDescriptions.includes(description)) {
      throw new Error("Description is not valid");
    }

    const player = await createTributeUseCase.execute({
      description,
      value,
      status_tribute: true,
      type_tribute_value_id,
      user_id,
    });

    return response.json(player);
  }
}
