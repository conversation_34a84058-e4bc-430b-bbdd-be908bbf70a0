import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListHistoricTributeUseCase } from "./ListHistoricTributeUseCase";

export class ListHistoricTributeController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { description } = request.query;
    const listHistoricTributeUseCase = container.resolve(
      ListHistoricTributeUseCase
    );

    const allTribute = await listHistoricTributeUseCase.execute(
      String(description)
    );

    return response.json(allTribute);
  }
}
