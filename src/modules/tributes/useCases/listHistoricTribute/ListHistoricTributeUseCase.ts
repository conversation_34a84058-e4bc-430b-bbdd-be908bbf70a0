import { inject, injectable } from "tsyringe";
import { ITributeRepository } from "@modules/tributes/repositories/ITributeRepository";
import { Tribute } from "@prisma/client";

@injectable()
export class ListHistoricTributeUseCase {
  constructor(
    @inject("TributeRepository") private tributeRepository: ITributeRepository
  ) {}

  async execute(description: string): Promise<Tribute[]> {
    const allTribute = await this.tributeRepository.listHistoricTributeUseCase(
      description
    );

    return allTribute;
  }
}
