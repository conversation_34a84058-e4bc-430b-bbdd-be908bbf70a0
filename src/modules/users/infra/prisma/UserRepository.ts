import { inject, injectable } from "tsyringe";

import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { Permission, PrismaClient, Role, User, User_token } from "@prisma/client";
import { GetResult } from "@prisma/client/runtime";

@injectable()
export class UserRepository implements IUserRepository {
  constructor(@inject("PrismaClient") private prisma: PrismaClient) {}

  async create(data: {
    email: string;
    password: string;
    name: string;
    registration: string;
    role_id: number;
  }): Promise<User> {
    const user = await this.prisma.user.create({
      data: {
        name: data.name,
        email: data.email,
        password: data.password,
        registration: data.registration,
        role_id: data.role_id,
      },
    });

    return user;
  }

  async findByEmail(email: string): Promise<User> {
    const user = await this.prisma.user.findFirst({
      where: {
        email,
      },
    });

    return user;
  }

  async findById(id: number): Promise<User> {
    const user = await this.prisma.user.findFirst({
      where: {
        id,
      },
      include: {
        Role: {
          include: {
            Permission: true,
          },
        },
      },
    });

    return user;
  }

  async findByToken(token: string): Promise<User_token> {
    const userToken = await this.prisma.user_token.findFirst({
      where: {
        token,
      },
    });

    return userToken;
  }

  async updateById(data: {
    id: number;
    password?: string;
    email?: string;
    name?: string;
    registration?: string;
    isActive?: boolean;
    avatar?: string;
    role_id?: number;
  }): Promise<User> {
    const user = await this.prisma.user.update({
      where: {
        id: data.id,
      },
      data: {
        email: data.email,
        name: data.name,
        password: data.password,
        isActive: data.isActive,
        registration: data.registration,
      },
    });

    return user;
  }

  async createToken(data: {
    token: string;
    user_id: number;
    expires_date: Date;
  }): Promise<User_token> {
    const token = await this.prisma.user_token.create({
      data: {
        token: data.token,
        expires_date: data.expires_date,
        User: {
          connect: {
            id: data.user_id,
          },
        },
      },
    });

    return token;
  }

  async deleteTokenById(id: number): Promise<void> {
    await this.prisma.user_token.delete({
      where: {
        id,
      },
    });
  }

  async listAllUsers(): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      include: {
        Role: {
          include: {
            Permission: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    return users;
  }
  async listAllRoles(): Promise<Role[]> {
    const roles = await this.prisma.role.findMany();
    return roles
  }
  async getUserPermissions(role_id: number): Promise<Permission[]> {
    const role = await this.prisma.role.findFirst({
      where: { id: role_id },
      include: { Permission: true }
    });
    
    if (!role) {
      throw new Error(`Role with id ${role_id} not found`);
    }
  
    const permissions = role.Permission || [];
  
    return permissions;
  }
  async getTotalUsersActive(): Promise<number> {
    const total = await this.prisma.user.count({});

    return total;
  }
}
