import { Permission, Role, User, User_token } from "@prisma/client";

export interface IUserRepository {
  create(data: {
    email: string;
    password: string;
    name: string;
    registration: string;
    role_id: number;
  }): Promise<User>;
  findByEmail(email: string): Promise<User>;
  findById(id: number): Promise<User>;
  findByToken(token: string): Promise<User_token>;
  updateById(data: {
    id: number;
    password?: string;
    name?: string;
    email?: string;
    registration?: string;
    isActive?: Boolean;
    avatar?: string;
    role_id?: number;
  }): Promise<User>;
  createToken(data: {
    token: string;
    user_id: number;
    expires_date: Date;
  }): Promise<User_token>;
  deleteTokenById(id: number): Promise<void>;
  listAllUsers(): Promise<User[]>;
  listAllRoles(): Promise<Role[]>;
  getUserPermissions(role_id: number): Promise<Permission[]>;
  getTotalUsersActive(): Promise<number>;
}
