import { Request, Response } from "express";
import { container } from "tsyringe";

import { GetTotalUsersActiveUseCase } from "./GetTotalUsersActiveUseCase";

export class GetTotalUsersActiveController {
  async handle(request: Request, response: Response): Promise<Response> {
    
    const totalUsersActiveUseCase = container.resolve(GetTotalUsersActiveUseCase);

    const total = await totalUsersActiveUseCase.execute();

    return response.json(total);
  }
}
