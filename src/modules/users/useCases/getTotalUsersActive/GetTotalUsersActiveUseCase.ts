import { inject, injectable } from "tsyringe";
import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { AppError } from "@shared/errors/AppError";

@injectable()
export class GetTotalUsersActiveUseCase {
  constructor(
    @inject("UserRepository") private userRepository: IUserRepository,
  ) {}

  async execute(): Promise<number> {

    const total = await this.userRepository.getTotalUsersActive();

    return total;
  }
}