import { compare } from "bcrypt";
import { sign } from "jsonwebtoken";
import { inject, injectable } from "tsyringe";

import auth from "@config/auth";
import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  email: string;
  password: string;
}

interface IPermission {
  id: number;
  name: string;
  route: string;
  icon: string;
}

interface IResponse {
  user: {
    id: number;
    email: string;
    name: string;
    permission: IPermission[];
  };
  token: string;
}

@injectable()
export class AuthenticateUserUseCase {
  constructor(
    @inject("UserRepository") private userRepository: IUserRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ email, password }: IRequest): Promise<IResponse> {
    const { secret_token } = auth;
    const expires_in_token = "7d";
    const expires_in_token_days = 7;
    const user = await this.userRepository.findByEmail(email);

    if (!user) {
      throw new AppError("User not found!");
    }

    const passwordMatched = await compare(password, user.password);

    if (!passwordMatched) {
      throw new AppError("username or password invalid!");
    }

    const token = sign({}, secret_token, {
      subject: String(user.id),
      expiresIn: expires_in_token,
    });

    const expires_date = this.dateProvider.addDays(expires_in_token_days);

    await this.userRepository.createToken({
      token,
      user_id: user.id,
      expires_date,
    });

    const permissions = await this.userRepository.getUserPermissions(user.role_id)

    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        permission: permissions,
      },
      token,
    };
  }
}
