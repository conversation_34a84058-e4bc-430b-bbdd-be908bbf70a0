import { inject, injectable } from "tsyringe";
import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { AppError } from "@shared/errors/AppError";
import { Role } from "@prisma/client";
import { Permission } from "@prisma/client"; // Certifique-se de importar o tipo Permission

@injectable()
export class ListAllRolesUseCase {
  constructor(
    @inject("UserRepository") private userRepository: IUserRepository,
  ) {}

  async execute(): Promise<{ role: Role; permissions: Permission[] }[]> {
    const roles = await this.userRepository.listAllRoles();

    const rolesWithPermissionsPromises = roles.map(async (role) => {
      const permissions = await this.userRepository.getUserPermissions(role.id);
      return { role, permissions };
    });

    const rolesWithPermissions = await Promise.all(rolesWithPermissionsPromises);

    return rolesWithPermissions;
  }
}