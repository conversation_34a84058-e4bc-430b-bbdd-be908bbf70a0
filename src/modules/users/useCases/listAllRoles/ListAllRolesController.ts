import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListAllRolesUseCase } from "./ListAllRolesUseCase";

export class ListAllRolesController {
  async handle(request: Request, response: Response): Promise<Response> {

    const listAllRolesUseCase = container.resolve(ListAllRolesUseCase);

    const Role = await listAllRolesUseCase.execute();

    return response.json(Role);
  }
}
