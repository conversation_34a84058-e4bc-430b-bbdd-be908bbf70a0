import { DeletePlayerUseCase } from "@modules/players/useCases/deletePlayer/DeletePlayerUseCase";
import { Request, Response } from "express";
import { container } from "tsyringe";
import { DeleteUserUseCase } from "./DeleteUserUseCase";


export class DeleteUserController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.params;
    const { isActive } = request.body;
    
    const deleteUserUseCase = container.resolve(
      DeleteUserUseCase
    );

    const user = await deleteUserUseCase.execute({
      id:  Number(id),
      isActive,
    });

    return response.json(user);
  }
}
