import { inject, injectable } from "tsyringe";

import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { User } from "@prisma/client";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";
import { hash } from "bcrypt";

interface IRequest {
  id: number;
  isActive?: Boolean;
}

type IResponse = User;

@injectable()
export class DeleteUserUseCase {
  constructor(
    @inject("UserRepository") private userRepository: IUserRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({
    id,
    isActive,
  }: IRequest): Promise<IResponse> {
    const user = await this.userRepository.findById(id);
    
    const updatedUser = await this.userRepository.updateById({
      id: user.id,
      isActive,
    });

    return updatedUser
  }
}
