import { inject, injectable } from "tsyringe";

import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

interface IRequest {
  token: string;
}

@injectable()
export class CheckUserSessionUseCase {
  constructor(
    @inject("UserRepository") private userRepository: IUserRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({ token }: IRequest): Promise<any> {
    const userToken = await this.userRepository.findByToken(token);

    if (!userToken) {
      throw new Error("Invalid token");
    }

    const user = await this.userRepository.findById(userToken.userId);

    delete user.password;

    return user;
  }
}
