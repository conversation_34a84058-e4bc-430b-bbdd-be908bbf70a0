import { Request, Response } from "express";
import { container } from "tsyringe";

import { CheckUserSessionUseCase } from "./CheckUserSessionUseCase";

export class CheckUserSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const authHeader = request.headers.authorization;
    const [, token] = authHeader.split(" ");

    const checkSessionUseCase = container.resolve(CheckUserSessionUseCase);

    const user = await checkSessionUseCase.execute({
      token,
    });

    return response.json(user);
  }
}
