import { hash } from "bcrypt";
import { sign } from "jsonwebtoken";
import { inject, injectable } from "tsyringe";
import validator from "validator";

import auth from "@config/auth";
import { IUserRepository } from "@modules/users/repositories/IUserRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  email: string;
  password: string;
  name?: string;
  registration: string;
  role_id: number;
}

interface IResponse {
  user: {
    id: number;
    email: string;
    name: string;
    registration: string;
    role_id: number;
  };
  token: string;
}

@injectable()
export class CreateUserUseCase {
  constructor(
    @inject("UserRepository") private userRepository: IUserRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute({
    email,
    password,
    name,
    registration,
    role_id,
  }: IRequest): Promise<IResponse> {
    const { secret_token } = auth;
    const expires_in_token = "7d";
    const expires_in_token_days = 7;
    const emailAlreadyExists = await this.userRepository.findByEmail(email);

    if (emailAlreadyExists) {
      throw new AppError("Email already exists!");
    }

    if (!validator.isEmail(email)) {
      throw new AppError("Email invalid!");
    }

    const hashedPassword = await hash(password, 10);

    const user = await this.userRepository.create({
      email,
      password: hashedPassword,
      name,
      registration,
      role_id,
    });

    const token = sign({}, secret_token, {
      subject: String(user.id),
      expiresIn: expires_in_token,
    });

    const expires_date = this.dateProvider.addDays(expires_in_token_days);

    await this.userRepository.createToken({
      token,
      user_id: user.id,
      expires_date,
    });

    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        registration: user.registration,
        role_id,
      },
      token,
    };
  }
}
