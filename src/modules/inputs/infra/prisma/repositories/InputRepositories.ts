import { inject, injectable } from "tsyringe";

import { IInputRepository } from "@modules/inputs/repositories/IInputRepository";


import {
    Input
} from ".prisma/client";
import { PrismaClient } from "@prisma/client";
import { IInputDTO } from "@modules/inputs/dtos/IInputDTO";

@injectable()
export class InputRepository implements IInputRepository {
    constructor(@inject("PrismaClient") private prisma: PrismaClient) {}

    async listAll(): Promise<IInputDTO[]> {
        const inputs = await this.prisma.input.findMany()

        return inputs;
    }

}
