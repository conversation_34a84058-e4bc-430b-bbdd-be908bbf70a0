import { AppError } from "@shared/errors/AppError";
import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListAllInputsUseCase } from "./ListAllInputsUseCase";

export class ListAllInputsController {
    async handle(request: Request, response: Response): Promise<Response> {
        
        const listAllInputsUseCase = container.resolve(ListAllInputsUseCase);
        
        const inputs = await listAllInputsUseCase.execute();

        return response.json(inputs);
        
    }
}