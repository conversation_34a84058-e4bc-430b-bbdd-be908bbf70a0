import { inject, injectable } from "tsyringe";

import { IInputDTO } from "@modules/inputs/dtos/IInputDTO";
import { IInputRepository } from "@modules/inputs/repositories/IInputRepository";

@injectable()
export class ListAllInputsUseCase {
  constructor(
    @inject("InputRepository") private inputRepository: IInputRepository
  ) {}

  async execute(): Promise<IInputDTO[]> {

    const inputs = await this.inputRepository.listAll();

    return inputs;
  }
}
