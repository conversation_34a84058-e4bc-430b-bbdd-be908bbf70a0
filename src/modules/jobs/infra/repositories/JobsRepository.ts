import { inject, injectable } from "tsyringe";

import { IJobsRepository } from "@modules/jobs/repositories/IJobsRepository";
import { Jobs, PrismaClient } from "@prisma/client";

@injectable()
export class JobsRepository implements IJobsRepository {
  constructor(@inject("PrismaClient") private prisma: PrismaClient) {}

  async findAll(): Promise<Jobs[]> {
    const jobs = await this.prisma.jobs.findMany({});

    return jobs;
  }

  async findbySeat(seat_id: number): Promise<Jobs[]> {
    const jobs = await this.prisma.jobs.findMany({
      where: {
        seat_id
      }
    });

    return jobs;
  }

  async findSeat(id: number): Promise<any> {
    const seat = await this.prisma.seat.findFirst({
      where: { id },
      include: {
        Session: {
          include: {
            Player: true,
          },
        },
      },
    });

    return seat;
  }
}
