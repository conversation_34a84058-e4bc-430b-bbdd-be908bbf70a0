import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListPromotedSessionsUseCase } from "./ListPromotedSessionsUseCase";

export class ListPromotedSessionsJobController {
  async handle(request: Request, response: Response): Promise<Response> {
    const listPromotedSessionsUseCase = container.resolve(
      ListPromotedSessionsUseCase
    );

    const promoted_sessions = await listPromotedSessionsUseCase.execute();

    return response.json(promoted_sessions);
  }
}
