import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { IDateProvider } from "@shared/container/providers/DateProvider/IDateProvider";

@injectable()
export class ListPromotedSessionsUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository,
    @inject("DateProvider") private dateProvider: IDateProvider
  ) {}

  async execute(): Promise<any> {
    const promoted_sessions =
      await this.sessionRepository.listPromotedSessions();

    const promotedSessionsMapped = promoted_sessions.map((promoted) => {
      const past_days = this.dateProvider.compareInDays(
        promoted.createdAt,
        new Date()
      );

      let status: string;

      if (promoted.active) {
        status = "active";
      } else if (
        !this.dateProvider.compareIfBefore(promoted.until, new Date()) &&
        !promoted.active
      ) {
        status = "paused";
      } else {
        status = "finished";
      }

      return {
        id: promoted.id,
        status,
        until: promoted.until,
        days_to_expire: promoted.days_to_expire,
        past_days,
        price: promoted.price,
        expected_reach_min: promoted.expected_reach_min,
        expected_reach_max: promoted.expected_reach_max,
        createdAt: promoted.createdAt,
        session_id: promoted.session_id,
        session: {
          id: promoted.Session.id,
          start_time: promoted.Session.start_time,
          end_time: promoted.Session.end_time,
          price: promoted.Session.price,
          seats: promoted.Session.seats,
          isActive: promoted.Session.isActive,
          createdAt: promoted.Session.createdAt,
          player_id: promoted.Session.player_id,
          game_id: promoted.Session.game_id,
          session_base_id: promoted.Session.session_base_id,
        },
      };
    });

    return promotedSessionsMapped;
  }
}
