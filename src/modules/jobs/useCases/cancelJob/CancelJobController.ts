import { Request, Response } from "express";
import { container } from "tsyringe";

import { CancelJobUseCase } from "./CancelJobUseCase";

export class CancelJobController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { seat_id } = request.params;

    const cancelJobUseCase = container.resolve(CancelJobUseCase);

    const job = await cancelJobUseCase.execute({ seat_id: Number(seat_id) });

    return response.json(job);
  }
}
