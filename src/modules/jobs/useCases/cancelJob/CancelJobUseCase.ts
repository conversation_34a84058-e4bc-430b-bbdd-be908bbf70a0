import { inject, injectable } from "tsyringe";

import { IJobsRepository } from "@modules/jobs/repositories/IJobsRepository";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { orderBy } from "lodash";

interface IRequest {
  seat_id: number;
}

@injectable()
export class CancelJobUseCase {
  constructor(
    @inject("JobsRepository") private jobsRepository: IJobsRepository,
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ seat_id }: IRequest): Promise<any> {
    const seat = await this.jobsRepository.findSeat(seat_id);

    const oldStatement = await this.playerRepository.findStatementByPlayer(
      seat.player_id,
      "createdAt"
    );

    const [statementBySession] = oldStatement
      .filter((statement) => statement.session_id === seat.session_id)
      .slice(-1); // always get last statement

    await this.playerRepository.saveStatement(
      "Compra de sessão",
      `${seat.Session.Player.nickname}`,
      statementBySession.quantity,
      "canceled",
      seat.player_id,
      statementBySession.session_id
    ); // player statement

    await this.playerRepository.updateWallet(
      seat.player_id,
      "income",
      statementBySession.quantity
    );

    const ownerStatement = await this.playerRepository.findStatementByPlayer(
      seat.Session.Player.id,
      "createdAt"
    );

    // TODO: testar com dois statements com status "pending"
    const [ownerStatementBySession] = ownerStatement
      .filter(
        (statement) =>
          statement.buyer_id === seat.player_id &&
          statement.session_id === seat.session_id &&
          statement.type === "pending"
      )
      .slice(-1);

    await this.playerRepository.deleteStatementById(ownerStatementBySession.id);
    await this.sessionRepository.deleteSeat(seat.id);

    return "Job canceled";
  }
}
