import { Request, Response } from "express";
import { container } from "tsyringe";

import { UpdateRecurrenceUseCase } from "./UpdateRecurrenceUseCase";

export class UpdateRecurrenceController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { recurrence_id, pattern, nextGenDate, untilDate } = request.body;

    const updateRecurrenceUseCase = container.resolve(UpdateRecurrenceUseCase);

    await updateRecurrenceUseCase.execute({
      recurrence_id,
      pattern,
      nextGenDate: new Date(nextGenDate),
      untilDate: new Date(untilDate),
    });

    return response.send();
  }
}
