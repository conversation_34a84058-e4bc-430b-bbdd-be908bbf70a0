import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  recurrence_id: number;
  pattern?: string;
  nextGenDate?: Date;
  untilDate?: Date;
}

@injectable()
export class UpdateRecurrenceUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository
  ) {}

  async execute({
    recurrence_id,
    pattern,
    nextGenDate,
    untilDate,
  }: IRequest): Promise<void> {
    const recurrence = await this.sessionRepository.findRecurrence(
      recurrence_id
    );

    if (!recurrence) {
      throw new AppError("Recurrence not found");
    }

    await this.sessionRepository.updateRecurrence({
      recurrence_id,
      pattern,
      nextGenDate,
      untilDate,
    });
  }
}
