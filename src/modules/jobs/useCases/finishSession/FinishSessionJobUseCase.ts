/* eslint-disable no-await-in-loop */
import { inject, injectable } from "tsyringe";

import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  session_id: number;
  updateOwner: boolean;
}

@injectable()
export class FinishSessionJobUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async execute({ session_id, updateOwner }: IRequest): Promise<void> {
    const session = await this.sessionRepository.findById(session_id);

    if (!session) {
      throw new AppError("Session not found");
    }

    if (!session.isActive) {
      throw new AppError("Session already finished");
    }

    await this.sessionRepository.finishSession(session_id);

    if (updateOwner) {
      const statements = await this.playerRepository.findStatementByPlayer(
        session.player_id,
        "createdAt"
      );

      const seatsToFinish = session.players_seats.filter(
        (seat) => !seat.isFinished && !seat.hasDispute
      );

      // eslint-disable-next-line no-restricted-syntax
      for (const seat of seatsToFinish) {
        const [statementsToDelete] = statements.filter(
          (statement) =>
            statement.session_id === session_id &&
            statement.type === "pending" &&
            statement.buyer_id === seat.player_id
        );

        await this.playerRepository.deleteStatementById(statementsToDelete.id);

        const incomeValue = statementsToDelete.quantity;

        const player = await this.playerRepository.findById(seat.player_id);

        await this.playerRepository.saveStatement(
          "Venda de sessão",
          `${player.nickname}`,
          incomeValue,
          "income",
          session.player_id,
          session.id
        );

        await this.playerRepository.updateWallet(
          session.player_id,
          "income",
          incomeValue
        );
      }
    }
  }
}
