import { Request, Response } from "express";
import { container } from "tsyringe";

import { FinishSessionJobUseCase } from "./FinishSessionJobUseCase";

export class FinishSessionJobController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { session_id } = request.params;
    const { updateOwner } = request.body;

    const finishSessionJobUseCase = container.resolve(FinishSessionJobUseCase);

    await finishSessionJobUseCase.execute({
      session_id: Number(session_id),
      updateOwner,
    });

    return response.send();
  }
}
