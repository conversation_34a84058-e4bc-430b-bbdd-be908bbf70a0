import { inject, injectable } from "tsyringe";

import { IJobsRepository } from "@modules/jobs/repositories/IJobsRepository";
import { Jobs } from "@prisma/client";

@injectable()
export class ListAllJobsUseCase {
  constructor(
    @inject("JobsRepository") private jobsRepository: IJobsRepository
  ) {}

  async execute(): Promise<Jobs[]> {
    const jobs = await this.jobsRepository.findAll();

    return jobs;
  }
}
