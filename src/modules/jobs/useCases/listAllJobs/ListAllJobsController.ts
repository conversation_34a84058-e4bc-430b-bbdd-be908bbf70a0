import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListAllJobsUseCase } from "./ListAllJobsUseCase";

export class ListAllJobsController {
  async handle(request: Request, response: Response): Promise<Response> {
    const listAllJobsUseCase = container.resolve(ListAllJobsUseCase);

    const jobs = await listAllJobsUseCase.execute();

    return response.json(jobs);
  }
}
