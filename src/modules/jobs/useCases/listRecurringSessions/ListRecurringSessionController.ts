import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListRecurringSessionUseCase } from "./ListRecurringSessionUseCase";

export class ListRecurringSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const listRecurringSessionUseCase = container.resolve(
      ListRecurringSessionUseCase
    );

    const recurrences = await listRecurringSessionUseCase.execute();

    return response.json(recurrences);
  }
}
