import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";

@injectable()
export class ListRecurringSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository
  ) {}

  async execute(): Promise<any> {
    const recurrences = await this.sessionRepository.listRecurringSessions();

    return recurrences;
  }
}
