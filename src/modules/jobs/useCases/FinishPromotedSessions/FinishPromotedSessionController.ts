import { Request, Response } from "express";
import { container } from "tsyringe";

import { FinishPromotedSessionUseCase } from "./FinishPromotedSessionUseCase";

export class FinishPromotedSessionController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { promoted_session_id } = request.params;

    const finishPromotedSessionUseCase = container.resolve(
      FinishPromotedSessionUseCase
    );

    await finishPromotedSessionUseCase.execute({
      promoted_session_id: Number(promoted_session_id),
    });

    return response.json({ message: "Promoted session finished" });
  }
}
