import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";
import { AppError } from "@shared/errors/AppError";

interface IRequest {
  promoted_session_id: number;
}

@injectable()
export class FinishPromotedSessionUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository
  ) {}

  async execute({ promoted_session_id }: IRequest): Promise<void> {
    const promoted_session =
      await this.sessionRepository.findPromotedSessionById(promoted_session_id);

    if (!promoted_session) {
      throw new AppError("Promoted session not found");
    }

    await this.sessionRepository.updatePromotedSession({
      promoted_session_id,
      active: false,
    });
  }
}
