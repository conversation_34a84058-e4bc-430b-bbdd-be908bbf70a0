import { Game, Player, Badge, Type } from ".prisma/client";
import { ICreateGame } from "../dtos/ICreateGame";
import { IGameStatsByGame } from "../dtos/IGameStatsByGame";
import { IUpdateGame } from "../dtos/IUpdateGame";
import { ICreateStatsByGame } from "../dtos/ICreateStatsByGame";

export interface IGamesRepository {
  create(data: ICreateGame, game_stats:ICreateStatsByGame[]): Promise<any>;
  update(data: IUpdateGame): Promise<Game>;
  listAllActives(): Promise<Game[]>;
  rankingMentoringGames(): Promise<Game[]>;
  findById(id: number): Promise<Game>;
  findByName(name: string): Promise<Game>;
  listProPlayersByGame(game_id: number): Promise<
    (Player & {
      badges: Badge[];
    })[]
  >;
  listGameStatsByGameId(game_id: number): Promise<IGameStatsByGame[]>;
  listTypesGames(): Promise<Type[]>;
}
