import { inject, injectable } from "tsyringe";
import { IGameDTO } from "@modules/games/dtos/IGameDTO";
import { GamesMap } from "@modules/games/mapper/gamesMap";
import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";
import { IStorageProvider } from "@shared/container/providers/StorageProvider/IStorageProvider";
import { AppError } from "@shared/errors/AppError";
import { IUpdateGame } from "@modules/games/dtos/IUpdateGame";

@injectable()
export class UpdateGameUseCase {
  constructor(
    @inject("GamesRepository") private gamesRepository: IGamesRepository,
    @inject("StorageProvider") private storageProvider: IStorageProvider
  ) {}

  async execute({
    id,
    banner,
    logo,
    name,
    isActive,
    types,
  }: IUpdateGame): Promise<IGameDTO> {
    const existGame = await this.gamesRepository.findByName(name);

    if (existGame && existGame.id !== id) {
      throw new AppError("There is already a game with that name");
    }

    if (banner) {
      await this.storageProvider.save(banner, "games/banner");
    }
    if (logo) {
      await this.storageProvider.save(logo, "games/logo");
    }

    const game = await this.gamesRepository.update({
      id,
      banner,
      logo,
      name,
      isActive,
      types,
    });

    return GamesMap.toDTO(game);
  }
}
