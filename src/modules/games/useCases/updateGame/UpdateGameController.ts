import { Request, Response } from "express";
import { container } from "tsyringe";

import { resizeImage } from "@utils/resizeImage";

import { UpdateGameUseCase } from "./UpdateGameUseCase";

export class UpdateGameController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { id } = request.params;
    const files = request.files as {
      [fieldname: string]: Express.Multer.File[];
    };
    const { name, isActive, types } = request.body;
    const updateGameUseCase = container.resolve(UpdateGameUseCase);

    let banner;
    if (files?.banner) {
      const tmpBanner = files?.banner[0];
      banner = await resizeImage(tmpBanner.path, tmpBanner.destination);
    }

    let logo;
    if (files?.logo) {
      const tmpLogo = files?.logo[0];
      logo = await resizeImage(tmpLogo.path, tmpLogo.destination);
    }

    const game = await updateGameUseCase.execute({
      id: Number(id),
      banner,
      logo,
      name,
      isActive,
      types,
    });

    return response.json(game);
  }
}
