import { inject, injectable } from "tsyringe";

import { IGameDTO } from "@modules/games/dtos/IGameDTO";
import { GamesMap } from "@modules/games/mapper/gamesMap";
import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";

@injectable()
export class ListAllGamesUseCase {
  constructor(
    @inject("GamesRepository") private gamesRepository: IGamesRepository
  ) {}

  async execute(): Promise<IGameDTO[]> {
    const games = await this.gamesRepository.listAllActives();

    const gamesDTO = games.map((game) => GamesMap.toDTO(game));

    return gamesDTO;
  }
}
