import request from "supertest";

import { app } from "@shared/infra/http/app";

import { PrismaClient } from ".prisma/client";

const prisma = new PrismaClient();

describe("List all games Controller", () => {
  beforeAll(async () => {
    await prisma.game.createMany({
      data: [
        {
          name: "Super Mario",
          banner: "https://www.mario.com/assets/img/mario-bros-logo.png",
          logo: "https://www.mario.com/assets/img/mario-bros-logo.png",
        },
        {
          name: "<PERSON><PERSON><PERSON>",
          banner: "https://www.zelda.com/assets/img/zelda-logo.png",
          logo: "https://www.zelda.com/assets/img/zelda-logo.png",
        },
        {
          name: "Metroid",
          banner: "https://www.metroid.com/assets/img/metroid-logo.png",
          logo: "https://www.metroid.com/assets/img/metroid-logo.png",
        },
        {
          name: "<PERSON><PERSON><PERSON>",
          banner: "https://www.pokemon.com/assets/img/pokemon-logo.png",
          logo: "https://www.pokemon.com/assets/img/pokemon-logo.png",
        },
      ],
    });
  });

  afterAll(async () => {
    const token = prisma.player_token.deleteMany();
    const wallet = prisma.wallet.deleteMany();
    const player = prisma.player.deleteMany();
    const game = prisma.game.deleteMany();

    await prisma.$transaction([token, wallet, player, game]);
    await prisma.$disconnect();
  });

  it("should be able to list all games", async () => {
    const response = await request(app).get("/games/").send();

    expect(response.status).toBe(200);
    expect(response.body).toHaveLength(4);
  });
});
