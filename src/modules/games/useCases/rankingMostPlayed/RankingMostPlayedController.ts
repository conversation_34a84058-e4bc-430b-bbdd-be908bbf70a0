import { Request, Response } from "express";
import { container } from "tsyringe";

import { RankingMostPlayedUseCase } from "./RankingMostPlayedUseCase";

export class RankingMostPlayedController {
  async handle(request: Request, response: Response): Promise<Response> {
    const rankingMostPlayedUseCase = container.resolve(
      RankingMostPlayedUseCase
    );

    const ranking = await rankingMostPlayedUseCase.execute();

    return response.json(ranking);
  }
}
