import { inject, injectable } from "tsyringe";

import { ISessionRepository } from "@modules/sessions/repositories/ISessionRepository";

@injectable()
export class RankingMostPlayedUseCase {
  constructor(
    @inject("SessionRepository") private sessionRepository: ISessionRepository
  ) {}

  async execute(): Promise<any> {
    const sessions = await this.sessionRepository.listAll();

    const ranking = sessions.reduce((acc, session) => {
      const { name: Game } = session.Game;

      if (!acc[Game]) {
        acc[Game] = {
          quantity: 0,
          logo: session.Game.logo,
          banner: session.Game.banner,
        };
      }

      acc[Game].quantity += 1;

      return acc;
    }, {});

    const arrRanking = Object.keys(ranking).map((key) => {
      // url aws sem a /
      return {
        game: key,
        quantity: ranking[key].quantity,
        logo: encodeURI(
          `${process.env.AWS_BUCKET_URL}/games/logo/${ranking[key].logo}`
        ),
        banner: encodeURI(
          `${process.env.AWS_BUCKET_URL}/games/banner/${ranking[key].banner}`
        ),
      };
    });

    arrRanking
      .sort((a, b) => b.quantity - a.quantity)
      .map((game, index) => {
        Object.assign(game, {
          position: index + 1,
        });
      });

    return arrRanking;
  }
}
