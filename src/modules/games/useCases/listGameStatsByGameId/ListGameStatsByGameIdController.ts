import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListGameStatsByGameIdUseCase } from "./ListGameStatsByGameIdUseCase";

export class ListGameStatsByGameIdController {
  async handle(request: Request, response: Response): Promise<Response> {        
    const { game_id }= request.params;    

    const listGameStatsByGameIdUseCase = container.resolve(ListGameStatsByGameIdUseCase);
    
    const list_game_stats = await listGameStatsByGameIdUseCase.execute(Number(game_id));

    return response.json(list_game_stats);
  }
}
