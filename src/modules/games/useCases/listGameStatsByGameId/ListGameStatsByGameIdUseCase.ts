import { inject, injectable } from "tsyringe";
import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";
import { IGameStatsByGame } from "@modules/games/dtos/IGameStatsByGame";

@injectable()
export class ListGameStatsByGameIdUseCase {
  constructor(
    @inject("GamesRepository") private gamesRepository: IGamesRepository
  ) {}

  async execute(game_id: number): Promise<IGameStatsByGame[]> {

    return await this.gamesRepository.listGameStatsByGameId(game_id);
  }
}
