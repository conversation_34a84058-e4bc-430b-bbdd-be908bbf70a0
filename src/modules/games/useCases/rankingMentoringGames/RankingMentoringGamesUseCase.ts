import { inject, injectable, container } from "tsyringe";
import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";


@injectable()
export class RankingMentoringGamesUseCase {
  constructor(@inject("GamesRepository") private gamesRepository: IGamesRepository) {}
  async execute(): Promise<any> {
      const allGames = await this.gamesRepository.rankingMentoringGames();
      
  
      
      return allGames;
  }
}
