import { Request, Response } from "express";
import { container } from "tsyringe";

import { RankingMentoringGamesUseCase } from "./RankingMentoringGamesUseCase";

export class RankingMentoringGamesController {
  async handle(request: Request, response: Response): Promise<Response> {
    
    const rankingMentoringGamesUseCase = container.resolve(
      RankingMentoringGamesUseCase
    );
    const allGames = await rankingMentoringGamesUseCase.execute();

    return response.json(allGames);
  }
}
