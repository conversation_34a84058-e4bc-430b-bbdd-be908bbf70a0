import { Request, Response } from "express";
import { container } from "tsyringe";

import { RankingByGamesUseCase } from "./RankingByGamesUseCase";

export class RankingByGamesController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { game_id } = request.params;
    const { order, player } = request.query;

    const rankingByGamesUseCase = container.resolve(RankingByGamesUseCase);

    const ranking = await rankingByGamesUseCase.execute({
      game_id: Number(game_id),
      order: String(order),
      player: player && String(player),
    });

    return response.json(ranking);
  }
}
