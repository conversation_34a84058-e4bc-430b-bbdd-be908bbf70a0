import { inject, injectable } from "tsyringe";

import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";
import { TPlayerStats } from "@modules/players/dtos/IPlayerDTO";
import { IPlayerRepository } from "@modules/players/repositories/IPlayerRepository";
import { AppError } from "@shared/errors/AppError";

import { Player, Badge } from ".prisma/client";

interface IRequest {
  game_id: number;
  order: string;
  player: string;
}

type IPlayer = Pick<Player, "id" | "nickname" | "avatar"> & {
  position?: number;
  avaliation?: number;
  player_stats?: TPlayerStats[];
  badges?: Badge[];
};

type TPlayerResponse = Omit<IPlayer, "player_stats"> & {
  player_stats: {
    name: string;
    value: number;
  }[];
};
interface IResponse {
  players: TPlayerResponse[];
}
@injectable()
export class RankingByGamesUseCase {
  constructor(
    @inject("GamesRepository") private gamesRepository: IGamesRepository,
    @inject("PlayerRepository") private playerRepository: IPlayerRepository
  ) {}

  async addPlayerAvaliationByGame(player: IPlayer, game_id: number) {
    const avaliations = await this.playerRepository.findAvaliationsByGame(
      player.id,
      game_id
    );

    let average = 5;

    if (avaliations.length > 0) {
      average =
        avaliations.reduce((acc, curr) => {
          return acc + curr.points;
        }, 5) /
        (avaliations.length + 1);
    }

    Object.assign(player, {
      avaliation: average,
    });

    return player;
  }

  async execute({ game_id, order, player }: IRequest): Promise<IResponse> {
    const game = await this.gamesRepository.findById(game_id);

    if (!game) {
      throw new AppError("Game not found");
    }

    let ranking: IPlayer[] = await this.gamesRepository.listProPlayersByGame(
      game_id
    );

    if (ranking.length === 0) {
      return { players: [] };
    }

    ranking = await Promise.all(
      ranking.map(async (player) => {
        return this.addPlayerAvaliationByGame(player, game_id);
      })
    );

    await Promise.all(
      ranking.map(async (player) => {
        player.player_stats = await this.playerRepository.findPlayerStatsByGame(
          player.id,
          game_id
        );
      })
    );

    switch (order) {
      case "avaliation":
        ranking = ranking.sort((a, b) => {
          return b.avaliation - a.avaliation;
        });
        break;
      case "average":
        ranking = ranking.sort((a, b) => {
          const a_average =
            a.player_stats.reduce((acc, curr) => {
              return acc + curr.value;
            }, 0) / a.player_stats.length;

          const b_average =
            b.player_stats.reduce((acc, curr) => {
              return acc + curr.value;
            }, 0) / b.player_stats.length;

          return b_average - a_average;
        });
        break;
      case ranking[0].player_stats[0]?.Game_stats.name:
        ranking = ranking.sort((a, b) => {
          return b.player_stats[0].value - a.player_stats[0].value;
        });
        break;
      case ranking[0].player_stats[1]?.Game_stats.name:
        ranking = ranking.sort((a, b) => {
          return b.player_stats[1].value - a.player_stats[1].value;
        });
        break;
      case ranking[0].player_stats[2]?.Game_stats.name:
        ranking = ranking.sort((a, b) => {
          return b.player_stats[2].value - a.player_stats[2].value;
        });
        break;
      default:
        ranking = ranking.sort((a, b) => b.avaliation - a.avaliation);
    }

    ranking.map((player, index) => {
      Object.assign(player, {
        position: index + 1,
      });
    });

    if (player) {
      ranking = ranking.filter((p) =>
        p.nickname.toLowerCase().includes(player.toLowerCase())
      );
    }

    return {
      players: ranking.map((player) => {
        return {
          id: player.id,
          position: player.position,
          nickname: player.nickname,
          avatar: player.avatar
            ? encodeURI(`${process.env.AWS_BUCKET_URL}/avatar/${player.avatar}`)
            : null,
          badges: player.badges
            .map((badge) => {
              return {
                id: badge.id,
                name: badge.name,
                icon: encodeURI(
                  `${process.env.AWS_BUCKET_URL}/badges/${badge.icon}`
                ),
                description: badge.description,
              };
            })
            .filter((badge) => badge.name !== "Jogador Pro"),
          avaliation: player.avaliation,
          player_stats: player.player_stats.map((stat) => {
            return {
              name: stat?.Game_stats.name,
              slug: stat?.Game_stats.slug,
              value: stat.value,
            };
          }),
        };
      }),
    };
  }
}
