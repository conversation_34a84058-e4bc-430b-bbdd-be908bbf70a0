import { inject, injectable, container } from "tsyringe";
import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";
import { GamesRepository } from "@modules/games/infra/prisma/repositories/GamesRepository"
container.registerSingleton('GamesRepository', GamesRepository);
@injectable()
export class ListTypeGamesUseCase {
  constructor(@inject("GamesRepository") private gamesRepository: IGamesRepository) {}
  async execute(): Promise<any> {
      const allTypesGames = await this.gamesRepository.listTypesGames();
      return allTypesGames;
  }
}
