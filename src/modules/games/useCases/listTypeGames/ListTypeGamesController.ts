import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListTypeGamesUseCase } from "./ListTypeGamesUseCase";

export class ListTypeGamesController {
  async handle(request: Request, response: Response): Promise<Response> {
    
    const listTypeGamesUseCase = container.resolve(
      ListTypeGamesUseCase
    );
    const allTypesGames = await listTypeGamesUseCase.execute();

    return response.json(allTypesGames);
  }
}
