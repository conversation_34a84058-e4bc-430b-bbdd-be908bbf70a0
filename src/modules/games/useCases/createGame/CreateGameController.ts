import { Request, Response } from "express";
import { container } from "tsyringe";

import { resizeImage } from "@utils/resizeImage";

import { CreateGameUseCase } from "./CreateGameUseCase";

export class CreateGameController {
  async handle(request: Request, response: Response): Promise<Response> {
    const { name, types, game_stats } = request.body;
    const files = request.files as {
      [fieldname: string]: Express.Multer.File[];
    };

    const createGameUseCase = container.resolve(CreateGameUseCase);
    let banner;
    if (files?.banner) {
      const tmpBanner = files?.banner[0];
      banner = await resizeImage(tmpBanner.path, tmpBanner.destination);
    }
    let logo;
    if (files?.logo) {
      const tmpLogo = files?.logo[0];
      logo = await resizeImage(tmpLogo.path, tmpLogo.destination);
    }

    const game = await createGameUseCase.execute({ name, banner, logo, types }, game_stats);

    return response.json(game);
  }
}
