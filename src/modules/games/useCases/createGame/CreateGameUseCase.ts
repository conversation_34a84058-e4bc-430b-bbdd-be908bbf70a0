import { inject, injectable } from "tsyringe";

import { ICreateGame } from "@modules/games/dtos/ICreateGame";
import { IGameDTO } from "@modules/games/dtos/IGameDTO";
import { GamesMap } from "@modules/games/mapper/gamesMap";
import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";
import { IStorageProvider } from "@shared/container/providers/StorageProvider/IStorageProvider";
import { AppError } from "@shared/errors/AppError";
import { log } from "handlebars/runtime";
import { ICreateStatsByGame } from "@modules/games/dtos/ICreateStatsByGame";

@injectable()
export class CreateGameUseCase {
  constructor(
    @inject("GamesRepository") private gamesRepository: IGamesRepository,
    @inject("StorageProvider") private storageProvider: IStorageProvider
  ) {}

  async execute({ name, banner, logo, types}: ICreateGame, game_stats:ICreateStatsByGame[]): Promise<IGameDTO> {
    const existGame = await this.gamesRepository.findByName(name);

    if (existGame) {
      throw new AppError("Game already exists!");
    }

    await this.storageProvider.save(banner, "games/banner");
    await this.storageProvider.save(logo, "games/logo");

    const game = await this.gamesRepository.create({ name, banner, logo, types}, game_stats);
    
    return GamesMap.toDTO(game);
  }
}
