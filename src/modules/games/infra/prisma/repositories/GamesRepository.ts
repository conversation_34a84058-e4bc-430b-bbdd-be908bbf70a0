import { inject, injectable } from "tsyringe";
import { ICreateGame } from "@modules/games/dtos/ICreateGame";
import { IGamesRepository } from "@modules/games/repositories/IGamesRepository";

import { Game, Player, PrismaClient, Badge, Type } from ".prisma/client";
import { IGameStatsByGame } from "@modules/games/dtos/IGameStatsByGame";
import { IUpdateGame } from "@modules/games/dtos/IUpdateGame";
import { Game_stats } from "@prisma/client";
import { ICreateStatsByGame } from "@modules/games/dtos/ICreateStatsByGame";

@injectable()
export class GamesRepository implements IGamesRepository {
  constructor(@inject("PrismaClient") private prisma: PrismaClient) {}

  async listAllActives(): Promise<Game[]> {
    const games = await this.prisma.game.findMany({
      where: {
        isActive: true,
      },
      include:{
        Type: true,
        game_stats: true,
      }
    });

    return games;
  }

  async rankingMentoringGames(): Promise<Game[]> {
    const games = await this.prisma.game.findMany({
      where: {
        isActive: true,
      },
      include: {
        Mentoring_games: true
      },
      orderBy: {
        Mentoring_games: {
          _count: 'desc',
        },
      },
    });
  
    return games.map((game) => ({
      ...game,
      Mentoring_games: game.Mentoring_games.length,
    }));
  }
  

  async findById(id: number): Promise<Game> {
    const game = await this.prisma.game.findUnique({
      where: {
        id,
      },
    });

    return game;
  }

  async findByName(name: string): Promise<Game> {
    const game = await this.prisma.game.findFirst({
      where: {
        name,
      },
    });

    return game;
  }

  async create({ name, logo, banner, types }: ICreateGame, game_stats: ICreateStatsByGame[]): Promise<any> {
    const typeIds = types.split(',').map(type => parseInt(type.trim(), 10));
    const newGame = await this.prisma.$transaction(async (tx) => {
      const game = await tx.game.create({
        data: {
          name,
          logo,
          banner,
          Type: {
            connect: typeIds.map(id => ({ id })),
          },
        },
      });
        for (let i = 0; i < game_stats.length; i++) {
          game_stats[i].game_id = game.id;
          await tx.game_stats.create({
            data: {
              name: game_stats[i].name,
              game_id: game_stats[i].game_id,
            },
          });
        }
      return game;
    });
    return newGame;
  }
 


  async update({ id, name, logo, banner, isActive, types }: IUpdateGame): Promise<Game> {
    const typeIds = types.split(',').map(type => parseInt(type.trim(), 10));
    const isActiveBoolean = isActive.toLowerCase() === 'true';
    const game = await this.prisma.game.update({
      where: { id },
      data: {
        name,
        logo,
        banner,
        isActive: isActiveBoolean,
        Type: {
          set:typeIds.map(id => ({ id })),
        }
      },
    });

    return game;
  }

  async listProPlayersByGame(game_id: number): Promise<
    (Player & {
      badges: Badge[];
    })[]
  > {
    const mentoring_games = await this.prisma.mentoring_games.findMany({
      select: {
        Player: {
          include: {
            badges: true,
          },
        },
      },
      where: {
        Game: {
          id: game_id,
        },
      },
    });

    return mentoring_games.map((mentoring) => mentoring.Player);
  }

  async listGameStatsByGameId(game_id: number): Promise<IGameStatsByGame[]> {
    const game_stats = await this.prisma.game_stats.findMany({
      where: {
        game_id
      },
    });

    return game_stats;
  }
  async listTypesGames(): Promise<Type[]> {
    const typeGames = await this.prisma.type.findMany();
    return typeGames;
  }
}
