import { Game } from ".prisma/client";
import { IGameDTO, IIdTypesGameForCreateUpdate } from "../dtos/IGameDTO";

export class GamesMap {
  public static toDTO(game: Game): IGameDTO {
    if (game.banner) {
      const bannerUrl =
        process.env.disk === "local"
          ? `${process.env.APP_API_URL}/games/banner/${game.banner}`
          : `${process.env.AWS_BUCKET_URL}/games/banner/${game.banner}`;

      game.banner = encodeURI(bannerUrl);
    }

    if (game.logo) {
      const logoUrl =
        process.env.disk === "local"
          ? `${process.env.APP_API_URL}/games/logo/${game.logo}`
          : `${process.env.AWS_BUCKET_URL}/games/logo/${game.logo}`;

      game.logo = encodeURI(logoUrl);
    }
    return {
      id: game.id,
      name: game.name,
      banner: game.banner,
      logo: game.logo,
      types: game.Type,
      game_stats: game.game_stats,
    };
  }
}
