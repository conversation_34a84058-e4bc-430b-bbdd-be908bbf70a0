import request from "supertest";

import { app } from "@shared/infra/http/app";

import { PrismaClient } from ".prisma/client";

const prisma = new PrismaClient();

describe("List all inputs Controller", () => {
    it("should be able to list all players", async () => {
        const response = await request(app).get("/input").send();

        expect(response.status).toBe(200);
        // expect(response.body).toHaveLength(2);
    });
});