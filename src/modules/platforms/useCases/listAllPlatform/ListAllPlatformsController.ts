import { Request, Response } from "express";
import { container } from "tsyringe";

import { ListAllPlatformsUseCase } from "./ListAllPlatformsUseCase";

export class ListAllPlatformsController {
    async handle(request: Request, response: Response): Promise<Response> {
        
        const listAllPlatformsUseCase = container.resolve(ListAllPlatformsUseCase);
        
        const platforms = await listAllPlatformsUseCase.execute();

        return response.json(platforms);
    }
}