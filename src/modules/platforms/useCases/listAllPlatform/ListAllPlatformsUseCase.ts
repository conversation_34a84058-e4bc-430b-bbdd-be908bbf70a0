import { inject, injectable } from "tsyringe";

import { IPlatformDTO } from "@modules/platforms/dtos/IPlatformsDTO";
import { IPlatformRepository } from "@modules/platforms/repositories/IPlatformRepository";

@injectable()
export class ListAllPlatformsUseCase {
  constructor(
    @inject("PlatformRepository") private platformRepository: IPlatformRepository
  ) {}

  async execute(): Promise<IPlatformDTO[]> {

    const inputs = await this.platformRepository.listAll();

    return inputs;
  }
}
