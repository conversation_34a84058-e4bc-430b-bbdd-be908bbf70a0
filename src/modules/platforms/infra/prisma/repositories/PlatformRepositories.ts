import { inject, injectable } from "tsyringe";

import { IPlatformRepository } from "@modules/platforms/repositories/IPlatformRepository";


import {
    Platform
} from ".prisma/client";
import { PrismaClient } from "@prisma/client";

@injectable()
export class PlatformRepository implements IPlatformRepository {
    constructor(@inject("PrismaClient") private prisma: PrismaClient) {}

    async listAll(): Promise<Platform[]> {
        const platforms = await this.prisma.platform.findMany()

        return platforms;
    }

}
